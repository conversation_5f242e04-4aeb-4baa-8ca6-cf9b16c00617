/**
 * React Native Camera App with Heavy UI and Comprehensive Logging
 * Enhanced with Redux, Performance Monitoring, and Lifecycle Tracking
 *
 * @format
 */

import React, { useRef, useEffect, useState } from 'react';
import type { PropsWithChildren } from 'react';
import {
  DeviceEventEmitter,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Button,
  Text,
  useColorScheme,
  View,
  Alert,
  TextInput,
  AppState,
  Dimensions
} from 'react-native';

// Redux imports
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './src/store';

// Service imports
import LoggerService from './src/services/LoggerService';
import PerformanceMonitor from './src/utils/PerformanceMonitor';
import AssetManager from './src/utils/AssetManager';

// Component imports
import AppStateMonitor from './src/components/AppStateMonitor';
import CameraScreen from './src/screens/CameraScreen';

// Model imports
import { LogLevel, LogCategory } from './src/models/LogEntry';

import {
  Colors,
} from 'react-native/Libraries/NewAppScreen';

//@ts-ignore
import { VGuardPlugin } from 'react-native-vguard';

type SectionProps = PropsWithChildren<{
  title: string;
}>;

function Section({ children, title }: SectionProps): JSX.Element {

  return (
    <View style={styles.sectionContainer}>
      <Text
        style={[
          styles.ButtonStyle,
        ]}>
        {children}
      </Text>
    </View>
  );
}

// Get screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Main App Component with Enhanced Logging
function EnhancedApp(): JSX.Element {
  const [logs, setLogs] = useState('');
  const [logItem, setLogItem] = useState('');
  const [payload, onChangePayload] = useState('message to sign');
  const [isInitialized, setIsInitialized] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState(null);

  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const initializationTime = useRef(Date.now());

  const getData = async () => {
    try {
      // Construct the API URL
      const url = `https://api.restful-api.dev/objects`;
  
      // Fetch the data
      const response = await fetch(url);
  
      // Check if the response status is OK (200 range)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
  
      // Parse the JSON response
      const result = await response.json();
  
      // Introduce a 30-second delay
      await new Promise(resolve => setTimeout(resolve, 30000));
  
      // Return the data
      return {
        result
      };
    } catch (error) {
      // Handle errors (e.g., network issues or invalid city)
      console.error('Error fetching data:', error);
      return null;
    }
  };

  // Initialize services
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize Logger Service
        await LoggerService.initialize();

        // Start Performance Monitoring
        PerformanceMonitor.startMonitoring();

        // Generate dummy assets for size requirement
        await AssetManager.generateDummyAssets();

        // Log app initialization
        LoggerService.log(
          LogLevel.LIFECYCLE,
          LogCategory.APP_LIFECYCLE,
          'App',
          'Application initialized successfully',
          {
            initializationTime: Date.now() - initializationTime.current,
            screenDimensions: { width: screenWidth, height: screenHeight },
            platform: Platform.OS,
            platformVersion: Platform.Version
          }
        );

        setIsInitialized(true);

      } catch (error) {
        LoggerService.error('App', 'Failed to initialize application', error);
        console.error('App initialization failed:', error);
      }
    };

    initializeApp();

    // Cleanup on unmount
    return () => {
      PerformanceMonitor.stopMonitoring();
      LoggerService.cleanup();
    };
  }, []);

  // Performance metrics monitoring
  useEffect(() => {
    if (!isInitialized) return;

    const updatePerformanceMetrics = () => {
      const metrics = PerformanceMonitor.getCurrentMetrics();
      setPerformanceMetrics(metrics);

      // Log performance summary every 60 seconds
      LoggerService.log(
        LogLevel.PERFORMANCE,
        LogCategory.PERFORMANCE_METRICS,
        'App',
        'Performance metrics update',
        metrics
      );
    };

    const performanceInterval = setInterval(updatePerformanceMetrics, 60000);
    updatePerformanceMetrics(); // Initial update

    return () => clearInterval(performanceInterval);
  }, [isInitialized]);

  const printLogs = (log: string) => {
    setLogItem(log);

    // Enhanced logging with LoggerService
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.UI_INTERACTIONS,
      'VGuard',
      log,
      { source: 'VGuard', originalLog: log }
    );
  }

  useEffect(() => {
    // This effect will run after each logItem update
    const value = logs + "\n\n" + logItem;
    setLogs(value);

    // Log to enhanced logger
    if (logItem) {
      LoggerService.debug('App', 'Log item updated', { logItem, totalLogs: logs.length });
    }
  }, [logItem]);

  useEffect(() => {
    if (!isInitialized) return;

    DeviceEventEmitter.addListener(VGuardPlugin.VGUARD_EVENTS, onVGuardEvents);
    startVGuard();

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.APP_LIFECYCLE,
      'VGuard',
      'VGuard event listeners initialized'
    );

    return () => {
      // Remove the event listener before the component is destroyed.
      DeviceEventEmitter.removeAllListeners(VGuardPlugin.VGUARD_EVENTS);

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.APP_LIFECYCLE,
        'VGuard',
        'VGuard event listeners cleaned up'
      );
    };
  }, [isInitialized]);

  useEffect(() => { // Enhanced app state monitoring with logging
    const subscription = AppState.addEventListener("change", nextAppState => {
      const previousState = appState.current;

      LoggerService.log(
        LogLevel.LIFECYCLE,
        LogCategory.APP_LIFECYCLE,
        'App',
        `App state changed: ${previousState} → ${nextAppState}`,
        {
          previousState,
          currentState: nextAppState,
          timestamp: new Date().toISOString(),
          performanceMetrics
        }
      );

      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        LoggerService.log(
          LogLevel.INFO,
          LogCategory.APP_LIFECYCLE,
          'App',
          'App returned to foreground - restarting VGuard'
        );
        startVGuard();
      }

      appState.current = nextAppState;
      setAppStateVisible(appState.current);
    });

    return () => {
      subscription.remove();
    };
  }, [performanceMetrics]);

  const startVGuard = () => {
    printLogs('START VGUARD');
    // Set url of synchronizing the vos logs if enabled
    const tlaUrl = 'https://stg-cloud.v-key.com';
    if (tlaUrl) {
      VGuardPlugin.setLoggerBaseUrl(tlaUrl);
    }
    // Set TI Url if enabled
    const tiUrl = 'https://stg-cloud.v-key.com/';
    if (tiUrl) {
      VGuardPlugin.setThreatIntelligenceServerURL(tiUrl);
    }
    // 0: DEFAULT, 1: HIGHT
    VGuardPlugin.setMemoryConfiguration(1);
    // Enable the overlay detection
    if (Platform.OS === 'android') {
      VGuardPlugin.setOverlayDetectionEnabled(false);
    }

    // Set TA Alias
    let TA_alias = "ACB_Corp";// ACB
    // let TA_alias = "Momo";// momo
    // let TA_alias = "VDS";// VDS


    
    VGuardPlugin.setTaAlias(TA_alias);
    printLogs('setTaAlias ' + TA_alias);
    // Intialize Vguard
    VGuardPlugin.setupVGuard();
  };

  const onVGuardEvents = async (event: any) => {
    const action = event.action;
    printLogs('. event.action: ' + event.action);

    if (action == VGuardPlugin.ACTION_SCAN_COMPLETE) {
      const threats = event.data;
      if (threats != null && threats.length > 0) {
        for (let i = 0; i < threats.length; i++) {
          var threatInfo = threats[i];
          // printLogs(
          //   'Threat Info: ' +
          //     threatInfo.ThreatClass +
          //     ' - ' +
          //     threatInfo.ThreatName +
          //     ' - ' +
          //     threatInfo.ThreatInfo +
          //     ' - ' +
          //     threatInfo.ThreatPackageID,
          // );
        }
      } else {
        printLogs('Scan complete, no threats found!');
      }
    } else if (action == VGuardPlugin.VOS_READY) {
      printLogs('v-os return code: ' + event.data);

      const errorCode = event.data;
      if (errorCode == '-1039' || errorCode == '20050') {// iOS behavior
        // showWarningAlert('Simulator is detected: ' + errorCode)
      }

      const tid = await VGuardPlugin.getTroubleshootingId();
      printLogs('TID: ' + tid);

    } else if (action == VGuardPlugin.VGUARD_ERROR) {
      // Force synce to send TLA logs if vguard initialization fails
      // VGuardPlugin.forceSyncLogs(); // called automatically in Plugin already

      const errorCode = event.data;
      if (errorCode == '-1039' || errorCode == '20050') {// Android behavior
        showWarningAlert('Emulator is detected: ' + errorCode)
      } else {
        printLogs('Initialize V-Guard is failure caused: ' + errorCode);
      }

    } else if (action == VGuardPlugin.VGUARD_VIRTUAL_SPACE_DETECTED) { // This event is for Android only
      showWarningAlert('app is running on Virtual Space')
    } else if (action == VGuardPlugin.VGUARD_OVERLAY_DETECTED) { // This event is for Android only
      showWarningAlert('OVERLAY DETECTED!')
    } else if (action == VGuardPlugin.VGUARD_SCREEN_SHARING_DETECTED) {
      if (Platform.OS === 'android') {
        var _data = 'VGUARD_SCREEN_SHARING_DETECTED'
        const data = event.data;
        if (data != null && data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            _data += '\nScreen Name: ' + data[i];
          }
        }
        showWarningAlert('OVERLAY DETECTED!')
      } else if (Platform.OS === 'ios') {
        showWarningAlert('SCREEN_SHARING DETECTED!')
      }

    } else if (action == VGuardPlugin.VGUARD_HANDLE_THREAT_POLICY) {
      const data = event.data;
      // printLogs("data: " + data);
      // printLogs('highest_policy: ' + data.highest_policy);
      // printLogs('alertTitle: ' + data.alertTitle);
      // printLogs('alertMessage: ' + data.alertMessage);
      // printLogs('disabledAppExpired: ' + data.disabledAppExpired);
      
      const threats = data.threats;
      if (threats != null && threats.length > 0) {
        for (let i = 0; i < threats.length; i++) {
          var threatInfo = threats[i];
          // printLogs(
          //   'Threat Info: ' +
          //   threatInfo.ThreatClass +
          //   ' - ' +
          //   threatInfo.ThreatName +
          //   ' - ' +
          //   threatInfo.ThreatInfo +
          //   ' - ' +
          //   threatInfo.ThreatPackageID,
          // );
        }
      } else {
        printLogs('VGUARD_HANDLE_THREAT_POLICY, no threats found!');
      }
    } else {
      printLogs('. event.data: ' + event.data);
    }
  };

  const signMsg = async () => {
    printLogs('trigger cryptoTA.signMsg');
    printLogs("--------");
    printLogs("payload:\n" + payload);
    const signature = await VGuardPlugin.signMsg(payload)
    printLogs("==>");
    printLogs("signature:\n" + signature);
    printLogs("--------");
  }

  const signMsgChecksum = async () => {
    printLogs("trigger cryptoTA.signMsgWithChecksum")
    printLogs("--------");
    printLogs("payload:\n" + payload);

    const signatureData = await VGuardPlugin.signMsgChecksum(payload)
    printLogs("==>");
    if (signatureData) {
      // printLogs("signatureData:\n" + signatureData);

      //Handle signature data
      const signatureDataObject = JSON.parse(signatureData)
      const numberOfKeys = Object.keys(signatureDataObject).length
      printLogs("Number of keys: " + numberOfKeys)
      switch (numberOfKeys) {
        case 0: {//{}
          printLogs("JSONException happened!")
          break
        }
        case 1: {//{error:XXX}
          printLogs("Error: " + signatureDataObject.error)
          // get Pri error and Sec error

          break
        }
        case 2: {//{primaryError:XXX, SecondaryError:XXX}
          printLogs("primaryError: " + signatureDataObject.primaryError)
          printLogs("SecondaryError: " + signatureDataObject.SecondaryError)
          break
        }// new add by tu.do
        case 3: {//{signature:XXX, checksum:XXX, epoctime:XXX}
          //Get base64 encoded data
          const signature = signatureDataObject.signature
          printLogs("signature: " + signature)
          const checksum = signatureDataObject.checksum
          printLogs("checksum: " + checksum)
          const epoctime = signatureDataObject.epoctime
          printLogs("epoctime: " + epoctime)
          break
        }
        default: {//2
          printLogs("result signMsgWithChecksum: " + signatureDataObject)
          break
        }
      }
    }
    printLogs("--------");
  }

  const showWarningAlert = (message: string) => {
    printLogs(message);

    Alert.alert('Warning', message, [
      { text: 'OK', onPress: () => "" },
    ]);
  }

  const isDarkMode = useColorScheme() === 'dark';

  const SafeAreaViewStyle = {
    backgroundColor: Colors.white,
  };
  const scrollViewStyle = {
    height: 300,
    backgroundColor: 'lightgray',
    borderRadius: 5,
    margin: 10,
  };
  const logStyle = {
    color: 'black',
  };

  // Show loading screen while initializing
  if (!isInitialized) {
    return (
      <SafeAreaView style={[SafeAreaViewStyle, { justifyContent: 'center', alignItems: 'center' }]}>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <Text style={styles.loadingText}>Initializing Camera App...</Text>
        <Text style={styles.loadingSubtext}>Setting up logging and performance monitoring</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={SafeAreaViewStyle}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
      />

      {/* Performance Metrics Display */}
      {performanceMetrics && (
        <View style={styles.performanceContainer}>
          <Text style={styles.performanceText}>
            FPS: {performanceMetrics.fps.current} |
            Memory: {performanceMetrics.memory.current?.percentage.toFixed(1)}% |
            Session: {Math.floor((Date.now() - initializationTime.current) / 1000)}s
          </Text>
        </View>
      )}

      <View style={styles.sectionContainer}>
        <TextInput
          style={styles.input}
          placeholder="Type here to sign!"
          onChangeText={onChangePayload}
          value={payload}
        />
      </View>

      <Section title='VGuard Controls'>
        <Button
          onPress={() => {
            printLogs('trigger request scan');
            VGuardPlugin.requestScan();
          }}
          title="Scan"
          color="#841584" />
        <View style={{ padding: 2 }} />

        <Button
          onPress={() => {
            printLogs('trigger forceSyncLogs');
            VGuardPlugin.forceSyncLogs();
          }}
          title="Force Sync Logs"
          color="#841584" />
        <View style={{ padding: 2 }} />

        <Button
          onPress={() => {
            signMsg()
          }}
          title="Sign Message"
          color="#841584" />
        <View style={{ padding: 2 }} />

        <Button
          onPress={() => { signMsgChecksum() }}
          title="Sign with Checksum"
          color="#841584" />
        <View style={{ padding: 2 }} />

        <Button
          onPress={() => { VGuardPlugin.showPopupVkey('','', true, 5) }}
          title="Show Popup (Quit)"
          color="#841584" />
        <View style={{ padding: 2 }} />

        <Button
          onPress={() => { VGuardPlugin.showPopupVkey('', '', false, 5) }}
          title="Show Popup (No Quit)"
          color="#841584" />
      </Section>

      {/* Enhanced Logs Section */}
      <Section title="Application Logs" />
      <ScrollView style={scrollViewStyle}>
        <View style={styles.sectionContainer}>
          <Text style={logStyle}>{logs}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  sectionContainer: {
    marginTop: 10,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  ButtonStyle: {
    backgroundColor: Colors.lighter,
  },
  highlight: {
    fontWeight: '700',
  },
  input: {
    height: 40,
    width: 330,
    margin: 12,
    borderWidth: 1,
    padding: 10,
    backgroundColor: Colors.lighter,
    color: Colors.black
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.black,
    marginBottom: 10,
  },
  loadingSubtext: {
    fontSize: 14,
    color: Colors.dark,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  performanceContainer: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  performanceText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
});

// Main App Component with Redux Provider
function App(): JSX.Element {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <AppStateMonitor>
          <CameraScreen />
        </AppStateMonitor>
      </PersistGate>
    </Provider>
  );
}

export default App;