/**
 * Platform Integration Tests
 * End-to-end tests that verify platform-specific features work together correctly
 */

import { Platform } from 'react-native';
import PlatformTestUtils from '../platform/PlatformTestUtils';
import PlatformTestRunner from '../platform/PlatformTestRunner';
import CameraService from '../../src/services/CameraService';
import LocationService from '../../src/services/LocationService';
import DeviceInfoService from '../../src/services/DeviceInfoService';

describe('Platform Integration Tests', () => {
  let testRunner;
  let cameraService;
  let locationService;
  let deviceInfoService;

  beforeEach(() => {
    jest.clearAllMocks();
    testRunner = new PlatformTestRunner();
    cameraService = new CameraService();
    locationService = new LocationService();
    deviceInfoService = new DeviceInfoService();
  });

  describe('Service Integration with Platform Features', () => {
    test('should integrate CameraService with platform permissions', async () => {
      // Test camera service initialization with platform-specific permissions
      const permissions = PlatformTestUtils.getPlatformPermissions();
      
      // Mock successful permission flow
      const permissionResult = await PlatformTestUtils.testPermissionFlow(permissions.CAMERA);
      expect(permissionResult.granted).toBe(true);

      // Initialize camera service
      await cameraService.initialize();
      
      // Verify camera service is properly initialized
      expect(cameraService.isInitialized).toBe(true);
      expect(cameraService.permissionStatus).toBe('GRANTED');
    });

    test('should integrate LocationService with platform capabilities', async () => {
      // Test location service with device GPS capability
      const hasGPS = await PlatformTestUtils.hasGPSCapability();
      
      if (hasGPS) {
        const hasPermission = await locationService.requestLocationPermission();
        expect(hasPermission).toBe(true);

        // Test location retrieval
        const location = await locationService.getCurrentLocation();
        expect(location).toHaveProperty('latitude');
        expect(location).toHaveProperty('longitude');
      } else {
        console.log('GPS not available on this device/emulator');
      }
    });

    test('should integrate DeviceInfoService with platform detection', async () => {
      // Test device info service with platform-specific capabilities
      const deviceInfo = await deviceInfoService.getDeviceInfo();
      
      expect(deviceInfo.platform).toBe(Platform.OS);
      expect(deviceInfo).toHaveProperty('model');
      expect(deviceInfo).toHaveProperty('brand');
      
      // Verify platform-specific properties
      if (Platform.OS === 'ios') {
        expect(deviceInfo).toHaveProperty('hasNotch');
        expect(deviceInfo).toHaveProperty('hasDynamicIsland');
      } else {
        expect(deviceInfo).toHaveProperty('apiLevel');
        expect(deviceInfo).toHaveProperty('supportedAbis');
      }
    });
  });

  describe('Cross-Platform Feature Compatibility', () => {
    test('should handle permission flows consistently across platforms', async () => {
      const permissions = PlatformTestUtils.getPlatformPermissions();
      const testResults = [];

      // Test all platform permissions
      for (const [name, permission] of Object.entries(permissions)) {
        const result = await PlatformTestUtils.testPermissionFlow(permission);
        testResults.push({ name, ...result });
      }

      // Verify all permissions were tested
      expect(testResults.length).toBeGreaterThan(0);
      
      // Check that permission results have consistent structure
      testResults.forEach(result => {
        expect(result).toHaveProperty('name');
        expect(result).toHaveProperty('granted');
        expect(result).toHaveProperty('initialStatus');
        expect(result).toHaveProperty('finalStatus');
      });
    });

    test('should detect device capabilities consistently', async () => {
      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      // Verify common capabilities are present
      expect(capabilities).toHaveProperty('platform');
      expect(capabilities).toHaveProperty('isPhysical');
      expect(capabilities).toHaveProperty('hasCamera');
      expect(capabilities).toHaveProperty('hasGPS');
      expect(capabilities).toHaveProperty('screenDimensions');
      expect(capabilities).toHaveProperty('deviceInfo');

      // Verify platform-specific capabilities
      if (Platform.OS === 'ios') {
        expect(capabilities).toHaveProperty('ios');
        expect(capabilities.ios).toHaveProperty('hasNotch');
        expect(capabilities.ios).toHaveProperty('deviceType');
      } else {
        expect(capabilities).toHaveProperty('android');
        expect(capabilities.android).toHaveProperty('apiLevel');
        expect(capabilities.android).toHaveProperty('supportedAbis');
      }
    });

    test('should validate native module availability across platforms', () => {
      const requiredModules = [
        'VGuardPlugin',
        'RNDeviceInfo',
        'RNPermissions',
        'RNCAsyncStorage',
      ];

      const moduleResults = requiredModules.map(moduleName => 
        PlatformTestUtils.testNativeModuleAvailability(moduleName)
      );

      // Verify all required modules are available
      moduleResults.forEach(result => {
        expect(result.available).toBe(true);
        expect(result.methods.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Platform-Specific Error Handling', () => {
    test('should handle platform-specific permission denials gracefully', async () => {
      const permissions = PlatformTestUtils.getPlatformPermissions();
      
      // Mock permission denial
      const { check, request } = require('react-native-permissions');
      check.mockResolvedValue('denied');
      request.mockResolvedValue('denied');

      const result = await PlatformTestUtils.testPermissionFlow(permissions.CAMERA);
      
      expect(result.granted).toBe(false);
      expect(result.denied).toBe(true);
      expect(result.finalStatus).toBe('denied');
    });

    test('should handle native module failures gracefully', async () => {
      // Test with non-existent module
      const result = PlatformTestUtils.testNativeModuleAvailability('NonExistentModule');
      
      expect(result.available).toBe(false);
      expect(result.methods).toEqual([]);
    });

    test('should handle device capability detection failures', async () => {
      // Mock DeviceInfo failure
      const DeviceInfo = require('react-native-device-info');
      DeviceInfo.isEmulator.mockRejectedValue(new Error('Device info unavailable'));

      try {
        await PlatformTestUtils.isPhysicalDevice();
      } catch (error) {
        expect(error.message).toBe('Device info unavailable');
      }
    });
  });

  describe('Performance and Reliability', () => {
    test('should complete platform tests within reasonable time', async () => {
      const startTime = Date.now();
      
      // Run a subset of platform tests
      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      const permissions = PlatformTestUtils.getPlatformPermissions();
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within 10 seconds
      expect(duration).toBeLessThan(10000);
      expect(capabilities).toBeDefined();
      expect(permissions).toBeDefined();
    });

    test('should handle concurrent platform operations', async () => {
      // Test multiple platform operations running concurrently
      const operations = [
        PlatformTestUtils.getDeviceCapabilities(),
        PlatformTestUtils.isPhysicalDevice(),
        PlatformTestUtils.hasCameraCapability(),
        PlatformTestUtils.hasGPSCapability(),
      ];

      const results = await Promise.all(operations);
      
      expect(results).toHaveLength(4);
      results.forEach(result => {
        expect(result).toBeDefined();
      });
    });

    test('should handle platform test timeouts gracefully', async () => {
      // Test timeout handling
      const slowOperation = () => new Promise(resolve => 
        setTimeout(() => resolve('completed'), 2000)
      );

      await expect(
        PlatformTestUtils.waitForAsync(slowOperation, 1000)
      ).rejects.toThrow('Operation timed out after 1000ms');
    });
  });

  describe('End-to-End Platform Workflow', () => {
    test('should complete full platform test suite', async () => {
      // Run the complete platform test suite
      const report = await testRunner.runAllTests();
      
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('results');
      expect(report).toHaveProperty('platformInfo');
      expect(report).toHaveProperty('recommendations');

      // Verify test results structure
      expect(report.results).toHaveProperty('permissions');
      expect(report.results).toHaveProperty('nativeModules');
      expect(report.results).toHaveProperty('deviceCapabilities');
      expect(report.results).toHaveProperty('uiTests');

      // Verify summary information
      expect(report.summary.platform).toBe(Platform.OS);
      expect(report.summary.totalTests).toBeGreaterThan(0);
      expect(report.summary.successRate).toBeGreaterThanOrEqual(0);
      expect(report.summary.successRate).toBeLessThanOrEqual(100);
    });

    test('should generate meaningful recommendations', async () => {
      const report = await testRunner.runAllTests();
      
      expect(Array.isArray(report.recommendations)).toBe(true);
      
      // If there are recommendations, they should have proper structure
      if (report.recommendations.length > 0) {
        report.recommendations.forEach(rec => {
          expect(rec).toHaveProperty('type');
          expect(rec).toHaveProperty('message');
          expect(rec).toHaveProperty('priority');
          expect(['HIGH', 'MEDIUM', 'LOW']).toContain(rec.priority);
        });
      }
    });

    test('should validate platform configuration', () => {
      const validation = PlatformTestUtils.validatePlatformConfiguration();
      
      expect(validation).toHaveProperty('valid');
      expect(validation).toHaveProperty('issues');
      expect(typeof validation.valid).toBe('boolean');
      expect(Array.isArray(validation.issues)).toBe(true);
    });
  });

  describe('Platform Test Data Generation', () => {
    test('should generate consistent platform test data', () => {
      const testData1 = PlatformTestUtils.generatePlatformTestData();
      const testData2 = PlatformTestUtils.generatePlatformTestData();
      
      // Should have consistent structure
      expect(testData1.platform).toBe(testData2.platform);
      expect(testData1.version).toBe(testData2.version);
      
      // Should have different timestamps
      expect(testData1.timestamp).not.toBe(testData2.timestamp);
      
      // Should have platform-specific data
      if (Platform.OS === 'ios') {
        expect(testData1).toHaveProperty('ios');
        expect(testData1.ios).toHaveProperty('bundleId');
        expect(testData1.ios).toHaveProperty('deploymentTarget');
      } else {
        expect(testData1).toHaveProperty('android');
        expect(testData1.android).toHaveProperty('packageName');
        expect(testData1.android).toHaveProperty('minSdkVersion');
      }
    });
  });
});
