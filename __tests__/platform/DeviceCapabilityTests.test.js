/**
 * Device Capability Tests
 * Tests device-specific features like camera hardware, sensors, screen properties, and platform-specific APIs
 */

import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import PlatformTestUtils from './PlatformTestUtils';
import DeviceInfoService from '../../src/services/DeviceInfoService';

// Mock react-native-device-info
jest.mock('react-native-device-info', () => ({
  getModel: jest.fn(),
  getBrand: jest.fn(),
  getSystemVersion: jest.fn(),
  getBuildNumber: jest.fn(),
  getApiLevel: jest.fn(),
  getScreenWidth: jest.fn(),
  getScreenHeight: jest.fn(),
  isEmulator: jest.fn(),
  hasNotch: jest.fn(),
  hasDynamicIsland: jest.fn(),
  getDeviceType: jest.fn(),
  isFingerprintSupported: jest.fn(),
  supportedAbis: jest.fn(),
  getSecurityPatch: jest.fn(),
  hasSystemFeature: jest.fn(),
  getManufacturer: jest.fn(),
  getHardware: jest.fn(),
  getProduct: jest.fn(),
  getTags: jest.fn(),
  getType: jest.fn(),
  isPinOrFingerprintSet: jest.fn(),
}));

describe('Device Capability Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Device Information', () => {
    test('should get device model and brand', async () => {
      const mockModel = 'iPhone 14 Pro';
      const mockBrand = 'Apple';
      
      DeviceInfo.getModel.mockResolvedValue(mockModel);
      DeviceInfo.getBrand.mockResolvedValue(mockBrand);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      expect(capabilities.deviceInfo.model).toBe(mockModel);
      expect(capabilities.deviceInfo.brand).toBe(mockBrand);
    });

    test('should detect physical vs emulator device', async () => {
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const isPhysical = await PlatformTestUtils.isPhysicalDevice();
      
      expect(isPhysical).toBe(true);
      expect(DeviceInfo.isEmulator).toHaveBeenCalled();
    });

    test('should get screen dimensions', async () => {
      const mockWidth = 390;
      const mockHeight = 844;
      
      DeviceInfo.getScreenWidth.mockResolvedValue(mockWidth);
      DeviceInfo.getScreenHeight.mockResolvedValue(mockHeight);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      expect(capabilities.screenDimensions.width).toBe(mockWidth);
      expect(capabilities.screenDimensions.height).toBe(mockHeight);
    });

    test('should get system version and build info', async () => {
      const mockSystemVersion = '17.0';
      const mockBuildNumber = '21A329';
      
      DeviceInfo.getSystemVersion.mockResolvedValue(mockSystemVersion);
      DeviceInfo.getBuildNumber.mockResolvedValue(mockBuildNumber);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      expect(capabilities.deviceInfo.systemVersion).toBe(mockSystemVersion);
      expect(capabilities.deviceInfo.buildNumber).toBe(mockBuildNumber);
    });
  });

  describe('iOS-Specific Capabilities', () => {
    beforeEach(() => {
      Platform.OS = 'ios';
    });

    test('should detect iPhone notch', async () => {
      DeviceInfo.hasNotch.mockReturnValue(true);
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      if (Platform.OS === 'ios') {
        expect(capabilities.ios.hasNotch).toBe(true);
      }
    });

    test('should detect Dynamic Island', async () => {
      DeviceInfo.hasDynamicIsland.mockReturnValue(true);
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      if (Platform.OS === 'ios') {
        expect(capabilities.ios.hasDynamicIsland).toBe(true);
      }
    });

    test('should get iOS device type', async () => {
      const mockDeviceType = 'iPhone';
      DeviceInfo.getDeviceType.mockResolvedValue(mockDeviceType);
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      if (Platform.OS === 'ios') {
        expect(capabilities.ios.deviceType).toBe(mockDeviceType);
      }
    });

    test('should detect biometric support on iOS', async () => {
      DeviceInfo.isFingerprintSupported.mockResolvedValue(true);
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      expect(capabilities.hasBiometrics).toBe(true);
    });
  });

  describe('Android-Specific Capabilities', () => {
    beforeEach(() => {
      Platform.OS = 'android';
    });

    test('should get Android API level', async () => {
      const mockApiLevel = 34;
      DeviceInfo.getApiLevel.mockResolvedValue(mockApiLevel);
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      if (Platform.OS === 'android') {
        expect(capabilities.android.apiLevel).toBe(mockApiLevel);
      }
    });

    test('should get Android security patch level', async () => {
      const mockSecurityPatch = '2023-10-01';
      DeviceInfo.getSecurityPatch.mockResolvedValue(mockSecurityPatch);
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      if (Platform.OS === 'android') {
        expect(capabilities.android.securityPatch).toBe(mockSecurityPatch);
      }
    });

    test('should get supported ABIs', async () => {
      const mockAbis = ['arm64-v8a', 'armeabi-v7a'];
      DeviceInfo.supportedAbis.mockResolvedValue(mockAbis);
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      if (Platform.OS === 'android') {
        expect(capabilities.android.supportedAbis).toEqual(mockAbis);
      }
    });

    test('should check Android system features', async () => {
      DeviceInfo.hasSystemFeature.mockImplementation((feature) => {
        const features = {
          'android.hardware.camera': true,
          'android.hardware.location.gps': true,
          'android.hardware.sensor.accelerometer': true,
        };
        return Promise.resolve(features[feature] || false);
      });

      const hasCamera = await PlatformTestUtils.hasCameraCapability();
      const hasGPS = await PlatformTestUtils.hasGPSCapability();
      
      expect(hasCamera).toBe(true);
      expect(hasGPS).toBe(true);
    });
  });

  describe('Camera Hardware Capabilities', () => {
    test('should detect camera availability on physical device', async () => {
      DeviceInfo.isEmulator.mockResolvedValue(false);
      DeviceInfo.hasSystemFeature.mockResolvedValue(true);

      const hasCamera = await PlatformTestUtils.hasCameraCapability();
      
      expect(hasCamera).toBe(true);
    });

    test('should handle camera unavailable on emulator', async () => {
      DeviceInfo.isEmulator.mockResolvedValue(true);

      const hasCamera = await PlatformTestUtils.hasCameraCapability();
      
      if (Platform.OS === 'android') {
        expect(DeviceInfo.hasSystemFeature).toHaveBeenCalledWith('android.hardware.camera');
      } else {
        // iOS assumes no camera on simulator
        expect(hasCamera).toBe(false);
      }
    });

    test('should handle camera capability check errors', async () => {
      DeviceInfo.hasSystemFeature.mockRejectedValue(new Error('Feature check failed'));
      DeviceInfo.isEmulator.mockResolvedValue(false);

      const hasCamera = await PlatformTestUtils.hasCameraCapability();
      
      expect(hasCamera).toBe(false);
    });
  });

  describe('GPS and Location Capabilities', () => {
    test('should detect GPS availability', async () => {
      DeviceInfo.isEmulator.mockResolvedValue(false);
      DeviceInfo.hasSystemFeature.mockResolvedValue(true);

      const hasGPS = await PlatformTestUtils.hasGPSCapability();
      
      expect(hasGPS).toBe(true);
    });

    test('should handle GPS unavailable', async () => {
      DeviceInfo.isEmulator.mockResolvedValue(true);
      DeviceInfo.hasSystemFeature.mockResolvedValue(false);

      const hasGPS = await PlatformTestUtils.hasGPSCapability();
      
      if (Platform.OS === 'android') {
        expect(hasGPS).toBe(false);
      } else {
        // iOS simulator doesn't have GPS
        expect(hasGPS).toBe(false);
      }
    });
  });

  describe('Biometric Capabilities', () => {
    test('should detect biometric support', async () => {
      DeviceInfo.isFingerprintSupported.mockResolvedValue(true);
      DeviceInfo.isPinOrFingerprintSet.mockResolvedValue(true);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      expect(capabilities.hasBiometrics).toBe(true);
    });

    test('should handle biometric unavailable', async () => {
      DeviceInfo.isFingerprintSupported.mockResolvedValue(false);

      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      expect(capabilities.hasBiometrics).toBe(false);
    });
  });

  describe('Device Capability Integration', () => {
    test('should integrate with DeviceInfoService', async () => {
      // Mock all required DeviceInfo methods
      DeviceInfo.getModel.mockResolvedValue('Test Device');
      DeviceInfo.getBrand.mockResolvedValue('Test Brand');
      DeviceInfo.getSystemVersion.mockResolvedValue('1.0');
      DeviceInfo.getBuildNumber.mockResolvedValue('100');
      DeviceInfo.getManufacturer.mockResolvedValue('Test Manufacturer');
      DeviceInfo.getHardware.mockResolvedValue('Test Hardware');
      DeviceInfo.getProduct.mockResolvedValue('Test Product');
      DeviceInfo.getTags.mockResolvedValue('test-tags');
      DeviceInfo.getType.mockResolvedValue('test');
      DeviceInfo.getScreenWidth.mockReturnValue(400);
      DeviceInfo.getScreenHeight.mockReturnValue(800);
      DeviceInfo.hasNotch.mockReturnValue(false);
      DeviceInfo.hasDynamicIsland.mockReturnValue(false);
      DeviceInfo.isPinOrFingerprintSet.mockResolvedValue(false);
      DeviceInfo.supportedAbis.mockResolvedValue(['arm64']);
      DeviceInfo.isEmulator.mockResolvedValue(true);

      const deviceInfoService = new DeviceInfoService();
      const deviceInfo = await deviceInfoService.getDeviceInfo();
      
      expect(deviceInfo).toHaveProperty('model');
      expect(deviceInfo).toHaveProperty('brand');
      expect(deviceInfo).toHaveProperty('platform');
      expect(deviceInfo.platform).toBe(Platform.OS);
    });

    test('should generate platform-specific test data', () => {
      const testData = PlatformTestUtils.generatePlatformTestData();
      
      expect(testData).toHaveProperty('platform');
      expect(testData).toHaveProperty('timestamp');
      expect(testData.platform).toBe(Platform.OS);
      
      if (Platform.OS === 'ios') {
        expect(testData).toHaveProperty('ios');
        expect(testData.ios).toHaveProperty('bundleId');
        expect(testData.ios).toHaveProperty('deploymentTarget');
      } else {
        expect(testData).toHaveProperty('android');
        expect(testData.android).toHaveProperty('packageName');
        expect(testData.android).toHaveProperty('minSdkVersion');
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle DeviceInfo method failures gracefully', async () => {
      DeviceInfo.getModel.mockRejectedValue(new Error('Device info unavailable'));
      DeviceInfo.isEmulator.mockResolvedValue(false);

      try {
        await PlatformTestUtils.getDeviceCapabilities();
      } catch (error) {
        expect(error.message).toBe('Device info unavailable');
      }
    });

    test('should handle timeout in capability detection', async () => {
      DeviceInfo.isEmulator.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(false), 2000))
      );

      await expect(
        PlatformTestUtils.waitForAsync(
          () => PlatformTestUtils.isPhysicalDevice(),
          1000
        )
      ).rejects.toThrow('Operation timed out after 1000ms');
    });
  });
});
