/**
 * Native Module Tests
 * Tests VGuard security module and other native modules functionality across platforms
 */

import { Platform, NativeModules, NativeEventEmitter } from 'react-native';
import PlatformTestUtils from './PlatformTestUtils';

// Mock NativeModules for testing
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios', // Can be changed in individual tests
    Version: '17.0',
  },
  NativeModules: {
    VGuardPlugin: {
      // Mock VGuard methods
      sdkVersion: jest.fn(),
      getFirmwareVersion: jest.fn(),
      getProcessorVersion: jest.fn(),
      isVosRunning: jest.fn(),
      getIsVosStarted: jest.fn(),
      invokeVGuard: jest.fn(),
      // Event constants
      VGUARD_READY: 'VGUARD_READY',
      VGUARD_ERROR: 'VGUARD_ERROR',
      VGUARD_DEBUGGER_DETECTED: 'VGUARD_DEBUGGER_DETECTED',
      VGUARD_JAILBREAK_DETECTED: 'VGUARD_JAILBREAK_DETECTED',
      VGUARD_SCREEN_SHARING_DETECTED: 'VGUARD_SCREEN_SHARING_DETECTED',
      VGUARD_VIRTUAL_SPACE_DETECTED: 'VGUARD_VIRTUAL_SPACE_DETECTED',
      VGUARD_OVERLAY_DETECTED: 'VGUARD_OVERLAY_DETECTED',
    },
    RNDeviceInfo: {
      getModel: jest.fn(),
      getBrand: jest.fn(),
      getSystemVersion: jest.fn(),
      isEmulator: jest.fn(),
    },
    RNPermissions: {
      checkPermission: jest.fn(),
      requestPermission: jest.fn(),
    },
  },
  NativeEventEmitter: jest.fn().mockImplementation(() => ({
    addListener: jest.fn(),
    removeListener: jest.fn(),
    removeAllListeners: jest.fn(),
  })),
}));

describe('Native Module Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('VGuard Security Module', () => {
    test('should detect VGuard module availability', () => {
      const result = PlatformTestUtils.testNativeModuleAvailability('VGuardPlugin');
      
      expect(result.moduleName).toBe('VGuardPlugin');
      expect(result.available).toBe(true);
      expect(result.methods).toContain('sdkVersion');
      expect(result.methods).toContain('getFirmwareVersion');
      expect(result.methods).toContain('isVosRunning');
    });

    test('should get VGuard SDK version', async () => {
      const mockVersion = '1.2.3';
      NativeModules.VGuardPlugin.sdkVersion.mockResolvedValue(mockVersion);

      const version = await NativeModules.VGuardPlugin.sdkVersion();
      
      expect(version).toBe(mockVersion);
      expect(NativeModules.VGuardPlugin.sdkVersion).toHaveBeenCalled();
    });

    test('should get firmware version', async () => {
      const mockFirmware = '2.1.0';
      NativeModules.VGuardPlugin.getFirmwareVersion.mockResolvedValue(mockFirmware);

      const firmware = await NativeModules.VGuardPlugin.getFirmwareVersion();
      
      expect(firmware).toBe(mockFirmware);
      expect(NativeModules.VGuardPlugin.getFirmwareVersion).toHaveBeenCalled();
    });

    test('should check VOS running status', async () => {
      NativeModules.VGuardPlugin.isVosRunning.mockResolvedValue(true);

      const isRunning = await NativeModules.VGuardPlugin.isVosRunning();
      
      expect(isRunning).toBe(true);
      expect(NativeModules.VGuardPlugin.isVosRunning).toHaveBeenCalled();
    });

    test('should handle VGuard initialization', async () => {
      NativeModules.VGuardPlugin.invokeVGuard.mockResolvedValue(undefined);

      await NativeModules.VGuardPlugin.invokeVGuard();
      
      expect(NativeModules.VGuardPlugin.invokeVGuard).toHaveBeenCalled();
    });

    test('should handle VGuard errors gracefully', async () => {
      const error = new Error('VGuard initialization failed');
      NativeModules.VGuardPlugin.sdkVersion.mockRejectedValue(error);

      await expect(NativeModules.VGuardPlugin.sdkVersion()).rejects.toThrow(error);
    });
  });

  describe('Platform-Specific VGuard Behavior', () => {
    test('should handle iOS-specific VGuard features', async () => {
      Platform.OS = 'ios';
      
      // Test iOS-specific VOS started check
      NativeModules.VGuardPlugin.getIsVosStarted.mockResolvedValue(true);
      
      const isStarted = await NativeModules.VGuardPlugin.getIsVosStarted();
      expect(isStarted).toBe(true);
    });

    test('should handle Android-specific VGuard features', () => {
      Platform.OS = 'android';
      
      // Test Android-specific threat detection events
      const androidEvents = [
        'VGUARD_VIRTUAL_SPACE_DETECTED',
        'VGUARD_OVERLAY_DETECTED',
      ];
      
      androidEvents.forEach(event => {
        expect(NativeModules.VGuardPlugin[event]).toBeDefined();
      });
    });

    test('should set up VGuard event listeners correctly', () => {
      const mockEmitter = new NativeEventEmitter(NativeModules.VGuardPlugin);
      
      expect(NativeEventEmitter).toHaveBeenCalledWith(NativeModules.VGuardPlugin);
      expect(mockEmitter.addListener).toBeDefined();
    });
  });

  describe('Device Info Module', () => {
    test('should detect DeviceInfo module availability', () => {
      const result = PlatformTestUtils.testNativeModuleAvailability('RNDeviceInfo');
      
      expect(result.moduleName).toBe('RNDeviceInfo');
      expect(result.available).toBe(true);
      expect(result.methods).toContain('getModel');
      expect(result.methods).toContain('getBrand');
    });

    test('should get device information', async () => {
      const mockModel = 'iPhone 14';
      const mockBrand = 'Apple';
      
      NativeModules.RNDeviceInfo.getModel.mockResolvedValue(mockModel);
      NativeModules.RNDeviceInfo.getBrand.mockResolvedValue(mockBrand);

      const model = await NativeModules.RNDeviceInfo.getModel();
      const brand = await NativeModules.RNDeviceInfo.getBrand();
      
      expect(model).toBe(mockModel);
      expect(brand).toBe(mockBrand);
    });

    test('should detect emulator status', async () => {
      NativeModules.RNDeviceInfo.isEmulator.mockResolvedValue(false);

      const isEmulator = await NativeModules.RNDeviceInfo.isEmulator();
      
      expect(isEmulator).toBe(false);
    });
  });

  describe('Permissions Module', () => {
    test('should detect Permissions module availability', () => {
      const result = PlatformTestUtils.testNativeModuleAvailability('RNPermissions');
      
      expect(result.moduleName).toBe('RNPermissions');
      expect(result.available).toBe(true);
    });

    test('should check permissions through native module', async () => {
      const mockPermission = 'camera';
      const mockStatus = 'granted';
      
      NativeModules.RNPermissions.checkPermission.mockResolvedValue(mockStatus);

      const status = await NativeModules.RNPermissions.checkPermission(mockPermission);
      
      expect(status).toBe(mockStatus);
      expect(NativeModules.RNPermissions.checkPermission).toHaveBeenCalledWith(mockPermission);
    });
  });

  describe('Native Module Error Handling', () => {
    test('should handle missing native modules gracefully', () => {
      const result = PlatformTestUtils.testNativeModuleAvailability('NonExistentModule');
      
      expect(result.moduleName).toBe('NonExistentModule');
      expect(result.available).toBe(false);
      expect(result.methods).toEqual([]);
    });

    test('should handle native module method errors', async () => {
      const error = new Error('Native method failed');
      NativeModules.VGuardPlugin.sdkVersion.mockRejectedValue(error);

      try {
        await NativeModules.VGuardPlugin.sdkVersion();
        fail('Should have thrown an error');
      } catch (e) {
        expect(e).toBe(error);
      }
    });

    test('should timeout on slow native module calls', async () => {
      NativeModules.VGuardPlugin.sdkVersion.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve('1.0.0'), 2000))
      );

      await expect(
        PlatformTestUtils.waitForAsync(
          () => NativeModules.VGuardPlugin.sdkVersion(),
          1000
        )
      ).rejects.toThrow('Operation timed out after 1000ms');
    });
  });

  describe('Native Module Integration', () => {
    test('should validate all required native modules are available', () => {
      const requiredModules = ['VGuardPlugin', 'RNDeviceInfo', 'RNPermissions'];
      const results = requiredModules.map(module => 
        PlatformTestUtils.testNativeModuleAvailability(module)
      );

      results.forEach(result => {
        expect(result.available).toBe(true);
      });
    });

    test('should test native module method signatures', () => {
      const vguardMethods = PlatformTestUtils.testNativeModuleAvailability('VGuardPlugin').methods;
      
      // Check that essential VGuard methods are available
      const essentialMethods = ['sdkVersion', 'isVosRunning', 'invokeVGuard'];
      essentialMethods.forEach(method => {
        expect(vguardMethods).toContain(method);
      });
    });
  });

  describe('Platform Configuration Validation', () => {
    test('should validate native module configuration', () => {
      const validation = PlatformTestUtils.validatePlatformConfiguration();
      
      expect(validation).toHaveProperty('valid');
      expect(validation).toHaveProperty('issues');
      
      if (!validation.valid) {
        console.warn('Platform configuration issues:', validation.issues);
      }
    });
  });
});
