/**
 * Basic Platform Tests
 * Simple tests to verify platform-specific functionality works
 */

describe('Basic Platform Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Platform Detection', () => {
    test('should detect platform correctly', () => {
      const { Platform } = require('react-native');
      
      expect(Platform.OS).toBeDefined();
      expect(['ios', 'android']).toContain(Platform.OS);
    });

    test('should have Platform.select function', () => {
      const { Platform } = require('react-native');
      
      expect(typeof Platform.select).toBe('function');
      
      const result = Platform.select({
        ios: 'iOS Value',
        android: 'Android Value',
        default: 'Default Value',
      });
      
      expect(result).toBeDefined();
    });

    test('should have Platform.Version', () => {
      const { Platform } = require('react-native');
      
      expect(Platform.Version).toBeDefined();
    });
  });

  describe('Native Modules', () => {
    test('should have VGuardPlugin available', () => {
      const { NativeModules } = require('react-native');
      
      expect(NativeModules.VGuardPlugin).toBeDefined();
      expect(typeof NativeModules.VGuardPlugin.sdkVersion).toBe('function');
    });

    test('should have DeviceInfo available', () => {
      const { NativeModules } = require('react-native');
      
      expect(NativeModules.RNDeviceInfo).toBeDefined();
      expect(typeof NativeModules.RNDeviceInfo.getModel).toBe('function');
    });

    test('should have Permissions available', () => {
      const { NativeModules } = require('react-native');
      
      expect(NativeModules.RNPermissions).toBeDefined();
      expect(typeof NativeModules.RNPermissions.checkPermission).toBe('function');
    });
  });

  describe('Permission Constants', () => {
    test('should have permission constants available', () => {
      const permissions = require('react-native-permissions');
      
      expect(permissions.PERMISSIONS).toBeDefined();
      expect(permissions.PERMISSIONS.IOS).toBeDefined();
      expect(permissions.PERMISSIONS.ANDROID).toBeDefined();
      expect(permissions.RESULTS).toBeDefined();
    });

    test('should have permission functions available', () => {
      const permissions = require('react-native-permissions');
      
      expect(typeof permissions.check).toBe('function');
      expect(typeof permissions.request).toBe('function');
    });
  });

  describe('Device Info', () => {
    test('should have DeviceInfo functions available', () => {
      const DeviceInfo = require('react-native-device-info');
      
      expect(typeof DeviceInfo.getModel).toBe('function');
      expect(typeof DeviceInfo.getBrand).toBe('function');
      expect(typeof DeviceInfo.isEmulator).toBe('function');
    });

    test('should be able to call DeviceInfo functions', async () => {
      const DeviceInfo = require('react-native-device-info');
      
      const model = await DeviceInfo.getModel();
      const brand = await DeviceInfo.getBrand();
      const isEmulator = await DeviceInfo.isEmulator();
      
      expect(typeof model).toBe('string');
      expect(typeof brand).toBe('string');
      expect(typeof isEmulator).toBe('boolean');
    });
  });

  describe('Platform Test Utils', () => {
    test('should be able to import PlatformTestUtils', () => {
      const PlatformTestUtils = require('../platform/PlatformTestUtils').default;
      
      expect(PlatformTestUtils).toBeDefined();
      expect(typeof PlatformTestUtils.getPlatformPermissions).toBe('function');
      expect(typeof PlatformTestUtils.isPhysicalDevice).toBe('function');
    });

    test('should get platform permissions', () => {
      const PlatformTestUtils = require('../platform/PlatformTestUtils').default;
      
      const permissions = PlatformTestUtils.getPlatformPermissions();
      
      expect(permissions).toBeDefined();
      expect(permissions.CAMERA).toBeDefined();
      expect(permissions.MICROPHONE).toBeDefined();
      expect(permissions.LOCATION).toBeDefined();
    });

    test('should generate platform test data', () => {
      const PlatformTestUtils = require('../platform/PlatformTestUtils').default;
      
      const testData = PlatformTestUtils.generatePlatformTestData();
      
      expect(testData).toBeDefined();
      expect(testData.platform).toBeDefined();
      expect(testData.timestamp).toBeDefined();
      expect(typeof testData.timestamp).toBe('number');
    });
  });

  describe('Mock Functionality', () => {
    test('should have working permission mocks', async () => {
      const { check, request } = require('react-native-permissions');
      
      const checkResult = await check('test-permission');
      const requestResult = await request('test-permission');
      
      expect(checkResult).toBe('granted');
      expect(requestResult).toBe('granted');
    });

    test('should have working DeviceInfo mocks', async () => {
      const DeviceInfo = require('react-native-device-info');
      
      const model = await DeviceInfo.getModel();
      const screenWidth = DeviceInfo.getScreenWidth();
      
      expect(model).toBe('iPhone 14 Pro');
      expect(screenWidth).toBe(390);
    });

    test('should have working VGuard mocks', async () => {
      const { NativeModules } = require('react-native');
      
      const version = await NativeModules.VGuardPlugin.sdkVersion();
      const isRunning = await NativeModules.VGuardPlugin.isVosRunning();
      
      expect(version).toBe('1.0.0');
      expect(isRunning).toBe(true);
    });
  });

  describe('Platform Configuration', () => {
    test('should validate platform configuration', () => {
      const PlatformTestUtils = require('../platform/PlatformTestUtils').default;
      
      const validation = PlatformTestUtils.validatePlatformConfiguration();
      
      expect(validation).toBeDefined();
      expect(validation).toHaveProperty('valid');
      expect(validation).toHaveProperty('issues');
      expect(typeof validation.valid).toBe('boolean');
      expect(Array.isArray(validation.issues)).toBe(true);
    });

    test('should test native module availability', () => {
      const PlatformTestUtils = require('../platform/PlatformTestUtils').default;
      
      const result = PlatformTestUtils.testNativeModuleAvailability('VGuardPlugin');
      
      expect(result).toBeDefined();
      expect(result.moduleName).toBe('VGuardPlugin');
      expect(result.available).toBe(true);
      expect(Array.isArray(result.methods)).toBe(true);
    });
  });
});
