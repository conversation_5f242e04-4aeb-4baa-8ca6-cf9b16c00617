/**
 * Platform Test Runner
 * Automated testing pipeline for running platform-specific tests on iOS and Android
 */

import { Platform } from 'react-native';
import PlatformTestUtils from './PlatformTestUtils';

export class PlatformTestRunner {
  constructor() {
    this.testResults = {
      permissions: [],
      nativeModules: [],
      deviceCapabilities: [],
      uiTests: [],
      errors: [],
    };
    this.startTime = null;
    this.endTime = null;
  }

  /**
   * Run all platform-specific tests
   */
  async runAllTests() {
    this.startTime = Date.now();
    console.log(`🚀 Starting platform-specific tests for ${Platform.OS}...`);

    try {
      // Run test suites in sequence
      await this.runPermissionTests();
      await this.runNativeModuleTests();
      await this.runDeviceCapabilityTests();
      await this.runUITests();

      this.endTime = Date.now();
      return this.generateTestReport();
    } catch (error) {
      this.testResults.errors.push({
        type: 'RUNNER_ERROR',
        message: error.message,
        stack: error.stack,
        timestamp: Date.now(),
      });
      
      this.endTime = Date.now();
      return this.generateTestReport();
    }
  }

  /**
   * Run permission tests
   */
  async runPermissionTests() {
    console.log('📱 Running permission tests...');
    
    try {
      const permissions = PlatformTestUtils.getPlatformPermissions();
      
      for (const [name, permission] of Object.entries(permissions)) {
        try {
          const result = await PlatformTestUtils.testPermissionFlow(permission);
          this.testResults.permissions.push({
            name,
            permission,
            ...result,
            status: 'PASSED',
            timestamp: Date.now(),
          });
        } catch (error) {
          this.testResults.permissions.push({
            name,
            permission,
            status: 'FAILED',
            error: error.message,
            timestamp: Date.now(),
          });
        }
      }
    } catch (error) {
      this.testResults.errors.push({
        type: 'PERMISSION_TEST_ERROR',
        message: error.message,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * Run native module tests
   */
  async runNativeModuleTests() {
    console.log('🔧 Running native module tests...');
    
    const requiredModules = [
      'VGuardPlugin',
      'RNDeviceInfo',
      'RNPermissions',
      'RNCAsyncStorage',
      'RNGestureHandler',
    ];

    for (const moduleName of requiredModules) {
      try {
        const result = PlatformTestUtils.testNativeModuleAvailability(moduleName);
        this.testResults.nativeModules.push({
          ...result,
          status: result.available ? 'PASSED' : 'FAILED',
          timestamp: Date.now(),
        });
      } catch (error) {
        this.testResults.nativeModules.push({
          moduleName,
          available: false,
          status: 'ERROR',
          error: error.message,
          timestamp: Date.now(),
        });
      }
    }
  }

  /**
   * Run device capability tests
   */
  async runDeviceCapabilityTests() {
    console.log('📊 Running device capability tests...');
    
    try {
      const capabilities = await PlatformTestUtils.getDeviceCapabilities();
      
      const tests = [
        {
          name: 'Physical Device Detection',
          test: () => capabilities.isPhysical !== undefined,
        },
        {
          name: 'Camera Capability',
          test: () => capabilities.hasCamera !== undefined,
        },
        {
          name: 'GPS Capability',
          test: () => capabilities.hasGPS !== undefined,
        },
        {
          name: 'Screen Dimensions',
          test: () => capabilities.screenDimensions.width > 0 && capabilities.screenDimensions.height > 0,
        },
        {
          name: 'Device Info',
          test: () => capabilities.deviceInfo.brand && capabilities.deviceInfo.model,
        },
      ];

      for (const testCase of tests) {
        try {
          const passed = testCase.test();
          this.testResults.deviceCapabilities.push({
            name: testCase.name,
            status: passed ? 'PASSED' : 'FAILED',
            result: passed,
            timestamp: Date.now(),
          });
        } catch (error) {
          this.testResults.deviceCapabilities.push({
            name: testCase.name,
            status: 'ERROR',
            error: error.message,
            timestamp: Date.now(),
          });
        }
      }
    } catch (error) {
      this.testResults.errors.push({
        type: 'DEVICE_CAPABILITY_ERROR',
        message: error.message,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * Run UI tests
   */
  async runUITests() {
    console.log('🎨 Running UI tests...');
    
    try {
      const uiTests = [
        {
          name: 'Platform Detection',
          test: () => Platform.OS === 'ios' || Platform.OS === 'android',
        },
        {
          name: 'Platform Select Function',
          test: () => typeof Platform.select === 'function',
        },
        {
          name: 'Platform Version',
          test: () => Platform.Version !== undefined,
        },
      ];

      for (const testCase of uiTests) {
        try {
          const passed = testCase.test();
          this.testResults.uiTests.push({
            name: testCase.name,
            status: passed ? 'PASSED' : 'FAILED',
            result: passed,
            timestamp: Date.now(),
          });
        } catch (error) {
          this.testResults.uiTests.push({
            name: testCase.name,
            status: 'ERROR',
            error: error.message,
            timestamp: Date.now(),
          });
        }
      }
    } catch (error) {
      this.testResults.errors.push({
        type: 'UI_TEST_ERROR',
        message: error.message,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * Generate comprehensive test report
   */
  generateTestReport() {
    const duration = this.endTime - this.startTime;
    
    const summary = {
      platform: Platform.OS,
      duration: `${duration}ms`,
      startTime: new Date(this.startTime).toISOString(),
      endTime: new Date(this.endTime).toISOString(),
      totalTests: this.getTotalTestCount(),
      passedTests: this.getPassedTestCount(),
      failedTests: this.getFailedTestCount(),
      errorTests: this.getErrorTestCount(),
      successRate: this.getSuccessRate(),
    };

    const report = {
      summary,
      results: this.testResults,
      platformInfo: this.getPlatformInfo(),
      recommendations: this.generateRecommendations(),
    };

    this.logReport(report);
    return report;
  }

  /**
   * Get total test count
   */
  getTotalTestCount() {
    return (
      this.testResults.permissions.length +
      this.testResults.nativeModules.length +
      this.testResults.deviceCapabilities.length +
      this.testResults.uiTests.length
    );
  }

  /**
   * Get passed test count
   */
  getPassedTestCount() {
    return [
      ...this.testResults.permissions,
      ...this.testResults.nativeModules,
      ...this.testResults.deviceCapabilities,
      ...this.testResults.uiTests,
    ].filter(test => test.status === 'PASSED').length;
  }

  /**
   * Get failed test count
   */
  getFailedTestCount() {
    return [
      ...this.testResults.permissions,
      ...this.testResults.nativeModules,
      ...this.testResults.deviceCapabilities,
      ...this.testResults.uiTests,
    ].filter(test => test.status === 'FAILED').length;
  }

  /**
   * Get error test count
   */
  getErrorTestCount() {
    return [
      ...this.testResults.permissions,
      ...this.testResults.nativeModules,
      ...this.testResults.deviceCapabilities,
      ...this.testResults.uiTests,
    ].filter(test => test.status === 'ERROR').length + this.testResults.errors.length;
  }

  /**
   * Calculate success rate
   */
  getSuccessRate() {
    const total = this.getTotalTestCount();
    const passed = this.getPassedTestCount();
    return total > 0 ? Math.round((passed / total) * 100) : 0;
  }

  /**
   * Get platform information
   */
  getPlatformInfo() {
    return PlatformTestUtils.generatePlatformTestData();
  }

  /**
   * Generate recommendations based on test results
   */
  generateRecommendations() {
    const recommendations = [];

    // Check permission failures
    const failedPermissions = this.testResults.permissions.filter(p => p.status === 'FAILED');
    if (failedPermissions.length > 0) {
      recommendations.push({
        type: 'PERMISSION',
        message: `${failedPermissions.length} permission(s) failed. Check Info.plist (iOS) or AndroidManifest.xml (Android) configuration.`,
        priority: 'HIGH',
      });
    }

    // Check native module failures
    const failedModules = this.testResults.nativeModules.filter(m => m.status === 'FAILED');
    if (failedModules.length > 0) {
      recommendations.push({
        type: 'NATIVE_MODULE',
        message: `${failedModules.length} native module(s) unavailable. Check linking and installation.`,
        priority: 'HIGH',
      });
    }

    // Check device capability issues
    const failedCapabilities = this.testResults.deviceCapabilities.filter(c => c.status === 'FAILED');
    if (failedCapabilities.length > 0) {
      recommendations.push({
        type: 'DEVICE_CAPABILITY',
        message: `${failedCapabilities.length} device capability test(s) failed. May affect functionality on this device.`,
        priority: 'MEDIUM',
      });
    }

    // Check overall success rate
    if (this.getSuccessRate() < 80) {
      recommendations.push({
        type: 'OVERALL',
        message: 'Low success rate detected. Review platform configuration and dependencies.',
        priority: 'HIGH',
      });
    }

    return recommendations;
  }

  /**
   * Log test report to console
   */
  logReport(report) {
    console.log('\n📊 Platform Test Report');
    console.log('========================');
    console.log(`Platform: ${report.summary.platform}`);
    console.log(`Duration: ${report.summary.duration}`);
    console.log(`Total Tests: ${report.summary.totalTests}`);
    console.log(`✅ Passed: ${report.summary.passedTests}`);
    console.log(`❌ Failed: ${report.summary.failedTests}`);
    console.log(`⚠️  Errors: ${report.summary.errorTests}`);
    console.log(`📈 Success Rate: ${report.summary.successRate}%`);

    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. [${rec.priority}] ${rec.message}`);
      });
    }

    console.log('\n========================\n');
  }
}

export default PlatformTestRunner;
