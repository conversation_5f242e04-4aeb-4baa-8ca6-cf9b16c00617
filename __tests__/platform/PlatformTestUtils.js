/**
 * Platform-Specific Test Utilities
 * Provides utilities for testing platform-specific features across iOS and Android
 */

import { Platform, NativeModules } from 'react-native';
import { PERMISSIONS, RESULTS, request, check } from 'react-native-permissions';
import DeviceInfo from 'react-native-device-info';

export class PlatformTestUtils {
  /**
   * Get platform-specific permissions for testing
   */
  static getPlatformPermissions() {
    if (Platform.OS === 'ios') {
      return {
        CAMERA: PERMISSIONS.IOS.CAMERA,
        MICROPHONE: PERMISSIONS.IOS.MICROPHONE,
        LOCATION: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        PHOTO_LIBRARY: PERMISSIONS.IOS.PHOTO_LIBRARY,
        PHOTO_LIBRARY_ADD: PERMISSIONS.IOS.PHOTO_LIBRARY_ADD_ONLY,
      };
    } else {
      return {
        CAMERA: PERMISSIONS.ANDROID.CAMERA,
        MICROPHONE: PERMISSIONS.ANDROID.RECORD_AUDIO,
        LOCATION: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
        PHOTO_LIBRARY: PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
        PHOTO_LIBRARY_ADD: PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
      };
    }
  }

  /**
   * Check if running on physical device vs simulator/emulator
   */
  static async isPhysicalDevice() {
    return await DeviceInfo.isEmulator().then(isEmulator => !isEmulator);
  }

  /**
   * Get platform-specific device capabilities
   */
  static async getDeviceCapabilities() {
    const capabilities = {
      platform: Platform.OS,
      version: Platform.Version,
      isPhysical: await this.isPhysicalDevice(),
      hasCamera: await this.hasCameraCapability(),
      hasGPS: await this.hasGPSCapability(),
      hasBiometrics: await DeviceInfo.isFingerprintSupported(),
      screenDimensions: {
        width: await DeviceInfo.getScreenWidth(),
        height: await DeviceInfo.getScreenHeight(),
      },
      deviceInfo: {
        brand: await DeviceInfo.getBrand(),
        model: await DeviceInfo.getModel(),
        systemVersion: await DeviceInfo.getSystemVersion(),
        buildNumber: await DeviceInfo.getBuildNumber(),
      }
    };

    // Add platform-specific capabilities
    if (Platform.OS === 'ios') {
      capabilities.ios = {
        hasNotch: DeviceInfo.hasNotch(),
        hasDynamicIsland: DeviceInfo.hasDynamicIsland(),
        deviceType: await DeviceInfo.getDeviceType(),
      };
    } else {
      capabilities.android = {
        apiLevel: await DeviceInfo.getApiLevel(),
        securityPatch: await DeviceInfo.getSecurityPatch(),
        supportedAbis: await DeviceInfo.supportedAbis(),
      };
    }

    return capabilities;
  }

  /**
   * Check if device has camera capability
   */
  static async hasCameraCapability() {
    try {
      if (Platform.OS === 'android') {
        // Check if camera feature is available
        return await DeviceInfo.hasSystemFeature('android.hardware.camera');
      } else {
        // On iOS, assume camera is available on physical devices
        return await this.isPhysicalDevice();
      }
    } catch (error) {
      console.warn('Error checking camera capability:', error);
      return false;
    }
  }

  /**
   * Check if device has GPS capability
   */
  static async hasGPSCapability() {
    try {
      if (Platform.OS === 'android') {
        return await DeviceInfo.hasSystemFeature('android.hardware.location.gps');
      } else {
        // On iOS, assume GPS is available on physical devices
        return await this.isPhysicalDevice();
      }
    } catch (error) {
      console.warn('Error checking GPS capability:', error);
      return false;
    }
  }

  /**
   * Test permission request flow
   */
  static async testPermissionFlow(permission) {
    try {
      // Check current status
      const initialStatus = await check(permission);
      
      // Request permission if not granted
      let finalStatus = initialStatus;
      if (initialStatus !== RESULTS.GRANTED) {
        finalStatus = await request(permission);
      }

      return {
        permission,
        initialStatus,
        finalStatus,
        granted: finalStatus === RESULTS.GRANTED,
        denied: finalStatus === RESULTS.DENIED,
        blocked: finalStatus === RESULTS.BLOCKED,
      };
    } catch (error) {
      return {
        permission,
        error: error.message,
        granted: false,
      };
    }
  }

  /**
   * Test native module availability
   */
  static testNativeModuleAvailability(moduleName) {
    try {
      const module = NativeModules[moduleName];
      return {
        moduleName,
        available: !!module,
        methods: module ? Object.keys(module).filter(key => typeof module[key] === 'function') : [],
      };
    } catch (error) {
      return {
        moduleName,
        available: false,
        error: error.message,
      };
    }
  }

  /**
   * Create mock permission responses for testing
   */
  static createMockPermissionResponse(granted = true) {
    return granted ? RESULTS.GRANTED : RESULTS.DENIED;
  }

  /**
   * Wait for async operations with timeout
   */
  static async waitForAsync(asyncFn, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`));
      }, timeout);

      asyncFn()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Generate platform-specific test data
   */
  static generatePlatformTestData() {
    const baseData = {
      timestamp: Date.now(),
      platform: Platform.OS,
      version: Platform.Version,
    };

    if (Platform.OS === 'ios') {
      return {
        ...baseData,
        ios: {
          bundleId: 'com.testversion0753',
          deploymentTarget: '14.0',
          expectedPermissions: ['camera', 'microphone', 'location', 'photos'],
        },
      };
    } else {
      return {
        ...baseData,
        android: {
          packageName: 'com.testversion0753',
          minSdkVersion: 23,
          targetSdkVersion: 34,
          expectedPermissions: ['camera', 'record_audio', 'access_fine_location', 'write_external_storage'],
        },
      };
    }
  }

  /**
   * Validate platform-specific configuration
   */
  static validatePlatformConfiguration() {
    const issues = [];

    // Check platform-specific requirements
    if (Platform.OS === 'ios') {
      // iOS-specific validations
      if (!NativeModules.RNPermissions) {
        issues.push('react-native-permissions not properly configured for iOS');
      }
    } else {
      // Android-specific validations
      if (!NativeModules.PermissionsAndroid) {
        issues.push('Android permissions module not available');
      }
    }

    return {
      valid: issues.length === 0,
      issues,
    };
  }
}

export default PlatformTestUtils;
