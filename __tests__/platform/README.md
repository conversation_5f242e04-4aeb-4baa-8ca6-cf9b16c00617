# Platform-Specific Testing Infrastructure

This directory contains comprehensive testing infrastructure for platform-specific features in the React Native camera application, supporting both iOS and Android platforms.

## Overview

The platform testing infrastructure provides:

- **Permission Testing**: Automated tests for camera, microphone, location, and photo library permissions
- **Native Module Testing**: Tests for VGuard security module and other native modules
- **Device Capability Testing**: Tests for device-specific features like camera hardware, sensors, and screen properties
- **Platform-Specific UI Testing**: Tests for platform-specific behaviors, navigation patterns, and visual elements
- **Automated Test Pipeline**: Comprehensive test runner that can execute tests on both platforms

## Test Files Structure

```
__tests__/platform/
├── README.md                          # This documentation
├── PlatformTestUtils.js              # Core utilities for platform testing
├── PlatformBasicTests.test.js        # Basic platform functionality tests
├── PermissionTests.test.js           # Permission-specific tests
├── NativeModuleTests.test.js         # Native module tests
├── DeviceCapabilityTests.test.js     # Device capability tests
├── PlatformUITests.test.js           # Platform-specific UI tests
└── PlatformTestRunner.js             # Automated test runner
```

## Key Features

### 1. Platform Detection and Configuration
- Automatic iOS/Android platform detection
- Platform-specific permission mapping
- Device capability detection
- Configuration validation

### 2. Permission Testing
- **iOS Permissions**: Camera, Microphone, Location When In Use, Photo Library, Photo Library Add Only
- **Android Permissions**: Camera, Record Audio, Fine Location, External Storage Read/Write
- Permission flow testing (request → grant/deny → verify)
- Permission error handling and edge cases

### 3. Native Module Testing
- **VGuard Security Module**: SDK version, firmware version, VOS status, threat detection
- **Device Info Module**: Model, brand, system version, hardware capabilities
- **Permissions Module**: Permission checking and requesting
- **Storage Module**: AsyncStorage functionality
- Module availability and method signature validation

### 4. Device Capability Testing
- **Physical vs Emulator Detection**
- **Camera Hardware**: Availability, features, capabilities
- **GPS and Location**: GPS availability, location services
- **Screen Properties**: Dimensions, notch detection, Dynamic Island (iOS)
- **Biometric Support**: Fingerprint, Face ID, PIN detection
- **Platform-Specific Features**: iOS notch/Dynamic Island, Android API level/ABIs

### 5. UI and Interaction Testing
- Platform-specific styling and theming
- Status bar configuration (iOS vs Android)
- Navigation patterns (iOS push vs Android navigate)
- Safe area handling and notch adaptation
- Accessibility features (VoiceOver, TalkBack)
- Platform-specific animations and transitions

## Usage

### Running All Platform Tests

```bash
# Run comprehensive platform test suite
npm run test:platform

# Run platform tests for specific platform
npm run test:platform:ios
npm run test:platform:android
```

### Running Specific Test Suites

```bash
# Run permission tests only
npm run test:permissions

# Run native module tests only
npm run test:native-modules

# Run device capability tests only
npm run test:device-capabilities

# Run UI tests only
npm run test:ui
```

### Running Tests in Development

```bash
# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run basic platform tests
npm test __tests__/platform/PlatformBasicTests.test.js
```

## Test Configuration

### Jest Configuration
The platform tests use a custom Jest configuration with:
- React Native preset
- Custom test setup file
- Module name mapping for easy imports
- Coverage reporting
- Platform-specific mocking

### Mock Configuration
Comprehensive mocking for:
- React Native core modules
- react-native-permissions
- react-native-device-info
- Native modules (VGuard, AsyncStorage, etc.)
- Platform-specific APIs

## Platform Test Utils API

### Core Functions

```javascript
import PlatformTestUtils from './PlatformTestUtils';

// Get platform-specific permissions
const permissions = PlatformTestUtils.getPlatformPermissions();

// Check if running on physical device
const isPhysical = await PlatformTestUtils.isPhysicalDevice();

// Get comprehensive device capabilities
const capabilities = await PlatformTestUtils.getDeviceCapabilities();

// Test permission flow
const result = await PlatformTestUtils.testPermissionFlow(permission);

// Test native module availability
const moduleInfo = PlatformTestUtils.testNativeModuleAvailability('VGuardPlugin');

// Validate platform configuration
const validation = PlatformTestUtils.validatePlatformConfiguration();
```

### Device Capabilities

```javascript
const capabilities = await PlatformTestUtils.getDeviceCapabilities();

// Common capabilities
console.log(capabilities.platform);        // 'ios' or 'android'
console.log(capabilities.isPhysical);      // true/false
console.log(capabilities.hasCamera);       // true/false
console.log(capabilities.hasGPS);          // true/false
console.log(capabilities.screenDimensions); // {width, height}

// iOS-specific
if (capabilities.ios) {
  console.log(capabilities.ios.hasNotch);
  console.log(capabilities.ios.hasDynamicIsland);
  console.log(capabilities.ios.deviceType);
}

// Android-specific
if (capabilities.android) {
  console.log(capabilities.android.apiLevel);
  console.log(capabilities.android.supportedAbis);
  console.log(capabilities.android.securityPatch);
}
```

## Test Reports

The platform test runner generates comprehensive reports including:

- **Summary**: Total tests, pass/fail counts, success rate, duration
- **Platform Information**: OS version, device details, capabilities
- **Detailed Results**: Per-test results with timestamps and error details
- **Recommendations**: Actionable suggestions based on test results

Example report structure:
```json
{
  "summary": {
    "platform": "ios",
    "totalTests": 25,
    "passedTests": 23,
    "failedTests": 2,
    "successRate": 92,
    "duration": "1.2s"
  },
  "results": {
    "permissions": [...],
    "nativeModules": [...],
    "deviceCapabilities": [...],
    "uiTests": [...]
  },
  "recommendations": [
    {
      "type": "PERMISSION",
      "message": "Camera permission failed. Check Info.plist configuration.",
      "priority": "HIGH"
    }
  ]
}
```

## Best Practices

1. **Run Tests on Both Platforms**: Always test iOS and Android separately
2. **Use Physical Devices**: Some features only work on physical devices
3. **Mock Appropriately**: Use comprehensive mocks for consistent testing
4. **Handle Async Operations**: Use proper async/await patterns
5. **Test Error Cases**: Include permission denials and module failures
6. **Validate Configuration**: Ensure platform-specific setup is correct
7. **Monitor Performance**: Keep tests fast and reliable

## Troubleshooting

### Common Issues

1. **Permission Tests Failing**: Check Info.plist (iOS) or AndroidManifest.xml (Android)
2. **Native Module Not Found**: Verify module linking and installation
3. **Device Capability Detection**: May fail on emulators/simulators
4. **Test Timeouts**: Increase timeout for slow operations
5. **Mock Issues**: Ensure all required modules are properly mocked

### Debug Commands

```bash
# Run with verbose output
npm test -- --verbose __tests__/platform/

# Run with debug information
npm test -- --detectOpenHandles __tests__/platform/

# Run specific test with full output
npm test -- --no-coverage --verbose __tests__/platform/PlatformBasicTests.test.js
```

## Contributing

When adding new platform-specific features:

1. Add corresponding tests to the appropriate test file
2. Update PlatformTestUtils with new utility functions
3. Add platform-specific mocks to testSetup.js
4. Update this README with new functionality
5. Ensure tests pass on both iOS and Android

## Integration

The platform testing infrastructure integrates with:
- **CameraService**: Permission and capability testing
- **LocationService**: GPS and location permission testing
- **DeviceInfoService**: Device capability detection
- **VGuard Security**: Native module functionality testing
- **Redux Store**: State management testing
- **Logging System**: Test result logging and reporting
