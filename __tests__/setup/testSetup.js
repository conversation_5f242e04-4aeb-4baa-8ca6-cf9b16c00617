/**
 * Test Setup Configuration
 * Global setup for platform-specific testing
 */

import 'react-native-gesture-handler/jestSetup';

// Mock react-native modules
jest.mock('react-native', () => ({
    Platform: {
      OS: 'ios',
      Version: '17.0',
      select: jest.fn((obj) => obj.ios || obj.default),
    },
    NativeModules: {
      VGuardPlugin: {
        sdkVersion: jest.fn(() => Promise.resolve('1.0.0')),
        getFirmwareVersion: jest.fn(() => Promise.resolve('2.0.0')),
        getProcessorVersion: jest.fn(() => Promise.resolve('3.0.0')),
        isVosRunning: jest.fn(() => Promise.resolve(true)),
        getIsVosStarted: jest.fn(() => Promise.resolve(true)),
        invokeVGuard: jest.fn(() => Promise.resolve()),
        VGUARD_READY: 'VGUARD_READY',
        VGUARD_ERROR: 'VGUARD_ERROR',
        VGUARD_DEBUGGER_DETECTED: 'VGUARD_DEBUGGER_DETECTED',
        VGUARD_JAILBREAK_DETECTED: 'VGUARD_JAILBREAK_DETECTED',
        VGUARD_SCREEN_SHARING_DETECTED: 'VGUARD_SCREEN_SHARING_DETECTED',
        VGUARD_VIRTUAL_SPACE_DETECTED: 'VGUARD_VIRTUAL_SPACE_DETECTED',
        VGUARD_OVERLAY_DETECTED: 'VGUARD_OVERLAY_DETECTED',
      },
      RNDeviceInfo: {
        getModel: jest.fn(() => Promise.resolve('iPhone 14 Pro')),
        getBrand: jest.fn(() => Promise.resolve('Apple')),
        getSystemVersion: jest.fn(() => Promise.resolve('17.0')),
        getBuildNumber: jest.fn(() => Promise.resolve('21A329')),
        getApiLevel: jest.fn(() => Promise.resolve(34)),
        getScreenWidth: jest.fn(() => Promise.resolve(390)),
        getScreenHeight: jest.fn(() => Promise.resolve(844)),
        isEmulator: jest.fn(() => Promise.resolve(false)),
        hasNotch: jest.fn(() => true),
        hasDynamicIsland: jest.fn(() => false),
        getDeviceType: jest.fn(() => Promise.resolve('iPhone')),
        isFingerprintSupported: jest.fn(() => Promise.resolve(true)),
        supportedAbis: jest.fn(() => Promise.resolve(['arm64'])),
        getSecurityPatch: jest.fn(() => Promise.resolve('2023-10-01')),
        hasSystemFeature: jest.fn(() => Promise.resolve(true)),
        getManufacturer: jest.fn(() => Promise.resolve('Apple')),
        getHardware: jest.fn(() => Promise.resolve('iPhone14,3')),
        getProduct: jest.fn(() => Promise.resolve('iPhone OS')),
        getTags: jest.fn(() => Promise.resolve('release')),
        getType: jest.fn(() => Promise.resolve('user')),
        isPinOrFingerprintSet: jest.fn(() => Promise.resolve(true)),
      },
      RNPermissions: {
        checkPermission: jest.fn(() => Promise.resolve('granted')),
        requestPermission: jest.fn(() => Promise.resolve('granted')),
      },
      RNCAsyncStorage: {
        getItem: jest.fn(() => Promise.resolve(null)),
        setItem: jest.fn(() => Promise.resolve()),
        removeItem: jest.fn(() => Promise.resolve()),
        clear: jest.fn(() => Promise.resolve()),
      },
      RNGestureHandler: {
        attachGestureHandler: jest.fn(),
        createGestureHandler: jest.fn(),
        dropGestureHandler: jest.fn(),
        updateGestureHandler: jest.fn(),
      },
    },
    NativeEventEmitter: jest.fn().mockImplementation(() => ({
      addListener: jest.fn(() => ({ remove: jest.fn() })),
      removeListener: jest.fn(),
      removeAllListeners: jest.fn(),
    })),
    Alert: {
      alert: jest.fn(),
    },
    Dimensions: {
      get: jest.fn(() => ({ width: 375, height: 812 })),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    StatusBar: {
      setBarStyle: jest.fn(),
      setBackgroundColor: jest.fn(),
      setHidden: jest.fn(),
    },
    AppState: {
      currentState: 'active',
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    PermissionsAndroid: {
      PERMISSIONS: {
        CAMERA: 'android.permission.CAMERA',
        RECORD_AUDIO: 'android.permission.RECORD_AUDIO',
        ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
        WRITE_EXTERNAL_STORAGE: 'android.permission.WRITE_EXTERNAL_STORAGE',
      },
      RESULTS: {
        GRANTED: 'granted',
        DENIED: 'denied',
        NEVER_ASK_AGAIN: 'never_ask_again',
      },
      request: jest.fn(() => Promise.resolve('granted')),
      requestMultiple: jest.fn(() => Promise.resolve({})),
      check: jest.fn(() => Promise.resolve('granted')),
    },
}));

// Mock react-native-permissions
jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    IOS: {
      CAMERA: 'ios.permission.CAMERA',
      MICROPHONE: 'ios.permission.MICROPHONE',
      LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
      PHOTO_LIBRARY: 'ios.permission.PHOTO_LIBRARY',
      PHOTO_LIBRARY_ADD_ONLY: 'ios.permission.PHOTO_LIBRARY_ADD_ONLY',
    },
    ANDROID: {
      CAMERA: 'android.permission.CAMERA',
      RECORD_AUDIO: 'android.permission.RECORD_AUDIO',
      ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
      READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
      WRITE_EXTERNAL_STORAGE: 'android.permission.WRITE_EXTERNAL_STORAGE',
    },
  },
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
    BLOCKED: 'blocked',
    UNAVAILABLE: 'unavailable',
  },
  check: jest.fn(() => Promise.resolve('granted')),
  request: jest.fn(() => Promise.resolve('granted')),
  requestMultiple: jest.fn(() => Promise.resolve({})),
  openSettings: jest.fn(() => Promise.resolve()),
}));

// Mock react-native-device-info
jest.mock('react-native-device-info', () => ({
  getModel: jest.fn(() => Promise.resolve('iPhone 14 Pro')),
  getBrand: jest.fn(() => Promise.resolve('Apple')),
  getSystemVersion: jest.fn(() => Promise.resolve('17.0')),
  getBuildNumber: jest.fn(() => Promise.resolve('21A329')),
  getApiLevel: jest.fn(() => Promise.resolve(34)),
  getScreenWidth: jest.fn(() => 390),
  getScreenHeight: jest.fn(() => 844),
  isEmulator: jest.fn(() => Promise.resolve(false)),
  hasNotch: jest.fn(() => true),
  hasDynamicIsland: jest.fn(() => false),
  getDeviceType: jest.fn(() => Promise.resolve('iPhone')),
  isFingerprintSupported: jest.fn(() => Promise.resolve(true)),
  supportedAbis: jest.fn(() => Promise.resolve(['arm64'])),
  getSecurityPatch: jest.fn(() => Promise.resolve('2023-10-01')),
  hasSystemFeature: jest.fn(() => Promise.resolve(true)),
  getManufacturer: jest.fn(() => Promise.resolve('Apple')),
  getHardware: jest.fn(() => Promise.resolve('iPhone14,3')),
  getProduct: jest.fn(() => Promise.resolve('iPhone OS')),
  getTags: jest.fn(() => Promise.resolve('release')),
  getType: jest.fn(() => Promise.resolve('user')),
  isPinOrFingerprintSet: jest.fn(() => Promise.resolve(true)),
}));

// Mock react-native-fs
jest.mock('react-native-fs', () => ({
  DocumentDirectoryPath: '/mock/documents',
  CachesDirectoryPath: '/mock/caches',
  writeFile: jest.fn(() => Promise.resolve()),
  readFile: jest.fn(() => Promise.resolve('')),
  exists: jest.fn(() => Promise.resolve(true)),
  mkdir: jest.fn(() => Promise.resolve()),
  unlink: jest.fn(() => Promise.resolve()),
}));

// Mock @react-native-async-storage/async-storage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
  getAllKeys: jest.fn(() => Promise.resolve([])),
  multiGet: jest.fn(() => Promise.resolve([])),
  multiSet: jest.fn(() => Promise.resolve()),
  multiRemove: jest.fn(() => Promise.resolve()),
}));

// Mock @react-native-community/geolocation
jest.mock('@react-native-community/geolocation', () => ({
  getCurrentPosition: jest.fn((success) => {
    success({
      coords: {
        latitude: 37.7749,
        longitude: -122.4194,
        accuracy: 10,
        altitude: 0,
        altitudeAccuracy: 0,
        heading: 0,
        speed: 0,
      },
      timestamp: Date.now(),
    });
  }),
  watchPosition: jest.fn(() => 1),
  clearWatch: jest.fn(),
}));

// Mock react-native-image-picker
jest.mock('react-native-image-picker', () => ({
  launchImageLibrary: jest.fn((options, callback) => {
    callback({
      assets: [{
        uri: 'mock://image.jpg',
        type: 'image/jpeg',
        fileName: 'image.jpg',
        fileSize: 1024,
      }],
    });
  }),
  launchCamera: jest.fn((options, callback) => {
    callback({
      assets: [{
        uri: 'mock://camera.jpg',
        type: 'image/jpeg',
        fileName: 'camera.jpg',
        fileSize: 2048,
      }],
    });
  }),
}));

// Global test utilities
global.mockPlatform = (platform) => {
  const RN = require('react-native');
  RN.Platform.OS = platform;
  RN.Platform.select.mockImplementation((obj) => obj[platform] || obj.default);
};

global.resetMocks = () => {
  jest.clearAllMocks();
};

// Console suppression for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning:') || args[0].includes('React Native'))
  ) {
    return;
  }
  originalConsoleError(...args);
};

console.warn = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning:') || args[0].includes('React Native'))
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

// Set default timeout for async operations
jest.setTimeout(30000);
