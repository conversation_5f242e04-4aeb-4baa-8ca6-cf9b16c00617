# Large Images Directory

This directory contains large image assets for the React Native Camera App.

## Asset Requirements
- 25+ high-resolution images (5-10MB each)
- Total target size: ~150MB for images
- Formats: JPG, PNG, WebP
- Resolutions: 4K, 8K for maximum size

## Usage
Images are loaded and cached by the AssetManager utility.
They are used throughout the app for:
- Background images
- Camera overlay effects
- UI elements
- Sample gallery content

## Performance Notes
- Images are lazy-loaded to prevent memory issues
- Caching system prevents repeated loading
- Compression is applied where appropriate
- Memory management is handled automatically

The actual large image files would be placed here in a production environment.
For development, the AssetManager generates simulated large image data.
