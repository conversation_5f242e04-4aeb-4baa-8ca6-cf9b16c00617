// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* TestVersion0753Tests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* TestVersion0753Tests.m */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		3972E461F71CDC5D27A393C9 /* libPods-TestVersion0753-TestVersion0753Tests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 04F09DE2B2A71E49338E77A4 /* libPods-TestVersion0753-TestVersion0753Tests.a */; };
		7E16DB95E6483117E18C9F99 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 063CED34B38D9B1F87AB2EFD /* PrivacyInfo.xcprivacy */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		8C7824252CC1FF3F006EEE72 /* firmware in Resources */ = {isa = PBXBuildFile; fileRef = 8C78241E2CC1FF3F006EEE72 /* firmware */; };
		8C7824262CC1FF3F006EEE72 /* manifest_CAQUhsiQrTD.json in Resources */ = {isa = PBXBuildFile; fileRef = 8C78241F2CC1FF3F006EEE72 /* manifest_CAQUhsiQrTD.json */; };
		8C7824272CC1FF3F006EEE72 /* vkeylicensepack in Resources */ = {isa = PBXBuildFile; fileRef = 8C7824202CC1FF3F006EEE72 /* vkeylicensepack */; };
		8C7824282CC1FF3F006EEE72 /* signature in Resources */ = {isa = PBXBuildFile; fileRef = 8C7824212CC1FF3F006EEE72 /* signature */; };
		8C7824292CC1FF3F006EEE72 /* crypto_ta.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8C7824222CC1FF3F006EEE72 /* crypto_ta.bin */; };
		8C78242A2CC1FF3F006EEE72 /* profile in Resources */ = {isa = PBXBuildFile; fileRef = 8C7824232CC1FF3F006EEE72 /* profile */; };
		8C78242B2CC1FF3F006EEE72 /* tla_enc.cer in Resources */ = {isa = PBXBuildFile; fileRef = 8C7824242CC1FF3F006EEE72 /* tla_enc.cer */; };
		8CD436812E7C325F0057D64B /* large_data_42.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436502E7C325F0057D64B /* large_data_42.bin */; };
		8CD436822E7C325F0057D64B /* large_data_1.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436272E7C325F0057D64B /* large_data_1.bin */; };
		8CD436832E7C325F0057D64B /* large_data_2.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436282E7C325F0057D64B /* large_data_2.bin */; };
		8CD436842E7C325F0057D64B /* large_data_18.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436382E7C325F0057D64B /* large_data_18.bin */; };
		8CD436852E7C325F0057D64B /* large_image_13.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366B2E7C325F0057D64B /* large_image_13.jpg */; };
		8CD436862E7C325F0057D64B /* large_data_46.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436542E7C325F0057D64B /* large_data_46.bin */; };
		8CD436872E7C325F0057D64B /* large_data_44.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436522E7C325F0057D64B /* large_data_44.bin */; };
		8CD436882E7C325F0057D64B /* large_data_12.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436322E7C325F0057D64B /* large_data_12.bin */; };
		8CD436892E7C325F0057D64B /* large_data_6.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362C2E7C325F0057D64B /* large_data_6.bin */; };
		8CD4368A2E7C325F0057D64B /* large_image_11.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436692E7C325F0057D64B /* large_image_11.jpg */; };
		8CD4368B2E7C325F0057D64B /* large_image_17.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366F2E7C325F0057D64B /* large_image_17.jpg */; };
		8CD4368C2E7C325F0057D64B /* large_data_28.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436422E7C325F0057D64B /* large_data_28.bin */; };
		8CD4368D2E7C325F0057D64B /* large_data_3.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436292E7C325F0057D64B /* large_data_3.bin */; };
		8CD4368E2E7C325F0057D64B /* large_data_7.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362D2E7C325F0057D64B /* large_data_7.bin */; };
		8CD4368F2E7C325F0057D64B /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436732E7C325F0057D64B /* README.md */; };
		8CD436902E7C325F0057D64B /* large_data_14.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436342E7C325F0057D64B /* large_data_14.bin */; };
		8CD436912E7C325F0057D64B /* large_video_5.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436792E7C325F0057D64B /* large_video_5.mp4 */; };
		8CD436922E7C325F0057D64B /* large_database.db in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436592E7C325F0057D64B /* large_database.db */; };
		8CD436932E7C325F0057D64B /* large_video_1.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436752E7C325F0057D64B /* large_video_1.mp4 */; };
		8CD436942E7C325F0057D64B /* large_data_43.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436512E7C325F0057D64B /* large_data_43.bin */; };
		8CD436952E7C325F0057D64B /* large_data_40.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364E2E7C325F0057D64B /* large_data_40.bin */; };
		8CD436962E7C325F0057D64B /* large_data_48.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436562E7C325F0057D64B /* large_data_48.bin */; };
		8CD436972E7C325F0057D64B /* large_data_27.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436412E7C325F0057D64B /* large_data_27.bin */; };
		8CD436982E7C325F0057D64B /* large_data_8.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362E2E7C325F0057D64B /* large_data_8.bin */; };
		8CD436992E7C325F0057D64B /* large_data_21.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363B2E7C325F0057D64B /* large_data_21.bin */; };
		8CD4369A2E7C325F0057D64B /* large_video_9.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367D2E7C325F0057D64B /* large_video_9.mp4 */; };
		8CD4369B2E7C325F0057D64B /* large_data_23.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363D2E7C325F0057D64B /* large_data_23.bin */; };
		8CD4369C2E7C325F0057D64B /* large_data_26.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436402E7C325F0057D64B /* large_data_26.bin */; };
		8CD4369D2E7C325F0057D64B /* large_data_49.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436572E7C325F0057D64B /* large_data_49.bin */; };
		8CD4369E2E7C325F0057D64B /* large_data_13.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436332E7C325F0057D64B /* large_data_13.bin */; };
		8CD4369F2E7C325F0057D64B /* large_data_10.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436302E7C325F0057D64B /* large_data_10.bin */; };
		8CD436A02E7C325F0057D64B /* large_data_47.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436552E7C325F0057D64B /* large_data_47.bin */; };
		8CD436A12E7C325F0057D64B /* large_data_4.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362A2E7C325F0057D64B /* large_data_4.bin */; };
		8CD436A22E7C325F0057D64B /* sample_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436232E7C325F0057D64B /* sample_animation.json */; };
		8CD436A32E7C325F0057D64B /* large_data_22.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363C2E7C325F0057D64B /* large_data_22.bin */; };
		8CD436A42E7C325F0057D64B /* large_data_32.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436462E7C325F0057D64B /* large_data_32.bin */; };
		8CD436A52E7C325F0057D64B /* large_audio_1.wav in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436252E7C325F0057D64B /* large_audio_1.wav */; };
		8CD436A62E7C325F0057D64B /* large_image_10.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436682E7C325F0057D64B /* large_image_10.jpg */; };
		8CD436A72E7C325F0057D64B /* large_json_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4365A2E7C325F0057D64B /* large_json_data.json */; };
		8CD436A82E7C325F0057D64B /* large_data_41.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364F2E7C325F0057D64B /* large_data_41.bin */; };
		8CD436A92E7C325F0057D64B /* large_data_16.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436362E7C325F0057D64B /* large_data_16.bin */; };
		8CD436AA2E7C325F0057D64B /* large_image_3.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436612E7C325F0057D64B /* large_image_3.jpg */; };
		8CD436AB2E7C325F0057D64B /* large_image_19.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436712E7C325F0057D64B /* large_image_19.jpg */; };
		8CD436AC2E7C325F0057D64B /* large_data_19.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436392E7C325F0057D64B /* large_data_19.bin */; };
		8CD436AD2E7C325F0057D64B /* large_video_10.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367E2E7C325F0057D64B /* large_video_10.mp4 */; };
		8CD436AE2E7C325F0057D64B /* large_video_7.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367B2E7C325F0057D64B /* large_video_7.mp4 */; };
		8CD436AF2E7C325F0057D64B /* large_image_20.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436722E7C325F0057D64B /* large_image_20.jpg */; };
		8CD436B02E7C325F0057D64B /* large_video_8.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367C2E7C325F0057D64B /* large_video_8.mp4 */; };
		8CD436B12E7C325F0057D64B /* large_data_11.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436312E7C325F0057D64B /* large_data_11.bin */; };
		8CD436B22E7C325F0057D64B /* large_image_4.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436622E7C325F0057D64B /* large_image_4.jpg */; };
		8CD436B32E7C325F0057D64B /* large_data_9.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362F2E7C325F0057D64B /* large_data_9.bin */; };
		8CD436B42E7C325F0057D64B /* large_image_1.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4365F2E7C325F0057D64B /* large_image_1.jpg */; };
		8CD436B52E7C325F0057D64B /* large_video_4.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436782E7C325F0057D64B /* large_video_4.mp4 */; };
		8CD436B62E7C325F0057D64B /* large_data_17.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436372E7C325F0057D64B /* large_data_17.bin */; };
		8CD436B72E7C325F0057D64B /* large_data_45.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436532E7C325F0057D64B /* large_data_45.bin */; };
		8CD436B82E7C325F0057D64B /* large_data_37.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364B2E7C325F0057D64B /* large_data_37.bin */; };
		8CD436B92E7C325F0057D64B /* large_image_12.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366A2E7C325F0057D64B /* large_image_12.jpg */; };
		8CD436BA2E7C325F0057D64B /* large_data_25.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363F2E7C325F0057D64B /* large_data_25.bin */; };
		8CD436BB2E7C325F0057D64B /* large_data_20.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363A2E7C325F0057D64B /* large_data_20.bin */; };
		8CD436BC2E7C325F0057D64B /* large_data_31.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436452E7C325F0057D64B /* large_data_31.bin */; };
		8CD436BD2E7C325F0057D64B /* large_data_36.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364A2E7C325F0057D64B /* large_data_36.bin */; };
		8CD436BE2E7C325F0057D64B /* large_image_14.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366C2E7C325F0057D64B /* large_image_14.jpg */; };
		8CD436BF2E7C325F0057D64B /* large_image_6.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436642E7C325F0057D64B /* large_image_6.jpg */; };
		8CD436C02E7C325F0057D64B /* large_image_5.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436632E7C325F0057D64B /* large_image_5.jpg */; };
		8CD436C12E7C325F0057D64B /* large_image_9.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436672E7C325F0057D64B /* large_image_9.jpg */; };
		8CD436C22E7C325F0057D64B /* large_data_38.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364C2E7C325F0057D64B /* large_data_38.bin */; };
		8CD436C32E7C325F0057D64B /* large_video_2.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436762E7C325F0057D64B /* large_video_2.mp4 */; };
		8CD436C42E7C325F0057D64B /* large_video_6.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367A2E7C325F0057D64B /* large_video_6.mp4 */; };
		8CD436C52E7C325F0057D64B /* comprehensive_documentation.md in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4365C2E7C325F0057D64B /* comprehensive_documentation.md */; };
		8CD436C62E7C325F0057D64B /* large_image_2.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436602E7C325F0057D64B /* large_image_2.jpg */; };
		8CD436C72E7C325F0057D64B /* large_data_50.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436582E7C325F0057D64B /* large_data_50.bin */; };
		8CD436C82E7C325F0057D64B /* large_data_24.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363E2E7C325F0057D64B /* large_data_24.bin */; };
		8CD436C92E7C325F0057D64B /* large_image_7.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436652E7C325F0057D64B /* large_image_7.jpg */; };
		8CD436CA2E7C325F0057D64B /* large_video_3.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436772E7C325F0057D64B /* large_video_3.mp4 */; };
		8CD436CB2E7C325F0057D64B /* large_image_8.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436662E7C325F0057D64B /* large_image_8.jpg */; };
		8CD436CC2E7C325F0057D64B /* large_image_15.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366D2E7C325F0057D64B /* large_image_15.jpg */; };
		8CD436CD2E7C325F0057D64B /* large_data_29.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436432E7C325F0057D64B /* large_data_29.bin */; };
		8CD436CE2E7C325F0057D64B /* large_image_16.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366E2E7C325F0057D64B /* large_image_16.jpg */; };
		8CD436CF2E7C325F0057D64B /* large_data_34.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436482E7C325F0057D64B /* large_data_34.bin */; };
		8CD436D02E7C325F0057D64B /* large_data_30.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436442E7C325F0057D64B /* large_data_30.bin */; };
		8CD436D12E7C325F0057D64B /* large_data_35.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436492E7C325F0057D64B /* large_data_35.bin */; };
		8CD436D22E7C325F0057D64B /* large_data_33.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436472E7C325F0057D64B /* large_data_33.bin */; };
		8CD436D32E7C325F0057D64B /* large_data_15.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436352E7C325F0057D64B /* large_data_15.bin */; };
		8CD436D42E7C325F0057D64B /* large_data_39.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364D2E7C325F0057D64B /* large_data_39.bin */; };
		8CD436D52E7C325F0057D64B /* large_image_18.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436702E7C325F0057D64B /* large_image_18.jpg */; };
		8CD436D62E7C325F0057D64B /* large_data_5.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362B2E7C325F0057D64B /* large_data_5.bin */; };
		8CD436D72E7C325F0057D64B /* large_data_42.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436502E7C325F0057D64B /* large_data_42.bin */; };
		8CD436D82E7C325F0057D64B /* large_data_1.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436272E7C325F0057D64B /* large_data_1.bin */; };
		8CD436D92E7C325F0057D64B /* large_data_2.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436282E7C325F0057D64B /* large_data_2.bin */; };
		8CD436DA2E7C325F0057D64B /* large_data_18.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436382E7C325F0057D64B /* large_data_18.bin */; };
		8CD436DB2E7C325F0057D64B /* large_image_13.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366B2E7C325F0057D64B /* large_image_13.jpg */; };
		8CD436DC2E7C325F0057D64B /* large_data_46.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436542E7C325F0057D64B /* large_data_46.bin */; };
		8CD436DD2E7C325F0057D64B /* large_data_44.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436522E7C325F0057D64B /* large_data_44.bin */; };
		8CD436DE2E7C325F0057D64B /* large_data_12.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436322E7C325F0057D64B /* large_data_12.bin */; };
		8CD436DF2E7C325F0057D64B /* large_data_6.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362C2E7C325F0057D64B /* large_data_6.bin */; };
		8CD436E02E7C325F0057D64B /* large_image_11.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436692E7C325F0057D64B /* large_image_11.jpg */; };
		8CD436E12E7C325F0057D64B /* large_image_17.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366F2E7C325F0057D64B /* large_image_17.jpg */; };
		8CD436E22E7C325F0057D64B /* large_data_28.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436422E7C325F0057D64B /* large_data_28.bin */; };
		8CD436E32E7C325F0057D64B /* large_data_3.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436292E7C325F0057D64B /* large_data_3.bin */; };
		8CD436E42E7C325F0057D64B /* large_data_7.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362D2E7C325F0057D64B /* large_data_7.bin */; };
		8CD436E52E7C325F0057D64B /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436732E7C325F0057D64B /* README.md */; };
		8CD436E62E7C325F0057D64B /* large_data_14.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436342E7C325F0057D64B /* large_data_14.bin */; };
		8CD436E72E7C325F0057D64B /* large_video_5.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436792E7C325F0057D64B /* large_video_5.mp4 */; };
		8CD436E82E7C325F0057D64B /* large_database.db in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436592E7C325F0057D64B /* large_database.db */; };
		8CD436E92E7C325F0057D64B /* large_video_1.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436752E7C325F0057D64B /* large_video_1.mp4 */; };
		8CD436EA2E7C325F0057D64B /* large_data_43.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436512E7C325F0057D64B /* large_data_43.bin */; };
		8CD436EB2E7C325F0057D64B /* large_data_40.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364E2E7C325F0057D64B /* large_data_40.bin */; };
		8CD436EC2E7C325F0057D64B /* large_data_48.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436562E7C325F0057D64B /* large_data_48.bin */; };
		8CD436ED2E7C325F0057D64B /* large_data_27.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436412E7C325F0057D64B /* large_data_27.bin */; };
		8CD436EE2E7C325F0057D64B /* large_data_8.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362E2E7C325F0057D64B /* large_data_8.bin */; };
		8CD436EF2E7C325F0057D64B /* large_data_21.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363B2E7C325F0057D64B /* large_data_21.bin */; };
		8CD436F02E7C325F0057D64B /* large_video_9.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367D2E7C325F0057D64B /* large_video_9.mp4 */; };
		8CD436F12E7C325F0057D64B /* large_data_23.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363D2E7C325F0057D64B /* large_data_23.bin */; };
		8CD436F22E7C325F0057D64B /* large_data_26.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436402E7C325F0057D64B /* large_data_26.bin */; };
		8CD436F32E7C325F0057D64B /* large_data_49.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436572E7C325F0057D64B /* large_data_49.bin */; };
		8CD436F42E7C325F0057D64B /* large_data_13.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436332E7C325F0057D64B /* large_data_13.bin */; };
		8CD436F52E7C325F0057D64B /* large_data_10.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436302E7C325F0057D64B /* large_data_10.bin */; };
		8CD436F62E7C325F0057D64B /* large_data_47.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436552E7C325F0057D64B /* large_data_47.bin */; };
		8CD436F72E7C325F0057D64B /* large_data_4.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362A2E7C325F0057D64B /* large_data_4.bin */; };
		8CD436F82E7C325F0057D64B /* sample_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436232E7C325F0057D64B /* sample_animation.json */; };
		8CD436F92E7C325F0057D64B /* large_data_22.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363C2E7C325F0057D64B /* large_data_22.bin */; };
		8CD436FA2E7C325F0057D64B /* large_data_32.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436462E7C325F0057D64B /* large_data_32.bin */; };
		8CD436FB2E7C325F0057D64B /* large_audio_1.wav in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436252E7C325F0057D64B /* large_audio_1.wav */; };
		8CD436FC2E7C325F0057D64B /* large_image_10.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436682E7C325F0057D64B /* large_image_10.jpg */; };
		8CD436FD2E7C325F0057D64B /* large_json_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4365A2E7C325F0057D64B /* large_json_data.json */; };
		8CD436FE2E7C325F0057D64B /* large_data_41.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364F2E7C325F0057D64B /* large_data_41.bin */; };
		8CD436FF2E7C325F0057D64B /* large_data_16.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436362E7C325F0057D64B /* large_data_16.bin */; };
		8CD437002E7C325F0057D64B /* large_image_3.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436612E7C325F0057D64B /* large_image_3.jpg */; };
		8CD437012E7C325F0057D64B /* large_image_19.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436712E7C325F0057D64B /* large_image_19.jpg */; };
		8CD437022E7C325F0057D64B /* large_data_19.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436392E7C325F0057D64B /* large_data_19.bin */; };
		8CD437032E7C325F0057D64B /* large_video_10.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367E2E7C325F0057D64B /* large_video_10.mp4 */; };
		8CD437042E7C325F0057D64B /* large_video_7.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367B2E7C325F0057D64B /* large_video_7.mp4 */; };
		8CD437052E7C325F0057D64B /* large_image_20.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436722E7C325F0057D64B /* large_image_20.jpg */; };
		8CD437062E7C325F0057D64B /* large_video_8.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367C2E7C325F0057D64B /* large_video_8.mp4 */; };
		8CD437072E7C325F0057D64B /* large_data_11.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436312E7C325F0057D64B /* large_data_11.bin */; };
		8CD437082E7C325F0057D64B /* large_image_4.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436622E7C325F0057D64B /* large_image_4.jpg */; };
		8CD437092E7C325F0057D64B /* large_data_9.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362F2E7C325F0057D64B /* large_data_9.bin */; };
		8CD4370A2E7C325F0057D64B /* large_image_1.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4365F2E7C325F0057D64B /* large_image_1.jpg */; };
		8CD4370B2E7C325F0057D64B /* large_video_4.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436782E7C325F0057D64B /* large_video_4.mp4 */; };
		8CD4370C2E7C325F0057D64B /* large_data_17.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436372E7C325F0057D64B /* large_data_17.bin */; };
		8CD4370D2E7C325F0057D64B /* large_data_45.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436532E7C325F0057D64B /* large_data_45.bin */; };
		8CD4370E2E7C325F0057D64B /* large_data_37.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364B2E7C325F0057D64B /* large_data_37.bin */; };
		8CD4370F2E7C325F0057D64B /* large_image_12.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366A2E7C325F0057D64B /* large_image_12.jpg */; };
		8CD437102E7C325F0057D64B /* large_data_25.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363F2E7C325F0057D64B /* large_data_25.bin */; };
		8CD437112E7C325F0057D64B /* large_data_20.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363A2E7C325F0057D64B /* large_data_20.bin */; };
		8CD437122E7C325F0057D64B /* large_data_31.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436452E7C325F0057D64B /* large_data_31.bin */; };
		8CD437132E7C325F0057D64B /* large_data_36.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364A2E7C325F0057D64B /* large_data_36.bin */; };
		8CD437142E7C325F0057D64B /* large_image_14.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366C2E7C325F0057D64B /* large_image_14.jpg */; };
		8CD437152E7C325F0057D64B /* large_image_6.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436642E7C325F0057D64B /* large_image_6.jpg */; };
		8CD437162E7C325F0057D64B /* large_image_5.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436632E7C325F0057D64B /* large_image_5.jpg */; };
		8CD437172E7C325F0057D64B /* large_image_9.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436672E7C325F0057D64B /* large_image_9.jpg */; };
		8CD437182E7C325F0057D64B /* large_data_38.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364C2E7C325F0057D64B /* large_data_38.bin */; };
		8CD437192E7C325F0057D64B /* large_video_2.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436762E7C325F0057D64B /* large_video_2.mp4 */; };
		8CD4371A2E7C325F0057D64B /* large_video_6.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4367A2E7C325F0057D64B /* large_video_6.mp4 */; };
		8CD4371B2E7C325F0057D64B /* comprehensive_documentation.md in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4365C2E7C325F0057D64B /* comprehensive_documentation.md */; };
		8CD4371C2E7C325F0057D64B /* large_image_2.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436602E7C325F0057D64B /* large_image_2.jpg */; };
		8CD4371D2E7C325F0057D64B /* large_data_50.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436582E7C325F0057D64B /* large_data_50.bin */; };
		8CD4371E2E7C325F0057D64B /* large_data_24.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4363E2E7C325F0057D64B /* large_data_24.bin */; };
		8CD4371F2E7C325F0057D64B /* large_image_7.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436652E7C325F0057D64B /* large_image_7.jpg */; };
		8CD437202E7C325F0057D64B /* large_video_3.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436772E7C325F0057D64B /* large_video_3.mp4 */; };
		8CD437212E7C325F0057D64B /* large_image_8.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436662E7C325F0057D64B /* large_image_8.jpg */; };
		8CD437222E7C325F0057D64B /* large_image_15.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366D2E7C325F0057D64B /* large_image_15.jpg */; };
		8CD437232E7C325F0057D64B /* large_data_29.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436432E7C325F0057D64B /* large_data_29.bin */; };
		8CD437242E7C325F0057D64B /* large_image_16.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4366E2E7C325F0057D64B /* large_image_16.jpg */; };
		8CD437252E7C325F0057D64B /* large_data_34.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436482E7C325F0057D64B /* large_data_34.bin */; };
		8CD437262E7C325F0057D64B /* large_data_30.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436442E7C325F0057D64B /* large_data_30.bin */; };
		8CD437272E7C325F0057D64B /* large_data_35.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436492E7C325F0057D64B /* large_data_35.bin */; };
		8CD437282E7C325F0057D64B /* large_data_33.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436472E7C325F0057D64B /* large_data_33.bin */; };
		8CD437292E7C325F0057D64B /* large_data_15.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436352E7C325F0057D64B /* large_data_15.bin */; };
		8CD4372A2E7C325F0057D64B /* large_data_39.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4364D2E7C325F0057D64B /* large_data_39.bin */; };
		8CD4372B2E7C325F0057D64B /* large_image_18.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 8CD436702E7C325F0057D64B /* large_image_18.jpg */; };
		8CD4372C2E7C325F0057D64B /* large_data_5.bin in Resources */ = {isa = PBXBuildFile; fileRef = 8CD4362B2E7C325F0057D64B /* large_data_5.bin */; };
		FB55B13C107B29F626844BC8 /* libPods-TestVersion0753.a in Frameworks */ = {isa = PBXBuildFile; fileRef = D6816A5D5E212F1E4542D556 /* libPods-TestVersion0753.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = TestVersion0753;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* TestVersion0753Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TestVersion0753Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* TestVersion0753Tests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TestVersion0753Tests.m; sourceTree = "<group>"; };
		04F09DE2B2A71E49338E77A4 /* libPods-TestVersion0753-TestVersion0753Tests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-TestVersion0753-TestVersion0753Tests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		063CED34B38D9B1F87AB2EFD /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = TestVersion0753/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* TestVersion0753.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TestVersion0753.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = TestVersion0753/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = TestVersion0753/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = TestVersion0753/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = TestVersion0753/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = TestVersion0753/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = TestVersion0753/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		1E29F4FC13D7560130D6CAFF /* Pods-TestVersion0753.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TestVersion0753.debug.xcconfig"; path = "Target Support Files/Pods-TestVersion0753/Pods-TestVersion0753.debug.xcconfig"; sourceTree = "<group>"; };
		55258953BF2DC653B2ECBE87 /* Pods-TestVersion0753-TestVersion0753Tests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TestVersion0753-TestVersion0753Tests.release.xcconfig"; path = "Target Support Files/Pods-TestVersion0753-TestVersion0753Tests/Pods-TestVersion0753-TestVersion0753Tests.release.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = TestVersion0753/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8C78241E2CC1FF3F006EEE72 /* firmware */ = {isa = PBXFileReference; lastKnownFileType = text; name = firmware; path = assets/firmware; sourceTree = "<group>"; };
		8C78241F2CC1FF3F006EEE72 /* manifest_CAQUhsiQrTD.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; name = manifest_CAQUhsiQrTD.json; path = assets/manifest_CAQUhsiQrTD.json; sourceTree = "<group>"; };
		8C7824202CC1FF3F006EEE72 /* vkeylicensepack */ = {isa = PBXFileReference; lastKnownFileType = file; name = vkeylicensepack; path = assets/vkeylicensepack; sourceTree = "<group>"; };
		8C7824212CC1FF3F006EEE72 /* signature */ = {isa = PBXFileReference; lastKnownFileType = text; name = signature; path = assets/signature; sourceTree = "<group>"; };
		8C7824222CC1FF3F006EEE72 /* crypto_ta.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; name = crypto_ta.bin; path = assets/crypto_ta.bin; sourceTree = "<group>"; };
		8C7824232CC1FF3F006EEE72 /* profile */ = {isa = PBXFileReference; lastKnownFileType = text; name = profile; path = assets/profile; sourceTree = "<group>"; };
		8C7824242CC1FF3F006EEE72 /* tla_enc.cer */ = {isa = PBXFileReference; lastKnownFileType = file; name = tla_enc.cer; path = assets/tla_enc.cer; sourceTree = "<group>"; };
		8CD436232E7C325F0057D64B /* sample_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = sample_animation.json; sourceTree = "<group>"; };
		8CD436252E7C325F0057D64B /* large_audio_1.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = large_audio_1.wav; sourceTree = "<group>"; };
		8CD436272E7C325F0057D64B /* large_data_1.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_1.bin; sourceTree = "<group>"; };
		8CD436282E7C325F0057D64B /* large_data_2.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_2.bin; sourceTree = "<group>"; };
		8CD436292E7C325F0057D64B /* large_data_3.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_3.bin; sourceTree = "<group>"; };
		8CD4362A2E7C325F0057D64B /* large_data_4.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_4.bin; sourceTree = "<group>"; };
		8CD4362B2E7C325F0057D64B /* large_data_5.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_5.bin; sourceTree = "<group>"; };
		8CD4362C2E7C325F0057D64B /* large_data_6.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_6.bin; sourceTree = "<group>"; };
		8CD4362D2E7C325F0057D64B /* large_data_7.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_7.bin; sourceTree = "<group>"; };
		8CD4362E2E7C325F0057D64B /* large_data_8.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_8.bin; sourceTree = "<group>"; };
		8CD4362F2E7C325F0057D64B /* large_data_9.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_9.bin; sourceTree = "<group>"; };
		8CD436302E7C325F0057D64B /* large_data_10.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_10.bin; sourceTree = "<group>"; };
		8CD436312E7C325F0057D64B /* large_data_11.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_11.bin; sourceTree = "<group>"; };
		8CD436322E7C325F0057D64B /* large_data_12.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_12.bin; sourceTree = "<group>"; };
		8CD436332E7C325F0057D64B /* large_data_13.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_13.bin; sourceTree = "<group>"; };
		8CD436342E7C325F0057D64B /* large_data_14.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_14.bin; sourceTree = "<group>"; };
		8CD436352E7C325F0057D64B /* large_data_15.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_15.bin; sourceTree = "<group>"; };
		8CD436362E7C325F0057D64B /* large_data_16.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_16.bin; sourceTree = "<group>"; };
		8CD436372E7C325F0057D64B /* large_data_17.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_17.bin; sourceTree = "<group>"; };
		8CD436382E7C325F0057D64B /* large_data_18.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_18.bin; sourceTree = "<group>"; };
		8CD436392E7C325F0057D64B /* large_data_19.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_19.bin; sourceTree = "<group>"; };
		8CD4363A2E7C325F0057D64B /* large_data_20.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_20.bin; sourceTree = "<group>"; };
		8CD4363B2E7C325F0057D64B /* large_data_21.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_21.bin; sourceTree = "<group>"; };
		8CD4363C2E7C325F0057D64B /* large_data_22.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_22.bin; sourceTree = "<group>"; };
		8CD4363D2E7C325F0057D64B /* large_data_23.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_23.bin; sourceTree = "<group>"; };
		8CD4363E2E7C325F0057D64B /* large_data_24.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_24.bin; sourceTree = "<group>"; };
		8CD4363F2E7C325F0057D64B /* large_data_25.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_25.bin; sourceTree = "<group>"; };
		8CD436402E7C325F0057D64B /* large_data_26.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_26.bin; sourceTree = "<group>"; };
		8CD436412E7C325F0057D64B /* large_data_27.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_27.bin; sourceTree = "<group>"; };
		8CD436422E7C325F0057D64B /* large_data_28.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_28.bin; sourceTree = "<group>"; };
		8CD436432E7C325F0057D64B /* large_data_29.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_29.bin; sourceTree = "<group>"; };
		8CD436442E7C325F0057D64B /* large_data_30.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_30.bin; sourceTree = "<group>"; };
		8CD436452E7C325F0057D64B /* large_data_31.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_31.bin; sourceTree = "<group>"; };
		8CD436462E7C325F0057D64B /* large_data_32.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_32.bin; sourceTree = "<group>"; };
		8CD436472E7C325F0057D64B /* large_data_33.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_33.bin; sourceTree = "<group>"; };
		8CD436482E7C325F0057D64B /* large_data_34.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_34.bin; sourceTree = "<group>"; };
		8CD436492E7C325F0057D64B /* large_data_35.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_35.bin; sourceTree = "<group>"; };
		8CD4364A2E7C325F0057D64B /* large_data_36.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_36.bin; sourceTree = "<group>"; };
		8CD4364B2E7C325F0057D64B /* large_data_37.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_37.bin; sourceTree = "<group>"; };
		8CD4364C2E7C325F0057D64B /* large_data_38.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_38.bin; sourceTree = "<group>"; };
		8CD4364D2E7C325F0057D64B /* large_data_39.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_39.bin; sourceTree = "<group>"; };
		8CD4364E2E7C325F0057D64B /* large_data_40.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_40.bin; sourceTree = "<group>"; };
		8CD4364F2E7C325F0057D64B /* large_data_41.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_41.bin; sourceTree = "<group>"; };
		8CD436502E7C325F0057D64B /* large_data_42.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_42.bin; sourceTree = "<group>"; };
		8CD436512E7C325F0057D64B /* large_data_43.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_43.bin; sourceTree = "<group>"; };
		8CD436522E7C325F0057D64B /* large_data_44.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_44.bin; sourceTree = "<group>"; };
		8CD436532E7C325F0057D64B /* large_data_45.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_45.bin; sourceTree = "<group>"; };
		8CD436542E7C325F0057D64B /* large_data_46.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_46.bin; sourceTree = "<group>"; };
		8CD436552E7C325F0057D64B /* large_data_47.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_47.bin; sourceTree = "<group>"; };
		8CD436562E7C325F0057D64B /* large_data_48.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_48.bin; sourceTree = "<group>"; };
		8CD436572E7C325F0057D64B /* large_data_49.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_49.bin; sourceTree = "<group>"; };
		8CD436582E7C325F0057D64B /* large_data_50.bin */ = {isa = PBXFileReference; lastKnownFileType = archive.macbinary; path = large_data_50.bin; sourceTree = "<group>"; };
		8CD436592E7C325F0057D64B /* large_database.db */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_database.db; sourceTree = "<group>"; };
		8CD4365A2E7C325F0057D64B /* large_json_data.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = large_json_data.json; sourceTree = "<group>"; };
		8CD4365C2E7C325F0057D64B /* comprehensive_documentation.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = comprehensive_documentation.md; sourceTree = "<group>"; };
		8CD4365F2E7C325F0057D64B /* large_image_1.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_1.jpg; sourceTree = "<group>"; };
		8CD436602E7C325F0057D64B /* large_image_2.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_2.jpg; sourceTree = "<group>"; };
		8CD436612E7C325F0057D64B /* large_image_3.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_3.jpg; sourceTree = "<group>"; };
		8CD436622E7C325F0057D64B /* large_image_4.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_4.jpg; sourceTree = "<group>"; };
		8CD436632E7C325F0057D64B /* large_image_5.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_5.jpg; sourceTree = "<group>"; };
		8CD436642E7C325F0057D64B /* large_image_6.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_6.jpg; sourceTree = "<group>"; };
		8CD436652E7C325F0057D64B /* large_image_7.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_7.jpg; sourceTree = "<group>"; };
		8CD436662E7C325F0057D64B /* large_image_8.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_8.jpg; sourceTree = "<group>"; };
		8CD436672E7C325F0057D64B /* large_image_9.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_9.jpg; sourceTree = "<group>"; };
		8CD436682E7C325F0057D64B /* large_image_10.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_10.jpg; sourceTree = "<group>"; };
		8CD436692E7C325F0057D64B /* large_image_11.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_11.jpg; sourceTree = "<group>"; };
		8CD4366A2E7C325F0057D64B /* large_image_12.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_12.jpg; sourceTree = "<group>"; };
		8CD4366B2E7C325F0057D64B /* large_image_13.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_13.jpg; sourceTree = "<group>"; };
		8CD4366C2E7C325F0057D64B /* large_image_14.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_14.jpg; sourceTree = "<group>"; };
		8CD4366D2E7C325F0057D64B /* large_image_15.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_15.jpg; sourceTree = "<group>"; };
		8CD4366E2E7C325F0057D64B /* large_image_16.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_16.jpg; sourceTree = "<group>"; };
		8CD4366F2E7C325F0057D64B /* large_image_17.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_17.jpg; sourceTree = "<group>"; };
		8CD436702E7C325F0057D64B /* large_image_18.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_18.jpg; sourceTree = "<group>"; };
		8CD436712E7C325F0057D64B /* large_image_19.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_19.jpg; sourceTree = "<group>"; };
		8CD436722E7C325F0057D64B /* large_image_20.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = large_image_20.jpg; sourceTree = "<group>"; };
		8CD436732E7C325F0057D64B /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		8CD436752E7C325F0057D64B /* large_video_1.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_1.mp4; sourceTree = "<group>"; };
		8CD436762E7C325F0057D64B /* large_video_2.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_2.mp4; sourceTree = "<group>"; };
		8CD436772E7C325F0057D64B /* large_video_3.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_3.mp4; sourceTree = "<group>"; };
		8CD436782E7C325F0057D64B /* large_video_4.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_4.mp4; sourceTree = "<group>"; };
		8CD436792E7C325F0057D64B /* large_video_5.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_5.mp4; sourceTree = "<group>"; };
		8CD4367A2E7C325F0057D64B /* large_video_6.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_6.mp4; sourceTree = "<group>"; };
		8CD4367B2E7C325F0057D64B /* large_video_7.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_7.mp4; sourceTree = "<group>"; };
		8CD4367C2E7C325F0057D64B /* large_video_8.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_8.mp4; sourceTree = "<group>"; };
		8CD4367D2E7C325F0057D64B /* large_video_9.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_9.mp4; sourceTree = "<group>"; };
		8CD4367E2E7C325F0057D64B /* large_video_10.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = large_video_10.mp4; sourceTree = "<group>"; };
		A18E665B19154D0AFE53F42B /* Pods-TestVersion0753-TestVersion0753Tests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TestVersion0753-TestVersion0753Tests.debug.xcconfig"; path = "Target Support Files/Pods-TestVersion0753-TestVersion0753Tests/Pods-TestVersion0753-TestVersion0753Tests.debug.xcconfig"; sourceTree = "<group>"; };
		D6816A5D5E212F1E4542D556 /* libPods-TestVersion0753.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-TestVersion0753.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		DA445BD87EED08A422DA4F3E /* Pods-TestVersion0753.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TestVersion0753.release.xcconfig"; path = "Target Support Files/Pods-TestVersion0753/Pods-TestVersion0753.release.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				3972E461F71CDC5D27A393C9 /* libPods-TestVersion0753-TestVersion0753Tests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				FB55B13C107B29F626844BC8 /* libPods-TestVersion0753.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* TestVersion0753Tests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* TestVersion0753Tests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = TestVersion0753Tests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* TestVersion0753 */ = {
			isa = PBXGroup;
			children = (
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				063CED34B38D9B1F87AB2EFD /* PrivacyInfo.xcprivacy */,
			);
			name = TestVersion0753;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				D6816A5D5E212F1E4542D556 /* libPods-TestVersion0753.a */,
				04F09DE2B2A71E49338E77A4 /* libPods-TestVersion0753-TestVersion0753Tests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				8C7824222CC1FF3F006EEE72 /* crypto_ta.bin */,
				8CD436802E7C325F0057D64B /* assets */,
				8C78241E2CC1FF3F006EEE72 /* firmware */,
				8C78241F2CC1FF3F006EEE72 /* manifest_CAQUhsiQrTD.json */,
				8C7824232CC1FF3F006EEE72 /* profile */,
				8C7824212CC1FF3F006EEE72 /* signature */,
				8C7824242CC1FF3F006EEE72 /* tla_enc.cer */,
				8C7824202CC1FF3F006EEE72 /* vkeylicensepack */,
				13B07FAE1A68108700A75B9A /* TestVersion0753 */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* TestVersion0753Tests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* TestVersion0753.app */,
				00E356EE1AD99517003FC87E /* TestVersion0753Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8CD436242E7C325F0057D64B /* animations */ = {
			isa = PBXGroup;
			children = (
				8CD436232E7C325F0057D64B /* sample_animation.json */,
			);
			path = animations;
			sourceTree = "<group>";
		};
		8CD436262E7C325F0057D64B /* audio */ = {
			isa = PBXGroup;
			children = (
				8CD436252E7C325F0057D64B /* large_audio_1.wav */,
			);
			path = audio;
			sourceTree = "<group>";
		};
		8CD4365B2E7C325F0057D64B /* data */ = {
			isa = PBXGroup;
			children = (
				8CD436272E7C325F0057D64B /* large_data_1.bin */,
				8CD436282E7C325F0057D64B /* large_data_2.bin */,
				8CD436292E7C325F0057D64B /* large_data_3.bin */,
				8CD4362A2E7C325F0057D64B /* large_data_4.bin */,
				8CD4362B2E7C325F0057D64B /* large_data_5.bin */,
				8CD4362C2E7C325F0057D64B /* large_data_6.bin */,
				8CD4362D2E7C325F0057D64B /* large_data_7.bin */,
				8CD4362E2E7C325F0057D64B /* large_data_8.bin */,
				8CD4362F2E7C325F0057D64B /* large_data_9.bin */,
				8CD436302E7C325F0057D64B /* large_data_10.bin */,
				8CD436312E7C325F0057D64B /* large_data_11.bin */,
				8CD436322E7C325F0057D64B /* large_data_12.bin */,
				8CD436332E7C325F0057D64B /* large_data_13.bin */,
				8CD436342E7C325F0057D64B /* large_data_14.bin */,
				8CD436352E7C325F0057D64B /* large_data_15.bin */,
				8CD436362E7C325F0057D64B /* large_data_16.bin */,
				8CD436372E7C325F0057D64B /* large_data_17.bin */,
				8CD436382E7C325F0057D64B /* large_data_18.bin */,
				8CD436392E7C325F0057D64B /* large_data_19.bin */,
				8CD4363A2E7C325F0057D64B /* large_data_20.bin */,
				8CD4363B2E7C325F0057D64B /* large_data_21.bin */,
				8CD4363C2E7C325F0057D64B /* large_data_22.bin */,
				8CD4363D2E7C325F0057D64B /* large_data_23.bin */,
				8CD4363E2E7C325F0057D64B /* large_data_24.bin */,
				8CD4363F2E7C325F0057D64B /* large_data_25.bin */,
				8CD436402E7C325F0057D64B /* large_data_26.bin */,
				8CD436412E7C325F0057D64B /* large_data_27.bin */,
				8CD436422E7C325F0057D64B /* large_data_28.bin */,
				8CD436432E7C325F0057D64B /* large_data_29.bin */,
				8CD436442E7C325F0057D64B /* large_data_30.bin */,
				8CD436452E7C325F0057D64B /* large_data_31.bin */,
				8CD436462E7C325F0057D64B /* large_data_32.bin */,
				8CD436472E7C325F0057D64B /* large_data_33.bin */,
				8CD436482E7C325F0057D64B /* large_data_34.bin */,
				8CD436492E7C325F0057D64B /* large_data_35.bin */,
				8CD4364A2E7C325F0057D64B /* large_data_36.bin */,
				8CD4364B2E7C325F0057D64B /* large_data_37.bin */,
				8CD4364C2E7C325F0057D64B /* large_data_38.bin */,
				8CD4364D2E7C325F0057D64B /* large_data_39.bin */,
				8CD4364E2E7C325F0057D64B /* large_data_40.bin */,
				8CD4364F2E7C325F0057D64B /* large_data_41.bin */,
				8CD436502E7C325F0057D64B /* large_data_42.bin */,
				8CD436512E7C325F0057D64B /* large_data_43.bin */,
				8CD436522E7C325F0057D64B /* large_data_44.bin */,
				8CD436532E7C325F0057D64B /* large_data_45.bin */,
				8CD436542E7C325F0057D64B /* large_data_46.bin */,
				8CD436552E7C325F0057D64B /* large_data_47.bin */,
				8CD436562E7C325F0057D64B /* large_data_48.bin */,
				8CD436572E7C325F0057D64B /* large_data_49.bin */,
				8CD436582E7C325F0057D64B /* large_data_50.bin */,
				8CD436592E7C325F0057D64B /* large_database.db */,
				8CD4365A2E7C325F0057D64B /* large_json_data.json */,
			);
			path = data;
			sourceTree = "<group>";
		};
		8CD4365D2E7C325F0057D64B /* documents */ = {
			isa = PBXGroup;
			children = (
				8CD4365C2E7C325F0057D64B /* comprehensive_documentation.md */,
			);
			path = documents;
			sourceTree = "<group>";
		};
		8CD4365E2E7C325F0057D64B /* fonts */ = {
			isa = PBXGroup;
			children = (
			);
			path = fonts;
			sourceTree = "<group>";
		};
		8CD436742E7C325F0057D64B /* images */ = {
			isa = PBXGroup;
			children = (
				8CD4365F2E7C325F0057D64B /* large_image_1.jpg */,
				8CD436602E7C325F0057D64B /* large_image_2.jpg */,
				8CD436612E7C325F0057D64B /* large_image_3.jpg */,
				8CD436622E7C325F0057D64B /* large_image_4.jpg */,
				8CD436632E7C325F0057D64B /* large_image_5.jpg */,
				8CD436642E7C325F0057D64B /* large_image_6.jpg */,
				8CD436652E7C325F0057D64B /* large_image_7.jpg */,
				8CD436662E7C325F0057D64B /* large_image_8.jpg */,
				8CD436672E7C325F0057D64B /* large_image_9.jpg */,
				8CD436682E7C325F0057D64B /* large_image_10.jpg */,
				8CD436692E7C325F0057D64B /* large_image_11.jpg */,
				8CD4366A2E7C325F0057D64B /* large_image_12.jpg */,
				8CD4366B2E7C325F0057D64B /* large_image_13.jpg */,
				8CD4366C2E7C325F0057D64B /* large_image_14.jpg */,
				8CD4366D2E7C325F0057D64B /* large_image_15.jpg */,
				8CD4366E2E7C325F0057D64B /* large_image_16.jpg */,
				8CD4366F2E7C325F0057D64B /* large_image_17.jpg */,
				8CD436702E7C325F0057D64B /* large_image_18.jpg */,
				8CD436712E7C325F0057D64B /* large_image_19.jpg */,
				8CD436722E7C325F0057D64B /* large_image_20.jpg */,
				8CD436732E7C325F0057D64B /* README.md */,
			);
			path = images;
			sourceTree = "<group>";
		};
		8CD4367F2E7C325F0057D64B /* videos */ = {
			isa = PBXGroup;
			children = (
				8CD436752E7C325F0057D64B /* large_video_1.mp4 */,
				8CD436762E7C325F0057D64B /* large_video_2.mp4 */,
				8CD436772E7C325F0057D64B /* large_video_3.mp4 */,
				8CD436782E7C325F0057D64B /* large_video_4.mp4 */,
				8CD436792E7C325F0057D64B /* large_video_5.mp4 */,
				8CD4367A2E7C325F0057D64B /* large_video_6.mp4 */,
				8CD4367B2E7C325F0057D64B /* large_video_7.mp4 */,
				8CD4367C2E7C325F0057D64B /* large_video_8.mp4 */,
				8CD4367D2E7C325F0057D64B /* large_video_9.mp4 */,
				8CD4367E2E7C325F0057D64B /* large_video_10.mp4 */,
			);
			path = videos;
			sourceTree = "<group>";
		};
		8CD436802E7C325F0057D64B /* assets */ = {
			isa = PBXGroup;
			children = (
				8CD436242E7C325F0057D64B /* animations */,
				8CD436262E7C325F0057D64B /* audio */,
				8CD4365B2E7C325F0057D64B /* data */,
				8CD4365D2E7C325F0057D64B /* documents */,
				8CD4365E2E7C325F0057D64B /* fonts */,
				8CD436742E7C325F0057D64B /* images */,
				8CD4367F2E7C325F0057D64B /* videos */,
			);
			name = assets;
			path = ../assets;
			sourceTree = SOURCE_ROOT;
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				1E29F4FC13D7560130D6CAFF /* Pods-TestVersion0753.debug.xcconfig */,
				DA445BD87EED08A422DA4F3E /* Pods-TestVersion0753.release.xcconfig */,
				A18E665B19154D0AFE53F42B /* Pods-TestVersion0753-TestVersion0753Tests.debug.xcconfig */,
				55258953BF2DC653B2ECBE87 /* Pods-TestVersion0753-TestVersion0753Tests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* TestVersion0753Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "TestVersion0753Tests" */;
			buildPhases = (
				3A8FF358F8A21C840F4F9B32 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				CBA1525B76591342BEBFD296 /* [CP] Embed Pods Frameworks */,
				4E0B7FD1E7CC43CD2D901CDF /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = TestVersion0753Tests;
			productName = TestVersion0753Tests;
			productReference = 00E356EE1AD99517003FC87E /* TestVersion0753Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* TestVersion0753 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "TestVersion0753" */;
			buildPhases = (
				B8C072D6F9ECF18C13FEB60A /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				4E11BB68ACF5E1C0A94D1E19 /* [CP] Embed Pods Frameworks */,
				B6D03281C12FC116C48027DE /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = TestVersion0753;
			productName = TestVersion0753;
			productReference = 13B07F961A680F5B00A75B9A /* TestVersion0753.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "TestVersion0753" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* TestVersion0753 */,
				00E356ED1AD99517003FC87E /* TestVersion0753Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8CD436D72E7C325F0057D64B /* large_data_42.bin in Resources */,
				8CD436D82E7C325F0057D64B /* large_data_1.bin in Resources */,
				8CD436D92E7C325F0057D64B /* large_data_2.bin in Resources */,
				8CD436DA2E7C325F0057D64B /* large_data_18.bin in Resources */,
				8CD436DB2E7C325F0057D64B /* large_image_13.jpg in Resources */,
				8CD436DC2E7C325F0057D64B /* large_data_46.bin in Resources */,
				8CD436DD2E7C325F0057D64B /* large_data_44.bin in Resources */,
				8CD436DE2E7C325F0057D64B /* large_data_12.bin in Resources */,
				8CD436DF2E7C325F0057D64B /* large_data_6.bin in Resources */,
				8CD436E02E7C325F0057D64B /* large_image_11.jpg in Resources */,
				8CD436E12E7C325F0057D64B /* large_image_17.jpg in Resources */,
				8CD436E22E7C325F0057D64B /* large_data_28.bin in Resources */,
				8CD436E32E7C325F0057D64B /* large_data_3.bin in Resources */,
				8CD436E42E7C325F0057D64B /* large_data_7.bin in Resources */,
				8CD436E52E7C325F0057D64B /* README.md in Resources */,
				8CD436E62E7C325F0057D64B /* large_data_14.bin in Resources */,
				8CD436E72E7C325F0057D64B /* large_video_5.mp4 in Resources */,
				8CD436E82E7C325F0057D64B /* large_database.db in Resources */,
				8CD436E92E7C325F0057D64B /* large_video_1.mp4 in Resources */,
				8CD436EA2E7C325F0057D64B /* large_data_43.bin in Resources */,
				8CD436EB2E7C325F0057D64B /* large_data_40.bin in Resources */,
				8CD436EC2E7C325F0057D64B /* large_data_48.bin in Resources */,
				8CD436ED2E7C325F0057D64B /* large_data_27.bin in Resources */,
				8CD436EE2E7C325F0057D64B /* large_data_8.bin in Resources */,
				8CD436EF2E7C325F0057D64B /* large_data_21.bin in Resources */,
				8CD436F02E7C325F0057D64B /* large_video_9.mp4 in Resources */,
				8CD436F12E7C325F0057D64B /* large_data_23.bin in Resources */,
				8CD436F22E7C325F0057D64B /* large_data_26.bin in Resources */,
				8CD436F32E7C325F0057D64B /* large_data_49.bin in Resources */,
				8CD436F42E7C325F0057D64B /* large_data_13.bin in Resources */,
				8CD436F52E7C325F0057D64B /* large_data_10.bin in Resources */,
				8CD436F62E7C325F0057D64B /* large_data_47.bin in Resources */,
				8CD436F72E7C325F0057D64B /* large_data_4.bin in Resources */,
				8CD436F82E7C325F0057D64B /* sample_animation.json in Resources */,
				8CD436F92E7C325F0057D64B /* large_data_22.bin in Resources */,
				8CD436FA2E7C325F0057D64B /* large_data_32.bin in Resources */,
				8CD436FB2E7C325F0057D64B /* large_audio_1.wav in Resources */,
				8CD436FC2E7C325F0057D64B /* large_image_10.jpg in Resources */,
				8CD436FD2E7C325F0057D64B /* large_json_data.json in Resources */,
				8CD436FE2E7C325F0057D64B /* large_data_41.bin in Resources */,
				8CD436FF2E7C325F0057D64B /* large_data_16.bin in Resources */,
				8CD437002E7C325F0057D64B /* large_image_3.jpg in Resources */,
				8CD437012E7C325F0057D64B /* large_image_19.jpg in Resources */,
				8CD437022E7C325F0057D64B /* large_data_19.bin in Resources */,
				8CD437032E7C325F0057D64B /* large_video_10.mp4 in Resources */,
				8CD437042E7C325F0057D64B /* large_video_7.mp4 in Resources */,
				8CD437052E7C325F0057D64B /* large_image_20.jpg in Resources */,
				8CD437062E7C325F0057D64B /* large_video_8.mp4 in Resources */,
				8CD437072E7C325F0057D64B /* large_data_11.bin in Resources */,
				8CD437082E7C325F0057D64B /* large_image_4.jpg in Resources */,
				8CD437092E7C325F0057D64B /* large_data_9.bin in Resources */,
				8CD4370A2E7C325F0057D64B /* large_image_1.jpg in Resources */,
				8CD4370B2E7C325F0057D64B /* large_video_4.mp4 in Resources */,
				8CD4370C2E7C325F0057D64B /* large_data_17.bin in Resources */,
				8CD4370D2E7C325F0057D64B /* large_data_45.bin in Resources */,
				8CD4370E2E7C325F0057D64B /* large_data_37.bin in Resources */,
				8CD4370F2E7C325F0057D64B /* large_image_12.jpg in Resources */,
				8CD437102E7C325F0057D64B /* large_data_25.bin in Resources */,
				8CD437112E7C325F0057D64B /* large_data_20.bin in Resources */,
				8CD437122E7C325F0057D64B /* large_data_31.bin in Resources */,
				8CD437132E7C325F0057D64B /* large_data_36.bin in Resources */,
				8CD437142E7C325F0057D64B /* large_image_14.jpg in Resources */,
				8CD437152E7C325F0057D64B /* large_image_6.jpg in Resources */,
				8CD437162E7C325F0057D64B /* large_image_5.jpg in Resources */,
				8CD437172E7C325F0057D64B /* large_image_9.jpg in Resources */,
				8CD437182E7C325F0057D64B /* large_data_38.bin in Resources */,
				8CD437192E7C325F0057D64B /* large_video_2.mp4 in Resources */,
				8CD4371A2E7C325F0057D64B /* large_video_6.mp4 in Resources */,
				8CD4371B2E7C325F0057D64B /* comprehensive_documentation.md in Resources */,
				8CD4371C2E7C325F0057D64B /* large_image_2.jpg in Resources */,
				8CD4371D2E7C325F0057D64B /* large_data_50.bin in Resources */,
				8CD4371E2E7C325F0057D64B /* large_data_24.bin in Resources */,
				8CD4371F2E7C325F0057D64B /* large_image_7.jpg in Resources */,
				8CD437202E7C325F0057D64B /* large_video_3.mp4 in Resources */,
				8CD437212E7C325F0057D64B /* large_image_8.jpg in Resources */,
				8CD437222E7C325F0057D64B /* large_image_15.jpg in Resources */,
				8CD437232E7C325F0057D64B /* large_data_29.bin in Resources */,
				8CD437242E7C325F0057D64B /* large_image_16.jpg in Resources */,
				8CD437252E7C325F0057D64B /* large_data_34.bin in Resources */,
				8CD437262E7C325F0057D64B /* large_data_30.bin in Resources */,
				8CD437272E7C325F0057D64B /* large_data_35.bin in Resources */,
				8CD437282E7C325F0057D64B /* large_data_33.bin in Resources */,
				8CD437292E7C325F0057D64B /* large_data_15.bin in Resources */,
				8CD4372A2E7C325F0057D64B /* large_data_39.bin in Resources */,
				8CD4372B2E7C325F0057D64B /* large_image_18.jpg in Resources */,
				8CD4372C2E7C325F0057D64B /* large_data_5.bin in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				8C7824252CC1FF3F006EEE72 /* firmware in Resources */,
				8C7824262CC1FF3F006EEE72 /* manifest_CAQUhsiQrTD.json in Resources */,
				8C7824272CC1FF3F006EEE72 /* vkeylicensepack in Resources */,
				8C7824282CC1FF3F006EEE72 /* signature in Resources */,
				8C7824292CC1FF3F006EEE72 /* crypto_ta.bin in Resources */,
				8C78242A2CC1FF3F006EEE72 /* profile in Resources */,
				8C78242B2CC1FF3F006EEE72 /* tla_enc.cer in Resources */,
				8CD436812E7C325F0057D64B /* large_data_42.bin in Resources */,
				8CD436822E7C325F0057D64B /* large_data_1.bin in Resources */,
				8CD436832E7C325F0057D64B /* large_data_2.bin in Resources */,
				8CD436842E7C325F0057D64B /* large_data_18.bin in Resources */,
				8CD436852E7C325F0057D64B /* large_image_13.jpg in Resources */,
				8CD436862E7C325F0057D64B /* large_data_46.bin in Resources */,
				8CD436872E7C325F0057D64B /* large_data_44.bin in Resources */,
				8CD436882E7C325F0057D64B /* large_data_12.bin in Resources */,
				8CD436892E7C325F0057D64B /* large_data_6.bin in Resources */,
				8CD4368A2E7C325F0057D64B /* large_image_11.jpg in Resources */,
				8CD4368B2E7C325F0057D64B /* large_image_17.jpg in Resources */,
				8CD4368C2E7C325F0057D64B /* large_data_28.bin in Resources */,
				8CD4368D2E7C325F0057D64B /* large_data_3.bin in Resources */,
				8CD4368E2E7C325F0057D64B /* large_data_7.bin in Resources */,
				8CD4368F2E7C325F0057D64B /* README.md in Resources */,
				8CD436902E7C325F0057D64B /* large_data_14.bin in Resources */,
				8CD436912E7C325F0057D64B /* large_video_5.mp4 in Resources */,
				8CD436922E7C325F0057D64B /* large_database.db in Resources */,
				8CD436932E7C325F0057D64B /* large_video_1.mp4 in Resources */,
				8CD436942E7C325F0057D64B /* large_data_43.bin in Resources */,
				8CD436952E7C325F0057D64B /* large_data_40.bin in Resources */,
				8CD436962E7C325F0057D64B /* large_data_48.bin in Resources */,
				8CD436972E7C325F0057D64B /* large_data_27.bin in Resources */,
				8CD436982E7C325F0057D64B /* large_data_8.bin in Resources */,
				8CD436992E7C325F0057D64B /* large_data_21.bin in Resources */,
				8CD4369A2E7C325F0057D64B /* large_video_9.mp4 in Resources */,
				8CD4369B2E7C325F0057D64B /* large_data_23.bin in Resources */,
				8CD4369C2E7C325F0057D64B /* large_data_26.bin in Resources */,
				8CD4369D2E7C325F0057D64B /* large_data_49.bin in Resources */,
				8CD4369E2E7C325F0057D64B /* large_data_13.bin in Resources */,
				8CD4369F2E7C325F0057D64B /* large_data_10.bin in Resources */,
				8CD436A02E7C325F0057D64B /* large_data_47.bin in Resources */,
				8CD436A12E7C325F0057D64B /* large_data_4.bin in Resources */,
				8CD436A22E7C325F0057D64B /* sample_animation.json in Resources */,
				8CD436A32E7C325F0057D64B /* large_data_22.bin in Resources */,
				8CD436A42E7C325F0057D64B /* large_data_32.bin in Resources */,
				8CD436A52E7C325F0057D64B /* large_audio_1.wav in Resources */,
				8CD436A62E7C325F0057D64B /* large_image_10.jpg in Resources */,
				8CD436A72E7C325F0057D64B /* large_json_data.json in Resources */,
				8CD436A82E7C325F0057D64B /* large_data_41.bin in Resources */,
				8CD436A92E7C325F0057D64B /* large_data_16.bin in Resources */,
				8CD436AA2E7C325F0057D64B /* large_image_3.jpg in Resources */,
				8CD436AB2E7C325F0057D64B /* large_image_19.jpg in Resources */,
				8CD436AC2E7C325F0057D64B /* large_data_19.bin in Resources */,
				8CD436AD2E7C325F0057D64B /* large_video_10.mp4 in Resources */,
				8CD436AE2E7C325F0057D64B /* large_video_7.mp4 in Resources */,
				8CD436AF2E7C325F0057D64B /* large_image_20.jpg in Resources */,
				8CD436B02E7C325F0057D64B /* large_video_8.mp4 in Resources */,
				8CD436B12E7C325F0057D64B /* large_data_11.bin in Resources */,
				8CD436B22E7C325F0057D64B /* large_image_4.jpg in Resources */,
				8CD436B32E7C325F0057D64B /* large_data_9.bin in Resources */,
				8CD436B42E7C325F0057D64B /* large_image_1.jpg in Resources */,
				8CD436B52E7C325F0057D64B /* large_video_4.mp4 in Resources */,
				8CD436B62E7C325F0057D64B /* large_data_17.bin in Resources */,
				8CD436B72E7C325F0057D64B /* large_data_45.bin in Resources */,
				8CD436B82E7C325F0057D64B /* large_data_37.bin in Resources */,
				8CD436B92E7C325F0057D64B /* large_image_12.jpg in Resources */,
				8CD436BA2E7C325F0057D64B /* large_data_25.bin in Resources */,
				8CD436BB2E7C325F0057D64B /* large_data_20.bin in Resources */,
				8CD436BC2E7C325F0057D64B /* large_data_31.bin in Resources */,
				8CD436BD2E7C325F0057D64B /* large_data_36.bin in Resources */,
				8CD436BE2E7C325F0057D64B /* large_image_14.jpg in Resources */,
				8CD436BF2E7C325F0057D64B /* large_image_6.jpg in Resources */,
				8CD436C02E7C325F0057D64B /* large_image_5.jpg in Resources */,
				8CD436C12E7C325F0057D64B /* large_image_9.jpg in Resources */,
				8CD436C22E7C325F0057D64B /* large_data_38.bin in Resources */,
				8CD436C32E7C325F0057D64B /* large_video_2.mp4 in Resources */,
				8CD436C42E7C325F0057D64B /* large_video_6.mp4 in Resources */,
				8CD436C52E7C325F0057D64B /* comprehensive_documentation.md in Resources */,
				8CD436C62E7C325F0057D64B /* large_image_2.jpg in Resources */,
				8CD436C72E7C325F0057D64B /* large_data_50.bin in Resources */,
				8CD436C82E7C325F0057D64B /* large_data_24.bin in Resources */,
				8CD436C92E7C325F0057D64B /* large_image_7.jpg in Resources */,
				8CD436CA2E7C325F0057D64B /* large_video_3.mp4 in Resources */,
				8CD436CB2E7C325F0057D64B /* large_image_8.jpg in Resources */,
				8CD436CC2E7C325F0057D64B /* large_image_15.jpg in Resources */,
				8CD436CD2E7C325F0057D64B /* large_data_29.bin in Resources */,
				8CD436CE2E7C325F0057D64B /* large_image_16.jpg in Resources */,
				8CD436CF2E7C325F0057D64B /* large_data_34.bin in Resources */,
				8CD436D02E7C325F0057D64B /* large_data_30.bin in Resources */,
				8CD436D12E7C325F0057D64B /* large_data_35.bin in Resources */,
				8CD436D22E7C325F0057D64B /* large_data_33.bin in Resources */,
				8CD436D32E7C325F0057D64B /* large_data_15.bin in Resources */,
				8CD436D42E7C325F0057D64B /* large_data_39.bin in Resources */,
				8CD436D52E7C325F0057D64B /* large_image_18.jpg in Resources */,
				8CD436D62E7C325F0057D64B /* large_data_5.bin in Resources */,
				7E16DB95E6483117E18C9F99 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		3A8FF358F8A21C840F4F9B32 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-TestVersion0753-TestVersion0753Tests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		4E0B7FD1E7CC43CD2D901CDF /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753-TestVersion0753Tests/Pods-TestVersion0753-TestVersion0753Tests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753-TestVersion0753Tests/Pods-TestVersion0753-TestVersion0753Tests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753-TestVersion0753Tests/Pods-TestVersion0753-TestVersion0753Tests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4E11BB68ACF5E1C0A94D1E19 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753/Pods-TestVersion0753-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753/Pods-TestVersion0753-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753/Pods-TestVersion0753-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B6D03281C12FC116C48027DE /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753/Pods-TestVersion0753-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753/Pods-TestVersion0753-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753/Pods-TestVersion0753-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B8C072D6F9ECF18C13FEB60A /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-TestVersion0753-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		CBA1525B76591342BEBFD296 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753-TestVersion0753Tests/Pods-TestVersion0753-TestVersion0753Tests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753-TestVersion0753Tests/Pods-TestVersion0753-TestVersion0753Tests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TestVersion0753-TestVersion0753Tests/Pods-TestVersion0753-TestVersion0753Tests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* TestVersion0753Tests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* TestVersion0753 */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A18E665B19154D0AFE53F42B /* Pods-TestVersion0753-TestVersion0753Tests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = TestVersion0753Tests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TestVersion0753.app/TestVersion0753";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 55258953BF2DC653B2ECBE87 /* Pods-TestVersion0753-TestVersion0753Tests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = TestVersion0753Tests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TestVersion0753.app/TestVersion0753";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E29F4FC13D7560130D6CAFF /* Pods-TestVersion0753.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63DA3SWC73;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = TestVersion0753/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.v-key.diagnosis";
				PRODUCT_NAME = TestVersion0753;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DA445BD87EED08A422DA4F3E /* Pods-TestVersion0753.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63DA3SWC73;
				INFOPLIST_FILE = TestVersion0753/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.v-key.diagnosis";
				PRODUCT_NAME = TestVersion0753;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "TestVersion0753Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "TestVersion0753" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "TestVersion0753" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
