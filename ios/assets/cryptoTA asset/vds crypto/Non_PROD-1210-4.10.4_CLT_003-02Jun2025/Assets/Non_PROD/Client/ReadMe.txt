****************************************************************************************************
****************************************************************************************************
***********************************     V-Key Assets Package     ***********************************
****************************************************************************************************
****************************************************************************************************

This README file contains the information about the V-Key assets package generated for the
following:

Assets Environment :	NON_PROD
Firmware Version   :	50.4.2.0
Customer Info      :	Viettel Digital (VDS) (1210)
Package Date       :	02 June 2025
Usage              :	For being included inside the mobile app


Each folder/subfolder contains the necessary asset files. Verify each asset file by validating its
MD5 checksum. For target location of the asset files in your app project folder, see respective
Developer Guide for more details.

The MD5 checksum of each asset file is as follows:

firmware
 |_____debug/firmware                            :	b13c3e53d0009593672402b1ff375bb4
 |_____release/firmware                          :	f3f84bee1c6159b26c011668b0c83e0e
\manifest
manifest.json                                    :	46bbd5bf8679a85a4be7543764f99a71

signature
 |_____android/signature                         :	dbdb3a4959f73eb7d2b4d80b8f578f71
 |_____ios/signature                             :	9203f0381940f6e1c6556a6b07792fed

vkeylicensepack
 |_____vds_android_alpha/vkeylicensepack         :	c10adc917bc56e18959068658e43c271
 |_____vds_android_pat/vkeylicensepack           :	70666c439b2c7f18bb0eb778fd7705f2
 |_____vds_android_sit/vkeylicensepack           :	a4aedb93f35d78f2b283028610b31ae8
 |_____vds_android_uat/vkeylicensepack           :	3a0cd7888d74caf70339930e57c7c1db
 |_____vds_ios_alpha_acc2/vkeylicensepack        :	0a7f6577a23399b3653a331452d7d128
 |_____vds_ios_alpha_acc3/vkeylicensepack        :	f6dbd30eeb04faebca4867ecbe3a94c7
 |_____vds_ios_alpha_acc4/vkeylicensepack        :	9a90279106069c23e16b7667110a42c9
 |_____vds_ios_pat_acc2/vkeylicensepack          :	47a497cfefe096a0a0e2f6bc22bd9cfc
 |_____vds_ios_pat_acc3/vkeylicensepack          :	086d1cfd5183bf2094fa097a0ce92025
 |_____vds_ios_pat_acc4/vkeylicensepack          :	fade1110a775b8f25b059f86ab1428c0
 |_____vds_ios_sit_acc2/vkeylicensepack          :	657ebfab0b9cdfad728e8e72d8a103b9
 |_____vds_ios_sit_acc3/vkeylicensepack          :	5b4d20144338b85c1379646e98d854b8
 |_____vds_ios_sit_acc4/vkeylicensepack          :	7ec922d936a7998bb22c9c92003625a0
 |_____vds_ios_uat_acc2/vkeylicensepack          :	bcf4461c774d1d968fff0634c6e381d1
 |_____vds_ios_uat_acc3/vkeylicensepack          :	0a131244e42a2ee8c49ec63edfab01e8
 |_____vds_ios_uat_acc4/vkeylicensepack          :	f0b6d7ceaa5bdf3ef559fac10c7b9886

voscodesign.vky                                  :	40be902796df753015aba244c54b848b

****************************************************************************************************
****************************************************************************************************
*******************     Grant of Limited License / Disclaimer of Warranties     ********************
****************************************************************************************************
****************************************************************************************************

V-Key Inc (V-Key) hereby grants the Licensee a non-transferable and non-exclusive right (the
"License") for the duration of the Subscription Period(s) ordered and paid for to: (i) install, set-
up and process, or otherwise interact with the server components of the Software to generate soft
token firmware, seeds, APIN and related asset files as part of threat intelligence, token
management, authentication, messaging and provisioning; and (ii) allow end users who are customers
of the Licensee (via sub-license) to install and use client application components on mobile devices
for the purposes of tamper protection, identity authentication and transaction authorisation.
Software contains third party machine-readable codes e.g. in the form of open-source (Apache or BSD-
style) and/or for-commercial-use libraries. Such third party terms and conditions are made available
for reference as part of the Software installation package.

V-KEY AND ITS APPROVED SOURCES PROVIDE THE V-KEY SOFTWARE "AS IS" WITHOUT WARRANTY OF ANY KIND
INCLUDING WITHOUT LIMITATION, ANY WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT. FURTHER, V-KEY DOES NOT WARRANT RESULTS OF USE OR FREEDOM FROM BUGS OR
UNINTERRUPTED USE OR ACCESS. NOTWITHSTANDING THE FOREGOING, V-KEY WARRANTS THAT V-KEY SOFTW ARE DO
NOT AND WILL NOT CONTAIN ANY VIRUS, TROJAN HORSE, WORM, LOGIC BOMB, OR OTHER SOFTWARE ROUTINE
DESIGNED TO PERMIT UNAUTHORISED ACCESS, TO DISABLE, ERASE OR OTHERWISE HARM SOFTWARE, HARDWARE OR
DATA, OR TO PERFORM ANY SUCH ACTIONS.

Note: The above may be subject to additional terms and conditions negotiated between V-Key and the
License.
