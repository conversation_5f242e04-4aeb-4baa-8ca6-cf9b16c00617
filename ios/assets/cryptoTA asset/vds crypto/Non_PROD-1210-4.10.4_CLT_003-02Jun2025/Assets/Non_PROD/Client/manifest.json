{"timeStamp": "20250602071740UTC", "trustAnchor": {"objSerial": 0, "version": 2, "payload": "eyJjZXJ0TGlzdCI6WyJNSUlCd3pDQ0FXZWdBd0lCQWdJRVNJajhiekFNQmdncWhrak9QUVFEQWdVQU1GWXhDekFKQmdOVkJBWVRBbk5uTVFzd0NRWURWUVFJRXdKelp6RUxNQWtHQTFVRUJ4TUNjMmN4Q3pBSkJnTlZCQW9UQW5Obk1RMHdDd1lEVlFRTEV3UjJhMlY1TVJFd0R3WURWUVFERXdnM09IaDRJSE4wWnpBZUZ3MHlOREEzTVRjd05EQXdORGhhRncwek5EQTNNVFV3TkRBd05EaGFNRll4Q3pBSkJnTlZCQVlUQW5Obk1Rc3dDUVlEVlFRSUV3SnpaekVMTUFrR0ExVUVCeE1DYzJjeEN6QUpCZ05WQkFvVEFuTm5NUTB3Q3dZRFZRUUxFd1IyYTJWNU1SRXdEd1lEVlFRREV3ZzNPSGg0SUhOMFp6QlpNQk1HQnlxR1NNNDlBZ0VHQ0NxR1NNNDlBd0VIQTBJQUJPMW43YnNlR1ZYNXZob2NzTDdxaG84Y1VPQjJUMU1iZXdkVStuQllLM1E3YnQzTHVzT3hsdndYVGhFTThFK2Z2OW0xS2lxVDZ5WlNFcE1PVUt6cm95cWpJVEFmTUIwR0ExVWREZ1FXQkJTOVwvanlVMFhja2NaVkhGSFhvOWY3b0liditXakFNQmdncWhrak9QUVFEQWdVQUEwZ0FNRVVDSUROYzZLR0xhcTJtQVJBb1pkdkxvbVFtY1prdVFRRkk5SGFBSUFiM0s4WFlBaUVBcDRQcGRmdEIxNTJwb0F3Q3o5eks0RUQxTTZ1TGd5eWNMVkQ2MHJMMFc5az0iXX0A", "signature": "0ORRsb+5F0guVvBiHAyE+83saLX3MXN/x1PE2HqOTwM="}, "signature": "rAkJxduxjBdonblABNn1BbOC6GKL94nbFLOovE08VBI="}