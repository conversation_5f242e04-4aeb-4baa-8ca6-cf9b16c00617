****************************************************************************************************
****************************************************************************************************
***********************************     V-Key Assets Package     ***********************************
****************************************************************************************************
****************************************************************************************************

This V-Key license pack is generated with the following information:

BundleID/Package Name        :	com.bplus.vtpay
Manageability                :	1
MD5 of vkeylicensepack       :	3a0cd7888d74caf70339930e57c7c1db

The MD5 checksum and expiration date of the certificates are as follows:

Acc2_development_Certificates.cer (03 March 2026)     :	8d51486eff577765f465081f3a0544ff
Acc2_distribution_Certificates.cer (03 March 2026)    :	43b82944cbe352cbc60ee9eb2173d60d
Acc3_development_Certificates.cer (19 June 2025)      :	fbe83b069fd1145eca9023196596903b
Acc3_distribution_Certificates.cer (19 June 2025)     :	a0271d230d2567e0bf073672470721c7
Acc4_development_Certificates.cer (21 June 2025)      :	8ce003b990557afb68335e720fb7bb84
Acc4_distribution_Certificates.cer (21 June 2025)     :	03fbfbf162a76f42b8401d961d31e227
deployment_cert.der (11 November 2040)                :	821174a5e95eae77fa680ebefc883da5
upload_cert.der (11 November 2040)                    :	821174a5e95eae77fa680ebefc883da5
ti_enc.crt (15 July 2034)                             :	b042014cd33ecbf34c8b541c7369f11f
ttkkplus_vn_2022.crt (18 August 2025)                 :	9bf6f757acc2a407286a53c0f75f272f

****************************************************************************************************
****************************************************************************************************
*******************     Grant of Limited License / Disclaimer of Warranties     ********************
****************************************************************************************************
****************************************************************************************************

V-Key Inc (V-Key) hereby grants the Licensee a non-transferable and non-exclusive right (the
"License") for the duration of the Subscription Period(s) ordered and paid for to: (i) install, set-
up and process, or otherwise interact with the server components of the Software to generate soft
token firmware, seeds, APIN and related asset files as part of threat intelligence, token
management, authentication, messaging and provisioning; and (ii) allow end users who are customers
of the Licensee (via sub-license) to install and use client application components on mobile devices
for the purposes of tamper protection, identity authentication and transaction authorisation.
Software contains third party machine-readable codes e.g. in the form of open-source (Apache or BSD-
style) and/or for-commercial-use libraries. Such third party terms and conditions are made available
for reference as part of the Software installation package.

V-KEY AND ITS APPROVED SOURCES PROVIDE THE V-KEY SOFTWARE "AS IS" WITHOUT WARRANTY OF ANY KIND
INCLUDING WITHOUT LIMITATION, ANY WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT. FURTHER, V-KEY DOES NOT WARRANT RESULTS OF USE OR FREEDOM FROM BUGS OR
UNINTERRUPTED USE OR ACCESS. NOTWITHSTANDING THE FOREGOING, V-KEY WARRANTS THAT V-KEY SOFTW ARE DO
NOT AND WILL NOT CONTAIN ANY VIRUS, TROJAN HORSE, WORM, LOGIC BOMB, OR OTHER SOFTWARE ROUTINE
DESIGNED TO PERMIT UNAUTHORISED ACCESS, TO DISABLE, ERASE OR OTHERWISE HARM SOFTWARE, HARDWARE OR
DATA, OR TO PERFORM ANY SUCH ACTIONS.

Note: The above may be subject to additional terms and conditions negotiated between V-Key and the
License.