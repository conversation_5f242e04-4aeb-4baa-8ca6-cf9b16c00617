****************************************************************************************************
****************************************************************************************************
***********************************     V-Key Assets Package     ***********************************
****************************************************************************************************
****************************************************************************************************

This README file contains the information about the V-Key assets package generated for the
following:

Assets Environment :	NON_PROD
Firmware Version   :	50.4.2.0
Customer Info      :	Viettel Digital (VDS)(1210)
Package Date       :	02 June 2025
Usage              :	For creating company in V-OS App Protection Server


This V-Key assets package contains only the vkeylicensepack.json file. Verify the file by validating
its MD5 checksum. For when and how to use this file, see the V-OS App Protection Server User Guide
for more details.

The MD5 checksum of vkeylicensepack.json file is as follows:

vkeylicensepack.json    :	dee4f02efcd905bcd4e62710534134b5

****************************************************************************************************
****************************************************************************************************
*******************     Grant of Limited License / Disclaimer of Warranties     ********************
****************************************************************************************************
****************************************************************************************************

V-Key Inc (V-Key) hereby grants the Licensee a non-transferable and non-exclusive right (the
"License") for the duration of the Subscription Period(s) ordered and paid for to: (i) install, set-
up and process, or otherwise interact with the server components of the Software to generate soft
token firmware, seeds, APIN and related asset files as part of threat intelligence, token
management, authentication, messaging and provisioning; and (ii) allow end users who are customers
of the Licensee (via sub-license) to install and use client application components on mobile devices
for the purposes of tamper protection, identity authentication and transaction authorisation.
Software contains third party machine-readable codes e.g. in the form of open-source (Apache or BSD-
style) and/or for-commercial-use libraries. Such third party terms and conditions are made available
for reference as part of the Software installation package.

V-KEY AND ITS APPROVED SOURCES PROVIDE THE V-KEY SOFTWARE "AS IS" WITHOUT WARRANTY OF ANY KIND
INCLUDING WITHOUT LIMITATION, ANY WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE OR
NON-INFRINGEMENT. FURTHER, V-KEY DOES NOT WARRANT RESULTS OF USE OR FREEDOM FROM BUGS OR
UNINTERRUPTED USE OR ACCESS. NOTWITHSTANDING THE FOREGOING, V-KEY WARRANTS THAT V-KEY SOFTW ARE DO
NOT AND WILL NOT CONTAIN ANY VIRUS, TROJAN HORSE, WORM, LOGIC BOMB, OR OTHER SOFTWARE ROUTINE
DESIGNED TO PERMIT UNAUTHORISED ACCESS, TO DISABLE, ERASE OR OTHERWISE HARM SOFTWARE, HARDWARE OR
DATA, OR TO PERFORM ANY SUCH ACTIONS.

Note: The above may be subject to additional terms and conditions negotiated between V-Key and the
License.