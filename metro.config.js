const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  resolver: {
    assetExts: [
      ...require('@react-native/metro-config').getDefaultConfig(__dirname).resolver.assetExts,
      'lottie',
      'zip',
      'mp4',
      'webm',
      'wav',
      'mp3',
      'aac',
      'flac',
      'm4a',
      'ogg'
    ],
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
