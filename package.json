{"name": "TestVersion0753", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "test:platform": "node scripts/run-platform-tests.js", "test:platform:ios": "PLATFORM_OS=ios jest __tests__/platform/ --testNamePattern=\"iOS|Platform\"", "test:platform:android": "PLATFORM_OS=android jest __tests__/platform/ --testNamePattern=\"Android|Platform\"", "test:permissions": "node scripts/run-platform-tests.js suite permissions", "test:native-modules": "node scripts/run-platform-tests.js suite native-modules", "test:device-capabilities": "node scripts/run-platform-tests.js suite device-capabilities", "test:ui": "node scripts/run-platform-tests.js suite ui", "test:watch": "node scripts/run-platform-tests.js watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@reduxjs/toolkit": "^2.9.0", "axios": "^1.12.2", "lottie-react-native": "^7.3.4", "react": "18.3.1", "react-native": "0.75.3", "react-native-background-job": "^2.3.1", "react-native-battery": "^0.1.18", "react-native-device-info": "^14.1.1", "react-native-document-picker": "^9.3.1", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.28.0", "react-native-image-picker": "^8.2.1", "react-native-image-resizer": "^1.4.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.10.0", "react-native-network-info": "^5.2.1", "react-native-permissions": "^5.4.2", "react-native-reanimated": "^3.8.1", "react-native-share": "^12.2.0", "react-native-shimmer-placeholder": "^2.0.9", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "^15.13.0", "react-native-vguard": "./vkey/com-vkey-vguard", "react-native-video": "^6.16.1", "react-native-worklets-core": "^1.3.3", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.75.3", "@react-native/eslint-config": "0.75.3", "@react-native/metro-config": "0.75.3", "@react-native/typescript-config": "0.75.3", "@types/react": "^18.2.6", "@types/react-native": "^0.73.0", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "flipper-plugin-react-native-performance": "^0.6.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}