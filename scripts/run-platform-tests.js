#!/usr/bin/env node

/**
 * Platform-Specific Test Runner Script
 * Runs comprehensive platform tests for iOS and Android
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class PlatformTestScript {
  constructor() {
    this.testResults = {
      ios: null,
      android: null,
      summary: null,
    };
    this.startTime = Date.now();
  }

  /**
   * Main execution function
   */
  async run() {
    console.log('🚀 Starting Platform-Specific Test Suite');
    console.log('=========================================\n');

    try {
      // Check prerequisites
      this.checkPrerequisites();

      // Run tests for both platforms
      await this.runIOSTests();
      await this.runAndroidTests();

      // Generate final report
      this.generateFinalReport();

      console.log('\n✅ Platform tests completed successfully!');
      process.exit(0);
    } catch (error) {
      console.error('\n❌ Platform tests failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Check prerequisites
   */
  checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');

    // Check if Jest is installed
    try {
      execSync('npx jest --version', { stdio: 'pipe' });
      console.log('✅ Jest is available');
    } catch (error) {
      throw new Error('Jest is not installed. Run: npm install --save-dev jest');
    }

    // Check if test files exist
    const testFiles = [
      '__tests__/platform/PlatformTestUtils.js',
      '__tests__/platform/PermissionTests.test.js',
      '__tests__/platform/NativeModuleTests.test.js',
      '__tests__/platform/DeviceCapabilityTests.test.js',
      '__tests__/platform/PlatformUITests.test.js',
    ];

    for (const testFile of testFiles) {
      if (!fs.existsSync(testFile)) {
        throw new Error(`Test file not found: ${testFile}`);
      }
    }

    console.log('✅ All test files are present');
    console.log('✅ Prerequisites check passed\n');
  }

  /**
   * Run iOS-specific tests
   */
  async runIOSTests() {
    console.log('📱 Running iOS Platform Tests');
    console.log('-----------------------------');

    try {
      // Set platform environment
      process.env.PLATFORM_OS = 'ios';

      // Run Jest with iOS-specific configuration
      const result = execSync(
        'npx jest __tests__/platform/ --testNamePattern="iOS|Platform" --verbose --coverage',
        {
          stdio: 'pipe',
          encoding: 'utf8',
          env: { ...process.env, PLATFORM_OS: 'ios' },
        }
      );

      this.testResults.ios = {
        status: 'PASSED',
        output: result,
        timestamp: Date.now(),
      };

      console.log('✅ iOS tests completed successfully');
    } catch (error) {
      this.testResults.ios = {
        status: 'FAILED',
        output: error.stdout || error.message,
        error: error.stderr || error.message,
        timestamp: Date.now(),
      };

      console.log('❌ iOS tests failed');
      console.log(error.stdout || error.message);
    }
  }

  /**
   * Run Android-specific tests
   */
  async runAndroidTests() {
    console.log('\n🤖 Running Android Platform Tests');
    console.log('----------------------------------');

    try {
      // Set platform environment
      process.env.PLATFORM_OS = 'android';

      // Run Jest with Android-specific configuration
      const result = execSync(
        'npx jest __tests__/platform/ --testNamePattern="Android|Platform" --verbose --coverage',
        {
          stdio: 'pipe',
          encoding: 'utf8',
          env: { ...process.env, PLATFORM_OS: 'android' },
        }
      );

      this.testResults.android = {
        status: 'PASSED',
        output: result,
        timestamp: Date.now(),
      };

      console.log('✅ Android tests completed successfully');
    } catch (error) {
      this.testResults.android = {
        status: 'FAILED',
        output: error.stdout || error.message,
        error: error.stderr || error.message,
        timestamp: Date.now(),
      };

      console.log('❌ Android tests failed');
      console.log(error.stdout || error.message);
    }
  }

  /**
   * Generate final test report
   */
  generateFinalReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;

    const summary = {
      totalDuration: `${duration}ms`,
      startTime: new Date(this.startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      platforms: {
        ios: this.testResults.ios?.status || 'NOT_RUN',
        android: this.testResults.android?.status || 'NOT_RUN',
      },
      overallStatus: this.getOverallStatus(),
    };

    this.testResults.summary = summary;

    // Log summary
    console.log('\n📊 Platform Test Summary');
    console.log('========================');
    console.log(`Duration: ${summary.totalDuration}`);
    console.log(`iOS Tests: ${summary.platforms.ios}`);
    console.log(`Android Tests: ${summary.platforms.android}`);
    console.log(`Overall Status: ${summary.overallStatus}`);

    // Save detailed report
    this.saveDetailedReport();

    // Generate recommendations
    this.generateRecommendations();
  }

  /**
   * Get overall test status
   */
  getOverallStatus() {
    const iosStatus = this.testResults.ios?.status;
    const androidStatus = this.testResults.android?.status;

    if (iosStatus === 'PASSED' && androidStatus === 'PASSED') {
      return 'ALL_PASSED';
    } else if (iosStatus === 'FAILED' || androidStatus === 'FAILED') {
      return 'SOME_FAILED';
    } else {
      return 'INCOMPLETE';
    }
  }

  /**
   * Save detailed report to file
   */
  saveDetailedReport() {
    const reportPath = path.join(process.cwd(), 'platform-test-report.json');
    
    try {
      fs.writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));
      console.log(`📄 Detailed report saved to: ${reportPath}`);
    } catch (error) {
      console.warn(`⚠️  Could not save detailed report: ${error.message}`);
    }
  }

  /**
   * Generate recommendations based on test results
   */
  generateRecommendations() {
    const recommendations = [];

    // Check iOS test results
    if (this.testResults.ios?.status === 'FAILED') {
      recommendations.push({
        platform: 'iOS',
        issue: 'iOS tests failed',
        suggestion: 'Check iOS-specific permissions in Info.plist and native module linking',
        priority: 'HIGH',
      });
    }

    // Check Android test results
    if (this.testResults.android?.status === 'FAILED') {
      recommendations.push({
        platform: 'Android',
        issue: 'Android tests failed',
        suggestion: 'Check Android permissions in AndroidManifest.xml and Gradle configuration',
        priority: 'HIGH',
      });
    }

    // Overall recommendations
    if (recommendations.length === 0) {
      recommendations.push({
        platform: 'Both',
        issue: 'All tests passed',
        suggestion: 'Consider adding more comprehensive integration tests',
        priority: 'LOW',
      });
    }

    if (recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. [${rec.priority}] ${rec.platform}: ${rec.suggestion}`);
      });
    }
  }

  /**
   * Run specific test suite
   */
  static async runSpecificSuite(suiteName) {
    console.log(`🎯 Running specific test suite: ${suiteName}`);

    const suiteMap = {
      permissions: '__tests__/platform/PermissionTests.test.js',
      'native-modules': '__tests__/platform/NativeModuleTests.test.js',
      'device-capabilities': '__tests__/platform/DeviceCapabilityTests.test.js',
      ui: '__tests__/platform/PlatformUITests.test.js',
    };

    const testFile = suiteMap[suiteName];
    if (!testFile) {
      throw new Error(`Unknown test suite: ${suiteName}. Available: ${Object.keys(suiteMap).join(', ')}`);
    }

    try {
      const result = execSync(`npx jest ${testFile} --verbose`, {
        stdio: 'inherit',
        encoding: 'utf8',
      });

      console.log(`✅ ${suiteName} tests completed successfully`);
    } catch (error) {
      console.error(`❌ ${suiteName} tests failed`);
      throw error;
    }
  }

  /**
   * Run tests in watch mode
   */
  static runWatchMode() {
    console.log('👀 Running platform tests in watch mode...');

    try {
      execSync('npx jest __tests__/platform/ --watch --verbose', {
        stdio: 'inherit',
      });
    } catch (error) {
      console.error('❌ Watch mode failed:', error.message);
      process.exit(1);
    }
  }
}

// CLI handling
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];

  if (command === 'suite' && args[1]) {
    PlatformTestScript.runSpecificSuite(args[1])
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  } else if (command === 'watch') {
    PlatformTestScript.runWatchMode();
  } else {
    const runner = new PlatformTestScript();
    runner.run();
  }
}

module.exports = PlatformTestScript;
