/**
 * AccessibleButton - Fully accessible button component
 * Supports VoiceOver, TalkBack, and keyboard navigation
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  AccessibilityRole,
  AccessibilityState,
  Platform,
} from 'react-native';
import { AppTheme } from '../styles/AppTheme';

interface AccessibleButtonProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
  size?: 'small' | 'medium' | 'large';
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  testID?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  title,
  onPress,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'medium',
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  testID,
  style,
  textStyle,
  icon,
  fullWidth = false,
}) => {
  const getButtonStyle = (): ViewStyle => {
    const baseStyle = styles.button;
    const sizeStyle = styles[`${size}Button` as keyof typeof styles] as ViewStyle;
    const variantStyle = styles[`${variant}Button` as keyof typeof styles] as ViewStyle;
    
    let stateStyle: ViewStyle = {};
    if (disabled || loading) {
      stateStyle = styles.disabledButton;
    }

    let widthStyle: ViewStyle = {};
    if (fullWidth) {
      widthStyle = styles.fullWidthButton;
    }

    return {
      ...baseStyle,
      ...sizeStyle,
      ...variantStyle,
      ...stateStyle,
      ...widthStyle,
      ...style,
    };
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle = styles.buttonText;
    const sizeStyle = styles[`${size}ButtonText` as keyof typeof styles] as TextStyle;
    const variantStyle = styles[`${variant}ButtonText` as keyof typeof styles] as TextStyle;
    
    let stateStyle: TextStyle = {};
    if (disabled || loading) {
      stateStyle = styles.disabledButtonText;
    }

    return {
      ...baseStyle,
      ...sizeStyle,
      ...variantStyle,
      ...stateStyle,
      ...textStyle,
    };
  };

  const getAccessibilityState = (): AccessibilityState => {
    return {
      disabled: disabled || loading,
      busy: loading,
    };
  };

  const getAccessibilityLabel = (): string => {
    if (accessibilityLabel) {
      return accessibilityLabel;
    }
    
    let label = title;
    if (loading) {
      label += ', loading';
    }
    if (disabled) {
      label += ', disabled';
    }
    
    return label;
  };

  const getAccessibilityHint = (): string | undefined => {
    if (accessibilityHint) {
      return accessibilityHint;
    }
    
    if (disabled) {
      return 'Button is currently disabled';
    }
    
    if (loading) {
      return 'Button is currently loading, please wait';
    }
    
    return `Activates ${title.toLowerCase()}`;
  };

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled || loading}
      accessible={true}
      accessibilityRole={accessibilityRole}
      accessibilityLabel={getAccessibilityLabel()}
      accessibilityHint={getAccessibilityHint()}
      accessibilityState={getAccessibilityState()}
      testID={testID}
      activeOpacity={0.7}
    >
      {icon && <>{icon}</>}
      <Text style={getTextStyle()}>
        {loading ? 'Loading...' : title}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: AppTheme.borderRadius.medium,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingHorizontal: AppTheme.spacing.medium,
    paddingVertical: AppTheme.spacing.small,
    minHeight: 44, // iOS accessibility minimum
    ...Platform.select({
      ios: {
        shadowColor: AppTheme.colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  fullWidthButton: {
    width: '100%',
  },
  
  // Size variants
  smallButton: {
    paddingHorizontal: AppTheme.spacing.small,
    paddingVertical: AppTheme.spacing.xsmall,
    minHeight: 36,
  },
  mediumButton: {
    paddingHorizontal: AppTheme.spacing.medium,
    paddingVertical: AppTheme.spacing.small,
    minHeight: 44,
  },
  largeButton: {
    paddingHorizontal: AppTheme.spacing.large,
    paddingVertical: AppTheme.spacing.medium,
    minHeight: 52,
  },
  
  // Color variants
  primaryButton: {
    backgroundColor: AppTheme.colors.primary,
  },
  secondaryButton: {
    backgroundColor: AppTheme.colors.secondary,
    borderWidth: 1,
    borderColor: AppTheme.colors.border,
  },
  dangerButton: {
    backgroundColor: AppTheme.colors.error,
  },
  successButton: {
    backgroundColor: AppTheme.colors.success,
  },
  
  // Disabled state
  disabledButton: {
    backgroundColor: AppTheme.colors.disabled,
    opacity: 0.6,
  },
  
  // Text styles
  buttonText: {
    fontFamily: AppTheme.fonts.medium,
    textAlign: 'center',
    marginLeft: AppTheme.spacing.xsmall,
  },
  
  // Text size variants
  smallButtonText: {
    fontSize: AppTheme.fontSizes.small,
    lineHeight: AppTheme.lineHeights.small,
  },
  mediumButtonText: {
    fontSize: AppTheme.fontSizes.medium,
    lineHeight: AppTheme.lineHeights.medium,
  },
  largeButtonText: {
    fontSize: AppTheme.fontSizes.large,
    lineHeight: AppTheme.lineHeights.large,
  },
  
  // Text color variants
  primaryButtonText: {
    color: AppTheme.colors.white,
  },
  secondaryButtonText: {
    color: AppTheme.colors.text,
  },
  dangerButtonText: {
    color: AppTheme.colors.white,
  },
  successButtonText: {
    color: AppTheme.colors.white,
  },
  
  // Disabled text
  disabledButtonText: {
    color: AppTheme.colors.textSecondary,
  },
});

export default AccessibleButton;
