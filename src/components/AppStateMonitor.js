/**
 * AppStateMonitor Component
 * Monitors app state changes and system metrics
 */

import React, { useEffect } from 'react';
import { Platform } from 'react-native';
import { useDispatch } from 'react-redux';
import DeviceInfo from 'react-native-device-info';
import NetInfo from '@react-native-community/netinfo';
import LoggerService from '../services/LoggerService';
import { LogLevel, LogCategory } from '../models/LogEntry';
import { addLog } from '../store/slices/logSlice';
import useAppStateLogger from '../hooks/useAppStateLogger';

const AppStateMonitor = ({ children }) => {
  const dispatch = useDispatch();
  const appStateInfo = useAppStateLogger();

  // Network monitoring
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const networkData = {
        type: state.type,
        isConnected: state.isConnected,
        isInternetReachable: state.isInternetReachable,
        details: state.details,
        timestamp: Date.now()
      };

      const logEntry = {
        id: `network_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: state.isConnected ? LogLevel.INFO : LogLevel.WARNING,
        category: LogCategory.NETWORK_REQUESTS,
        component: 'NetworkMonitor',
        message: `Network ${state.isConnected ? 'connected' : 'disconnected'}: ${state.type}`,
        data: networkData
      };

      LoggerService.log(
        logEntry.level,
        logEntry.category,
        logEntry.component,
        logEntry.message,
        networkData
      );

      dispatch(addLog(logEntry));
    });

    return unsubscribe;
  }, [dispatch]);

  // Device info monitoring
  useEffect(() => {
    const collectDeviceMetrics = async () => {
      try {
        const deviceMetrics = {
          brand: await DeviceInfo.getBrand(),
          model: await DeviceInfo.getModel(),
          systemVersion: await DeviceInfo.getSystemVersion(),
          buildNumber: await DeviceInfo.getBuildNumber(),
          bundleId: await DeviceInfo.getBundleId(),
          version: await DeviceInfo.getVersion(),
          readableVersion: await DeviceInfo.getReadableVersion(),
          deviceId: await DeviceInfo.getDeviceId(),
          isEmulator: await DeviceInfo.isEmulator(),
          totalMemory: await DeviceInfo.getTotalMemory(),
          usedMemory: await DeviceInfo.getUsedMemory(),
          platform: Platform.OS,
          platformVersion: Platform.Version,
          timestamp: Date.now()
        };

        const logEntry = {
          id: `device_info_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: LogLevel.INFO,
          category: LogCategory.DEVICE_INFO,
          component: 'DeviceMonitor',
          message: 'Device information collected',
          data: deviceMetrics
        };

        LoggerService.log(
          LogLevel.INFO,
          LogCategory.DEVICE_INFO,
          'DeviceMonitor',
          'Device information collected',
          deviceMetrics
        );

        dispatch(addLog(logEntry));

        // Log emulator detection
        if (deviceMetrics.isEmulator) {
          const emulatorLogEntry = {
            id: `emulator_detected_${Date.now()}`,
            timestamp: new Date().toISOString(),
            level: LogLevel.WARNING,
            category: LogCategory.DEVICE_INFO,
            component: 'DeviceMonitor',
            message: 'Running on emulator/simulator',
            data: {
              isEmulator: true,
              platform: Platform.OS,
              model: deviceMetrics.model
            }
          };

          LoggerService.log(
            LogLevel.WARNING,
            LogCategory.DEVICE_INFO,
            'DeviceMonitor',
            'Running on emulator/simulator',
            emulatorLogEntry.data
          );

          dispatch(addLog(emulatorLogEntry));
        }

      } catch (error) {
        const errorLogEntry = {
          id: `device_info_error_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: LogLevel.ERROR,
          category: LogCategory.DEVICE_INFO,
          component: 'DeviceMonitor',
          message: 'Failed to collect device information',
          data: { error: error.message }
        };

        LoggerService.error('DeviceMonitor', 'Failed to collect device information', error);
        dispatch(addLog(errorLogEntry));
      }
    };

    collectDeviceMetrics();
  }, [dispatch]);

  // System resource monitoring
  useEffect(() => {
    const monitorSystemResources = async () => {
      try {
        const [totalMemory, usedMemory] = await Promise.all([
          DeviceInfo.getTotalMemory(),
          DeviceInfo.getUsedMemory()
        ]);

        const resourceData = {
          memory: {
            total: totalMemory,
            used: usedMemory,
            free: totalMemory - usedMemory,
            usagePercentage: (usedMemory / totalMemory) * 100
          },
          timestamp: Date.now(),
          appState: appStateInfo.appState,
          sessionDuration: appStateInfo.sessionDuration
        };

        // Only log if memory usage is concerning
        if (resourceData.memory.usagePercentage > 75) {
          const logEntry = {
            id: `resource_warning_${Date.now()}`,
            timestamp: new Date().toISOString(),
            level: resourceData.memory.usagePercentage > 90 ? LogLevel.ERROR : LogLevel.WARNING,
            category: LogCategory.PERFORMANCE_METRICS,
            component: 'ResourceMonitor',
            message: `High resource usage: ${resourceData.memory.usagePercentage.toFixed(1)}% memory`,
            data: resourceData
          };

          LoggerService.log(
            logEntry.level,
            logEntry.category,
            logEntry.component,
            logEntry.message,
            resourceData
          );

          dispatch(addLog(logEntry));
        }

      } catch (error) {
        LoggerService.error('ResourceMonitor', 'Failed to monitor system resources', error);
      }
    };

    // Monitor resources every 30 seconds
    const resourceInterval = setInterval(monitorSystemResources, 30000);
    monitorSystemResources(); // Initial check

    return () => clearInterval(resourceInterval);
  }, [dispatch, appStateInfo.appState, appStateInfo.sessionDuration]);

  // Error boundary logging
  useEffect(() => {
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    console.error = (...args) => {
      const errorMessage = args.join(' ');
      
      const logEntry = {
        id: `console_error_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.ERROR,
        category: LogCategory.ERROR_HANDLING,
        component: 'ConsoleMonitor',
        message: 'Console error detected',
        data: {
          errorMessage,
          args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg))
        }
      };

      LoggerService.error('ConsoleMonitor', 'Console error detected', { errorMessage });
      dispatch(addLog(logEntry));

      originalConsoleError.apply(console, args);
    };

    console.warn = (...args) => {
      const warningMessage = args.join(' ');
      
      const logEntry = {
        id: `console_warning_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.WARNING,
        category: LogCategory.ERROR_HANDLING,
        component: 'ConsoleMonitor',
        message: 'Console warning detected',
        data: {
          warningMessage,
          args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg))
        }
      };

      LoggerService.warn('ConsoleMonitor', 'Console warning detected', { warningMessage });
      dispatch(addLog(logEntry));

      originalConsoleWarn.apply(console, args);
    };

    return () => {
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
    };
  }, [dispatch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      const logEntry = {
        id: `monitor_cleanup_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.APP_LIFECYCLE,
        component: 'AppStateMonitor',
        message: 'App state monitor cleanup',
        data: {
          sessionDuration: appStateInfo.sessionDuration,
          finalAppState: appStateInfo.appState,
          memoryInfo: appStateInfo.memoryInfo
        }
      };

      LoggerService.info('AppStateMonitor', 'App state monitor cleanup', logEntry.data);
      dispatch(addLog(logEntry));
    };
  }, [dispatch, appStateInfo]);

  return children;
};

export default AppStateMonitor;
