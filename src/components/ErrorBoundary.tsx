/**
 * ErrorBoundary - Comprehensive error boundary with accessibility
 * Catches JavaScript errors and provides user-friendly error handling
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';

// Services
import LoggerService from '../services/LoggerService';

// Components
import AccessibleButton from './AccessibleButton';

// Types
import { LogLevel, LogCategory } from '../models/LogEntry';

// Theme
import { AppTheme } from '../styles/AppTheme';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error
    this.logError(error, errorInfo);
    
    // Update state with error info
    this.setState({
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  /**
   * Log error details
   */
  private logError = (error: Error, errorInfo: ErrorInfo) => {
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: 'ErrorBoundary',
      timestamp: new Date().toISOString(),
      platform: Platform.OS,
      errorId: this.state.errorId,
    };

    LoggerService.log(
      LogLevel.ERROR,
      LogCategory.ERROR,
      'ErrorBoundary',
      'JavaScript error caught by error boundary',
      errorDetails
    );

    // Also log to console for development
    if (__DEV__) {
      console.error('ErrorBoundary caught an error:', error);
      console.error('Component stack:', errorInfo.componentStack);
    }
  };

  /**
   * Reset error boundary
   */
  private resetErrorBoundary = () => {
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.ERROR,
      'ErrorBoundary',
      'Error boundary reset by user',
      { errorId: this.state.errorId }
    );

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  /**
   * Report error to support
   */
  private reportError = () => {
    const { error, errorInfo, errorId } = this.state;
    
    if (!error || !errorInfo) return;

    const errorReport = {
      errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      platform: Platform.OS,
      version: '1.0.0', // App version
    };

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.ERROR,
      'ErrorBoundary',
      'Error report generated',
      { errorId }
    );

    Alert.alert(
      'Error Report',
      `Error report generated with ID: ${errorId}\n\nThis information has been logged for debugging purposes.`,
      [{ text: 'OK' }]
    );
  };

  /**
   * Get error severity
   */
  private getErrorSeverity = (error: Error): 'low' | 'medium' | 'high' | 'critical' => {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // Critical errors
    if (
      message.includes('out of memory') ||
      message.includes('maximum call stack') ||
      stack.includes('native')
    ) {
      return 'critical';
    }

    // High severity errors
    if (
      message.includes('network') ||
      message.includes('permission') ||
      message.includes('security')
    ) {
      return 'high';
    }

    // Medium severity errors
    if (
      message.includes('render') ||
      message.includes('component') ||
      message.includes('props')
    ) {
      return 'medium';
    }

    // Default to low severity
    return 'low';
  };

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage = (error: Error): string => {
    const severity = this.getErrorSeverity(error);
    const message = error.message.toLowerCase();

    if (severity === 'critical') {
      return 'A critical error occurred. Please restart the app.';
    }

    if (message.includes('network')) {
      return 'A network error occurred. Please check your connection and try again.';
    }

    if (message.includes('permission')) {
      return 'A permission error occurred. Please check app permissions in settings.';
    }

    if (message.includes('camera')) {
      return 'A camera error occurred. Please ensure camera permissions are granted.';
    }

    return 'An unexpected error occurred. Please try again.';
  };

  render() {
    if (this.state.hasError) {
      const { error } = this.state;
      
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const severity = error ? this.getErrorSeverity(error) : 'medium';
      const userMessage = error ? this.getUserFriendlyMessage(error) : 'An error occurred';

      return (
        <View style={styles.container}>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            accessible={true}
            accessibilityLabel="Error screen"
          >
            <View style={styles.errorContainer}>
              {/* Error Icon */}
              <View style={[styles.errorIcon, styles[`${severity}ErrorIcon`]]}>
                <Text style={styles.errorIconText}>⚠️</Text>
              </View>

              {/* Error Title */}
              <Text
                style={styles.errorTitle}
                accessible={true}
                accessibilityRole="header"
                accessibilityLabel="Error occurred"
              >
                Oops! Something went wrong
              </Text>

              {/* User-friendly message */}
              <Text
                style={styles.errorMessage}
                accessible={true}
                accessibilityLabel={`Error message: ${userMessage}`}
              >
                {userMessage}
              </Text>

              {/* Error ID for support */}
              {this.state.errorId && (
                <Text
                  style={styles.errorId}
                  accessible={true}
                  accessibilityLabel={`Error ID: ${this.state.errorId}`}
                >
                  Error ID: {this.state.errorId}
                </Text>
              )}

              {/* Action buttons */}
              <View style={styles.actionContainer}>
                <AccessibleButton
                  title="Try Again"
                  onPress={this.resetErrorBoundary}
                  variant="primary"
                  size="large"
                  fullWidth
                  accessibilityLabel="Try again, reset the error"
                  accessibilityHint="Attempts to recover from the error and continue using the app"
                  testID="error-boundary-retry"
                />

                <AccessibleButton
                  title="Report Error"
                  onPress={this.reportError}
                  variant="secondary"
                  size="medium"
                  fullWidth
                  accessibilityLabel="Report this error"
                  accessibilityHint="Generates an error report for debugging purposes"
                  testID="error-boundary-report"
                />
              </View>

              {/* Development error details */}
              {__DEV__ && error && (
                <View style={styles.devErrorContainer}>
                  <Text style={styles.devErrorTitle}>Development Error Details:</Text>
                  <Text style={styles.devErrorText} selectable>
                    {error.message}
                  </Text>
                  {error.stack && (
                    <Text style={styles.devErrorStack} selectable>
                      {error.stack}
                    </Text>
                  )}
                </View>
              )}
            </View>
          </ScrollView>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppTheme.colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: AppTheme.spacing.large,
  },
  errorContainer: {
    alignItems: 'center',
    maxWidth: 400,
    alignSelf: 'center',
  },
  errorIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: AppTheme.spacing.large,
  },
  lowErrorIcon: {
    backgroundColor: AppTheme.colors.warning + '20',
  },
  mediumErrorIcon: {
    backgroundColor: AppTheme.colors.error + '20',
  },
  highErrorIcon: {
    backgroundColor: AppTheme.colors.error + '30',
  },
  criticalErrorIcon: {
    backgroundColor: AppTheme.colors.error + '40',
  },
  errorIconText: {
    fontSize: 40,
  },
  errorTitle: {
    fontSize: AppTheme.fontSizes.xxlarge,
    fontFamily: AppTheme.fonts.bold,
    color: AppTheme.colors.text,
    textAlign: 'center',
    marginBottom: AppTheme.spacing.medium,
  },
  errorMessage: {
    fontSize: AppTheme.fontSizes.medium,
    fontFamily: AppTheme.fonts.regular,
    color: AppTheme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: AppTheme.lineHeights.medium,
    marginBottom: AppTheme.spacing.large,
  },
  errorId: {
    fontSize: AppTheme.fontSizes.small,
    fontFamily: AppTheme.fonts.regular,
    color: AppTheme.colors.textTertiary,
    textAlign: 'center',
    marginBottom: AppTheme.spacing.large,
  },
  actionContainer: {
    width: '100%',
    gap: AppTheme.spacing.medium,
  },
  devErrorContainer: {
    marginTop: AppTheme.spacing.xlarge,
    padding: AppTheme.spacing.medium,
    backgroundColor: AppTheme.colors.backgroundSecondary,
    borderRadius: AppTheme.borderRadius.medium,
    width: '100%',
  },
  devErrorTitle: {
    fontSize: AppTheme.fontSizes.medium,
    fontFamily: AppTheme.fonts.bold,
    color: AppTheme.colors.error,
    marginBottom: AppTheme.spacing.small,
  },
  devErrorText: {
    fontSize: AppTheme.fontSizes.small,
    fontFamily: AppTheme.fonts.regular,
    color: AppTheme.colors.text,
    marginBottom: AppTheme.spacing.small,
  },
  devErrorStack: {
    fontSize: AppTheme.fontSizes.xsmall,
    fontFamily: Platform.select({
      ios: 'Menlo',
      android: 'monospace',
    }),
    color: AppTheme.colors.textSecondary,
    backgroundColor: AppTheme.colors.backgroundTertiary,
    padding: AppTheme.spacing.small,
    borderRadius: AppTheme.borderRadius.small,
  },
});

export default ErrorBoundary;
