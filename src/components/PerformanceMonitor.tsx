/**
 * PerformanceMonitor - Real-time performance monitoring component
 * Monitors app performance metrics and provides accessibility feedback
 */

import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  AppState,
  AppStateStatus,
  Platform,
} from 'react-native';

// Services
import LoggerService from '../services/LoggerService';
import HeavyComputationService from '../services/HeavyComputationService';

// Types
import { LogLevel, LogCategory } from '../models/LogEntry';

// Theme
import { AppTheme } from '../styles/AppTheme';

interface PerformanceMetrics {
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  cpuUsage: number;
  frameRate: number;
  networkLatency: number;
  batteryLevel: number;
  thermalState: string;
  timestamp: number;
}

interface PerformanceAlert {
  id: string;
  type: 'memory' | 'cpu' | 'network' | 'battery' | 'thermal';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: number;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const metricsHistoryRef = useRef<PerformanceMetrics[]>([]);
  const alertCountRef = useRef(0);

  useEffect(() => {
    startMonitoring();
    setupAppStateListener();
    
    return () => {
      stopMonitoring();
    };
  }, []);

  /**
   * Start performance monitoring
   */
  const startMonitoring = () => {
    if (isMonitoring) return;

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.PERFORMANCE,
      'PerformanceMonitor',
      'Starting performance monitoring'
    );

    setIsMonitoring(true);
    
    // Collect metrics every 5 seconds
    intervalRef.current = setInterval(() => {
      collectMetrics();
    }, 5000);

    // Initial collection
    collectMetrics();
  };

  /**
   * Stop performance monitoring
   */
  const stopMonitoring = () => {
    if (!isMonitoring) return;

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.PERFORMANCE,
      'PerformanceMonitor',
      'Stopping performance monitoring'
    );

    setIsMonitoring(false);
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  /**
   * Setup app state listener
   */
  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        startMonitoring();
      } else if (nextAppState === 'background') {
        // Continue monitoring in background but less frequently
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = setInterval(() => {
            collectMetrics();
          }, 30000); // Every 30 seconds in background
        }
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  };

  /**
   * Collect performance metrics
   */
  const collectMetrics = async () => {
    try {
      const timestamp = Date.now();
      
      // Simulate memory usage calculation
      const memoryUsage = await getMemoryUsage();
      
      // Simulate CPU usage calculation
      const cpuUsage = await getCPUUsage();
      
      // Simulate frame rate calculation
      const frameRate = await getFrameRate();
      
      // Simulate network latency
      const networkLatency = await getNetworkLatency();
      
      // Simulate battery level
      const batteryLevel = await getBatteryLevel();
      
      // Simulate thermal state
      const thermalState = await getThermalState();

      const newMetrics: PerformanceMetrics = {
        memoryUsage,
        cpuUsage,
        frameRate,
        networkLatency,
        batteryLevel,
        thermalState,
        timestamp,
      };

      setMetrics(newMetrics);
      
      // Store in history (keep last 100 entries)
      metricsHistoryRef.current.push(newMetrics);
      if (metricsHistoryRef.current.length > 100) {
        metricsHistoryRef.current.shift();
      }

      // Check for performance issues
      checkPerformanceAlerts(newMetrics);

      // Log metrics periodically
      if (metricsHistoryRef.current.length % 12 === 0) { // Every minute
        LoggerService.log(
          LogLevel.INFO,
          LogCategory.PERFORMANCE,
          'PerformanceMonitor',
          'Performance metrics collected',
          {
            memoryUsage: newMetrics.memoryUsage.percentage,
            cpuUsage: newMetrics.cpuUsage,
            frameRate: newMetrics.frameRate,
            networkLatency: newMetrics.networkLatency,
            batteryLevel: newMetrics.batteryLevel,
            thermalState: newMetrics.thermalState,
          }
        );
      }
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.PERFORMANCE,
        'PerformanceMonitor',
        'Failed to collect performance metrics',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
  };

  /**
   * Get memory usage
   */
  const getMemoryUsage = async (): Promise<{ used: number; total: number; percentage: number }> => {
    // Simulate memory usage calculation
    const computationStats = HeavyComputationService.getStatistics();
    const baseUsage = 50 + (computationStats.totalDataSets * 0.1); // MB
    const totalMemory = Platform.select({ ios: 4096, android: 3072 }) || 2048; // MB
    
    const used = Math.min(baseUsage + (Math.random() * 100), totalMemory * 0.9);
    const percentage = (used / totalMemory) * 100;
    
    return {
      used: Math.round(used),
      total: totalMemory,
      percentage: Math.round(percentage * 100) / 100,
    };
  };

  /**
   * Get CPU usage
   */
  const getCPUUsage = async (): Promise<number> => {
    // Simulate CPU usage based on computation service activity
    const computationStats = HeavyComputationService.getStatistics();
    const baseUsage = computationStats.isProcessing ? 60 : 10;
    const randomVariation = Math.random() * 30;
    
    return Math.min(Math.round(baseUsage + randomVariation), 100);
  };

  /**
   * Get frame rate
   */
  const getFrameRate = async (): Promise<number> => {
    // Simulate frame rate calculation
    const targetFPS = 60;
    const variation = Math.random() * 10 - 5; // ±5 FPS variation
    
    return Math.max(Math.round(targetFPS + variation), 30);
  };

  /**
   * Get network latency
   */
  const getNetworkLatency = async (): Promise<number> => {
    // Simulate network latency
    const baseLatency = 50; // ms
    const variation = Math.random() * 100;
    
    return Math.round(baseLatency + variation);
  };

  /**
   * Get battery level
   */
  const getBatteryLevel = async (): Promise<number> => {
    // Simulate battery level
    return Math.round(Math.random() * 100);
  };

  /**
   * Get thermal state
   */
  const getThermalState = async (): Promise<string> => {
    const states = ['nominal', 'fair', 'serious', 'critical'];
    const weights = [0.7, 0.2, 0.08, 0.02]; // Probability weights
    
    const random = Math.random();
    let cumulative = 0;
    
    for (let i = 0; i < states.length; i++) {
      cumulative += weights[i];
      if (random <= cumulative) {
        return states[i];
      }
    }
    
    return 'nominal';
  };

  /**
   * Check for performance alerts
   */
  const checkPerformanceAlerts = (metrics: PerformanceMetrics) => {
    const newAlerts: PerformanceAlert[] = [];

    // Memory usage alerts
    if (metrics.memoryUsage.percentage > 90) {
      newAlerts.push({
        id: `memory_${alertCountRef.current++}`,
        type: 'memory',
        severity: 'critical',
        message: `Memory usage is critically high: ${metrics.memoryUsage.percentage}%`,
        timestamp: metrics.timestamp,
      });
    } else if (metrics.memoryUsage.percentage > 75) {
      newAlerts.push({
        id: `memory_${alertCountRef.current++}`,
        type: 'memory',
        severity: 'high',
        message: `Memory usage is high: ${metrics.memoryUsage.percentage}%`,
        timestamp: metrics.timestamp,
      });
    }

    // CPU usage alerts
    if (metrics.cpuUsage > 85) {
      newAlerts.push({
        id: `cpu_${alertCountRef.current++}`,
        type: 'cpu',
        severity: 'high',
        message: `CPU usage is high: ${metrics.cpuUsage}%`,
        timestamp: metrics.timestamp,
      });
    }

    // Frame rate alerts
    if (metrics.frameRate < 30) {
      newAlerts.push({
        id: `frame_${alertCountRef.current++}`,
        type: 'cpu',
        severity: 'medium',
        message: `Frame rate is low: ${metrics.frameRate} FPS`,
        timestamp: metrics.timestamp,
      });
    }

    // Network latency alerts
    if (metrics.networkLatency > 500) {
      newAlerts.push({
        id: `network_${alertCountRef.current++}`,
        type: 'network',
        severity: 'medium',
        message: `Network latency is high: ${metrics.networkLatency}ms`,
        timestamp: metrics.timestamp,
      });
    }

    // Battery alerts
    if (metrics.batteryLevel < 10) {
      newAlerts.push({
        id: `battery_${alertCountRef.current++}`,
        type: 'battery',
        severity: 'high',
        message: `Battery level is low: ${metrics.batteryLevel}%`,
        timestamp: metrics.timestamp,
      });
    }

    // Thermal alerts
    if (metrics.thermalState === 'critical') {
      newAlerts.push({
        id: `thermal_${alertCountRef.current++}`,
        type: 'thermal',
        severity: 'critical',
        message: 'Device thermal state is critical',
        timestamp: metrics.timestamp,
      });
    } else if (metrics.thermalState === 'serious') {
      newAlerts.push({
        id: `thermal_${alertCountRef.current++}`,
        type: 'thermal',
        severity: 'high',
        message: 'Device thermal state is serious',
        timestamp: metrics.timestamp,
      });
    }

    // Add new alerts and log them
    if (newAlerts.length > 0) {
      setAlerts(prev => [...prev.slice(-10), ...newAlerts]); // Keep last 10 alerts
      
      newAlerts.forEach(alert => {
        LoggerService.log(
          alert.severity === 'critical' ? LogLevel.ERROR : 
          alert.severity === 'high' ? LogLevel.WARNING : LogLevel.INFO,
          LogCategory.PERFORMANCE,
          'PerformanceMonitor',
          `Performance alert: ${alert.message}`,
          { alertId: alert.id, type: alert.type, severity: alert.severity }
        );
      });
    }
  };

  /**
   * Get performance status color
   */
  const getPerformanceStatusColor = (): string => {
    if (!metrics) return AppTheme.colors.textSecondary;

    const criticalIssues = alerts.filter(a => a.severity === 'critical').length;
    const highIssues = alerts.filter(a => a.severity === 'high').length;

    if (criticalIssues > 0) return AppTheme.colors.error;
    if (highIssues > 0) return AppTheme.colors.warning;
    if (metrics.memoryUsage.percentage > 60 || metrics.cpuUsage > 60) return AppTheme.colors.warning;
    
    return AppTheme.colors.success;
  };

  // This component runs in the background and doesn't render visible UI
  // It only logs performance data and manages alerts
  return null;
};

const styles = StyleSheet.create({
  // Styles would be here if we had visible UI
  container: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'transparent',
  },
});

export default PerformanceMonitor;
