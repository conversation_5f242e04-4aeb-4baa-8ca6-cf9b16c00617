/**
 * SecurityMonitor - Real-time security monitoring component
 * Monitors security threats and provides accessibility feedback
 */

import React, { useEffect, useState, useRef } from 'react';
import {
  AppState,
  AppStateStatus,
  Platform,
  Alert,
} from 'react-native';

// Services
import LoggerService from '../services/LoggerService';
import VGuardService from '../services/VGuardService';

// Types
import { LogLevel, LogCategory } from '../models/LogEntry';

interface SecurityThreat {
  id: string;
  type: 'jailbreak' | 'debugger' | 'screen_recording' | 'overlay' | 'tampering' | 'emulator';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: number;
  resolved: boolean;
}

interface SecurityMetrics {
  deviceIntegrity: 'secure' | 'compromised' | 'unknown';
  debuggerDetected: boolean;
  screenRecordingDetected: boolean;
  overlayDetected: boolean;
  emulatorDetected: boolean;
  tamperingDetected: boolean;
  threatLevel: 'none' | 'low' | 'medium' | 'high' | 'critical';
  lastScanTime: number;
}

const SecurityMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    deviceIntegrity: 'unknown',
    debuggerDetected: false,
    screenRecordingDetected: false,
    overlayDetected: false,
    emulatorDetected: false,
    tamperingDetected: false,
    threatLevel: 'none',
    lastScanTime: 0,
  });
  
  const [threats, setThreats] = useState<SecurityThreat[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const threatCountRef = useRef(0);
  const lastAlertTimeRef = useRef(0);

  useEffect(() => {
    startMonitoring();
    setupAppStateListener();
    
    return () => {
      stopMonitoring();
    };
  }, []);

  /**
   * Start security monitoring
   */
  const startMonitoring = async () => {
    if (isMonitoring) return;

    try {
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.SECURITY,
        'SecurityMonitor',
        'Starting security monitoring'
      );

      setIsMonitoring(true);
      
      // Initial security scan
      await performSecurityScan();
      
      // Schedule regular scans every 30 seconds
      intervalRef.current = setInterval(() => {
        performSecurityScan();
      }, 30000);

    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.SECURITY,
        'SecurityMonitor',
        'Failed to start security monitoring',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
  };

  /**
   * Stop security monitoring
   */
  const stopMonitoring = () => {
    if (!isMonitoring) return;

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.SECURITY,
      'SecurityMonitor',
      'Stopping security monitoring'
    );

    setIsMonitoring(false);
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  /**
   * Setup app state listener
   */
  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // Perform immediate security scan when app becomes active
        performSecurityScan();
        startMonitoring();
      } else if (nextAppState === 'background') {
        // Continue monitoring in background but less frequently
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = setInterval(() => {
            performSecurityScan();
          }, 60000); // Every minute in background
        }
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  };

  /**
   * Perform comprehensive security scan
   */
  const performSecurityScan = async () => {
    try {
      const scanTime = Date.now();
      
      // Check device integrity
      const deviceIntegrity = await checkDeviceIntegrity();
      
      // Check for debugger
      const debuggerDetected = await checkDebugger();
      
      // Check for screen recording
      const screenRecordingDetected = await checkScreenRecording();
      
      // Check for overlay attacks
      const overlayDetected = await checkOverlay();
      
      // Check for emulator
      const emulatorDetected = await checkEmulator();
      
      // Check for tampering
      const tamperingDetected = await checkTampering();
      
      // Calculate threat level
      const threatLevel = calculateThreatLevel({
        deviceIntegrity,
        debuggerDetected,
        screenRecordingDetected,
        overlayDetected,
        emulatorDetected,
        tamperingDetected,
      });

      const newMetrics: SecurityMetrics = {
        deviceIntegrity,
        debuggerDetected,
        screenRecordingDetected,
        overlayDetected,
        emulatorDetected,
        tamperingDetected,
        threatLevel,
        lastScanTime: scanTime,
      };

      setMetrics(newMetrics);
      
      // Process security threats
      await processSecurityThreats(newMetrics);

      // Log security status periodically
      if (scanTime - metrics.lastScanTime > 300000) { // Every 5 minutes
        LoggerService.log(
          LogLevel.INFO,
          LogCategory.SECURITY,
          'SecurityMonitor',
          'Security scan completed',
          {
            deviceIntegrity,
            threatLevel,
            threatsDetected: [
              debuggerDetected && 'debugger',
              screenRecordingDetected && 'screen_recording',
              overlayDetected && 'overlay',
              emulatorDetected && 'emulator',
              tamperingDetected && 'tampering',
            ].filter(Boolean),
          }
        );
      }

    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.SECURITY,
        'SecurityMonitor',
        'Security scan failed',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
  };

  /**
   * Check device integrity
   */
  const checkDeviceIntegrity = async (): Promise<'secure' | 'compromised' | 'unknown'> => {
    try {
      // Use VGuard service to check device integrity
      const isJailbroken = await VGuardService.checkJailbreak();
      const isRooted = await VGuardService.checkRoot();
      
      if (isJailbroken || isRooted) {
        return 'compromised';
      }
      
      return 'secure';
    } catch (error) {
      return 'unknown';
    }
  };

  /**
   * Check for debugger
   */
  const checkDebugger = async (): Promise<boolean> => {
    try {
      return await VGuardService.checkDebugger();
    } catch (error) {
      return false;
    }
  };

  /**
   * Check for screen recording
   */
  const checkScreenRecording = async (): Promise<boolean> => {
    try {
      return await VGuardService.checkScreenRecording();
    } catch (error) {
      return false;
    }
  };

  /**
   * Check for overlay attacks
   */
  const checkOverlay = async (): Promise<boolean> => {
    try {
      return await VGuardService.checkOverlay();
    } catch (error) {
      return false;
    }
  };

  /**
   * Check for emulator
   */
  const checkEmulator = async (): Promise<boolean> => {
    try {
      return await VGuardService.checkEmulator();
    } catch (error) {
      return false;
    }
  };

  /**
   * Check for tampering
   */
  const checkTampering = async (): Promise<boolean> => {
    try {
      return await VGuardService.checkTampering();
    } catch (error) {
      return false;
    }
  };

  /**
   * Calculate overall threat level
   */
  const calculateThreatLevel = (checks: {
    deviceIntegrity: 'secure' | 'compromised' | 'unknown';
    debuggerDetected: boolean;
    screenRecordingDetected: boolean;
    overlayDetected: boolean;
    emulatorDetected: boolean;
    tamperingDetected: boolean;
  }): 'none' | 'low' | 'medium' | 'high' | 'critical' => {
    
    // Critical threats
    if (checks.deviceIntegrity === 'compromised' || checks.tamperingDetected) {
      return 'critical';
    }
    
    // High threats
    if (checks.debuggerDetected || checks.overlayDetected) {
      return 'high';
    }
    
    // Medium threats
    if (checks.emulatorDetected || checks.screenRecordingDetected) {
      return 'medium';
    }
    
    // Low threats
    if (checks.deviceIntegrity === 'unknown') {
      return 'low';
    }
    
    return 'none';
  };

  /**
   * Process security threats
   */
  const processSecurityThreats = async (metrics: SecurityMetrics) => {
    const newThreats: SecurityThreat[] = [];
    const timestamp = Date.now();

    // Check each threat type
    if (metrics.deviceIntegrity === 'compromised') {
      newThreats.push({
        id: `integrity_${threatCountRef.current++}`,
        type: 'jailbreak',
        severity: 'critical',
        message: 'Device integrity compromised - jailbreak/root detected',
        timestamp,
        resolved: false,
      });
    }

    if (metrics.debuggerDetected) {
      newThreats.push({
        id: `debugger_${threatCountRef.current++}`,
        type: 'debugger',
        severity: 'high',
        message: 'Debugger detected - potential reverse engineering attempt',
        timestamp,
        resolved: false,
      });
    }

    if (metrics.screenRecordingDetected) {
      newThreats.push({
        id: `recording_${threatCountRef.current++}`,
        type: 'screen_recording',
        severity: 'medium',
        message: 'Screen recording detected - potential data leakage',
        timestamp,
        resolved: false,
      });
    }

    if (metrics.overlayDetected) {
      newThreats.push({
        id: `overlay_${threatCountRef.current++}`,
        type: 'overlay',
        severity: 'high',
        message: 'Overlay attack detected - potential credential theft',
        timestamp,
        resolved: false,
      });
    }

    if (metrics.emulatorDetected) {
      newThreats.push({
        id: `emulator_${threatCountRef.current++}`,
        type: 'emulator',
        severity: 'medium',
        message: 'Emulator detected - potential automated attack',
        timestamp,
        resolved: false,
      });
    }

    if (metrics.tamperingDetected) {
      newThreats.push({
        id: `tampering_${threatCountRef.current++}`,
        type: 'tampering',
        severity: 'critical',
        message: 'Application tampering detected - integrity compromised',
        timestamp,
        resolved: false,
      });
    }

    // Add new threats
    if (newThreats.length > 0) {
      setThreats(prev => [...prev.slice(-20), ...newThreats]); // Keep last 20 threats
      
      // Log all threats
      newThreats.forEach(threat => {
        LoggerService.log(
          threat.severity === 'critical' ? LogLevel.ERROR : 
          threat.severity === 'high' ? LogLevel.WARNING : LogLevel.INFO,
          LogCategory.SECURITY,
          'SecurityMonitor',
          `Security threat detected: ${threat.message}`,
          { 
            threatId: threat.id, 
            type: threat.type, 
            severity: threat.severity,
            timestamp: threat.timestamp 
          }
        );
      });

      // Show user alert for critical threats (but not too frequently)
      const criticalThreats = newThreats.filter(t => t.severity === 'critical');
      if (criticalThreats.length > 0 && timestamp - lastAlertTimeRef.current > 60000) {
        lastAlertTimeRef.current = timestamp;
        showSecurityAlert(criticalThreats[0]);
      }
    }
  };

  /**
   * Show security alert to user
   */
  const showSecurityAlert = (threat: SecurityThreat) => {
    const title = 'Security Alert';
    const message = `${threat.message}\n\nFor your security, some features may be disabled.`;
    
    Alert.alert(
      title,
      message,
      [
        { text: 'OK', style: 'default' },
        { 
          text: 'More Info', 
          onPress: () => {
            LoggerService.log(
              LogLevel.INFO,
              LogCategory.SECURITY,
              'SecurityMonitor',
              'User requested more info about security threat',
              { threatId: threat.id }
            );
          }
        },
      ],
      { 
        cancelable: false,
        // Accessibility
        userInterfaceStyle: 'light',
      }
    );
  };

  /**
   * Get security status color
   */
  const getSecurityStatusColor = (): string => {
    switch (metrics.threatLevel) {
      case 'critical':
        return '#FF0000';
      case 'high':
        return '#FF6600';
      case 'medium':
        return '#FFAA00';
      case 'low':
        return '#FFDD00';
      case 'none':
      default:
        return '#00AA00';
    }
  };

  // This component runs in the background and doesn't render visible UI
  // It only monitors security and manages threats
  return null;
};

export default SecurityMonitor;
