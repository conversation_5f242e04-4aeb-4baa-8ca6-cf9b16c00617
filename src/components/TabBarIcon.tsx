/**
 * TabBarIcon - Accessible tab bar icon component
 * Provides consistent iconography with accessibility support
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { AppTheme } from '../styles/AppTheme';

interface TabBarIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({
  name,
  focused,
  color,
  size,
}) => {
  const getIconName = (baseName: string, focused: boolean): string => {
    const iconMap: { [key: string]: { focused: string; unfocused: string } } = {
      camera: {
        focused: 'camera',
        unfocused: 'camera-outline',
      },
      image: {
        focused: 'images',
        unfocused: 'images-outline',
      },
      settings: {
        focused: 'settings',
        unfocused: 'settings-outline',
      },
      list: {
        focused: 'list',
        unfocused: 'list-outline',
      },
      circle: {
        focused: 'ellipse',
        unfocused: 'ellipse-outline',
      },
    };

    const iconConfig = iconMap[baseName];
    if (iconConfig) {
      return focused ? iconConfig.focused : iconConfig.unfocused;
    }

    return baseName;
  };

  return (
    <View style={[styles.container, focused && styles.focusedContainer]}>
      <Icon
        name={getIconName(name, focused)}
        size={size}
        color={color}
        style={[styles.icon, focused && styles.focusedIcon]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 32,
    height: 32,
    borderRadius: AppTheme.borderRadius.small,
  },
  focusedContainer: {
    backgroundColor: AppTheme.colors.primaryLight + '20', // 20% opacity
  },
  icon: {
    textAlign: 'center',
  },
  focusedIcon: {
    // Additional styling for focused state if needed
  },
});

export default TabBarIcon;
