/**
 * CameraControls - Camera control interface with animations
 * Provides capture, mode switching, and camera controls
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Animated,
  Dimensions
} from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import LoggerService from '../../services/LoggerService';
import { LogLevel, LogCategory } from '../../models/LogEntry';
import { CameraMode, FlashMode, CameraDeviceType } from '../../types/CameraTypes';
import CameraUtils from '../../utils/CameraUtils';

const { width: screenWidth } = Dimensions.get('window');

const CameraControls = ({
  style,
  cameraMode,
  isRecording,
  isCapturing,
  flashMode,
  zoomLevel,
  availableDevices,
  currentDevice,
  onCapture,
  onModeChange,
  onDeviceSwitch,
  onFlashModeChange,
  onZoomChange,
  onSettingsPress,
  onOverlayToggle,
  ...props
}) => {
  const [showModeSelector, setShowModeSelector] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  
  // Animations
  const captureButtonScale = useRef(new Animated.Value(1)).current;
  const recordingPulse = useRef(new Animated.Value(1)).current;
  const modeSelectorOpacity = useRef(new Animated.Value(0)).current;
  const zoomSliderOpacity = useRef(new Animated.Value(0)).current;
  const controlsOpacity = useRef(new Animated.Value(1)).current;

  // Recording timer
  const recordingTimer = useRef(null);

  // Recording duration timer
  useEffect(() => {
    if (isRecording) {
      setRecordingDuration(0);
      recordingTimer.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      // Start recording pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(recordingPulse, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(recordingPulse, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
      recordingPulse.stopAnimation();
      recordingPulse.setValue(1);
      setRecordingDuration(0);
    }

    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
    };
  }, [isRecording]);

  // Mode selector animation
  useEffect(() => {
    Animated.timing(modeSelectorOpacity, {
      toValue: showModeSelector ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [showModeSelector]);

  // Handle capture button press
  const handleCapturePress = () => {
    if (isCapturing) return;

    // Animate capture button
    Animated.sequence([
      Animated.timing(captureButtonScale, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(captureButtonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    onCapture();

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.CAMERA_OPERATIONS,
      'CameraControls',
      'Capture button pressed',
      { mode: cameraMode, isRecording }
    );
  };

  // Handle mode change
  const handleModeChange = (mode) => {
    onModeChange(mode);
    setShowModeSelector(false);
    
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.CAMERA_OPERATIONS,
      'CameraControls',
      'Camera mode changed',
      { previousMode: cameraMode, newMode: mode }
    );
  };

  // Handle flash mode cycle
  const handleFlashModePress = () => {
    const modes = [FlashMode.OFF, FlashMode.AUTO, FlashMode.ON];
    const currentIndex = modes.indexOf(flashMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    
    onFlashModeChange(nextMode);
    
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.CAMERA_OPERATIONS,
      'CameraControls',
      'Flash mode changed',
      { previousMode: flashMode, newMode: nextMode }
    );
  };

  // Handle zoom gesture
  const handleZoomGesture = (event) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      const { translationY } = event.nativeEvent;
      const zoomDelta = -translationY / 100; // Convert gesture to zoom
      const newZoom = Math.max(1.0, Math.min(10.0, zoomLevel + zoomDelta));
      
      if (Math.abs(newZoom - zoomLevel) > 0.1) {
        onZoomChange(newZoom);
      }
    }
  };

  // Format recording duration
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get capture button style based on mode and state
  const getCaptureButtonStyle = () => {
    let backgroundColor = '#fff';
    let borderColor = '#fff';
    
    if (cameraMode === CameraMode.VIDEO) {
      backgroundColor = isRecording ? '#ff4444' : '#fff';
      borderColor = isRecording ? '#ff4444' : '#fff';
    }
    
    return {
      backgroundColor,
      borderColor,
      transform: [
        { scale: captureButtonScale },
        ...(isRecording ? [{ scale: recordingPulse }] : [])
      ]
    };
  };

  // Render mode selector
  const renderModeSelector = () => {
    const modes = [
      { key: CameraMode.PHOTO, label: 'Photo', icon: '📷' },
      { key: CameraMode.VIDEO, label: 'Video', icon: '🎥' },
      { key: CameraMode.PORTRAIT, label: 'Portrait', icon: '👤' },
      { key: CameraMode.NIGHT, label: 'Night', icon: '🌙' },
    ];

    return (
      <Animated.View style={[styles.modeSelector, { opacity: modeSelectorOpacity }]}>
        <LinearGradient
          colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
          style={styles.modeSelectorGradient}
        >
          {modes.map((mode) => (
            <TouchableOpacity
              key={mode.key}
              style={[
                styles.modeButton,
                cameraMode === mode.key && styles.activeModeButton
              ]}
              onPress={() => handleModeChange(mode.key)}
            >
              <Text style={styles.modeIcon}>{mode.icon}</Text>
              <Text style={[
                styles.modeLabel,
                cameraMode === mode.key && styles.activeModeLabel
              ]}>
                {mode.label}
              </Text>
            </TouchableOpacity>
          ))}
        </LinearGradient>
      </Animated.View>
    );
  };

  // Render recording indicator
  const renderRecordingIndicator = () => {
    if (!isRecording) return null;

    return (
      <View style={styles.recordingIndicator}>
        <Animated.View style={[styles.recordingDot, { transform: [{ scale: recordingPulse }] }]} />
        <Text style={styles.recordingText}>REC</Text>
        <Text style={styles.recordingDuration}>{formatDuration(recordingDuration)}</Text>
      </View>
    );
  };

  // Render zoom indicator
  const renderZoomIndicator = () => {
    if (zoomLevel <= 1.0) return null;

    return (
      <View style={styles.zoomIndicator}>
        <Text style={styles.zoomText}>{zoomLevel.toFixed(1)}x</Text>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]} {...props}>
      {/* Recording Indicator */}
      {renderRecordingIndicator()}
      
      {/* Zoom Indicator */}
      {renderZoomIndicator()}
      
      {/* Mode Selector */}
      {renderModeSelector()}
      
      {/* Main Controls */}
      <Animated.View style={[styles.mainControls, { opacity: controlsOpacity }]}>
        <LinearGradient
          colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)']}
          style={styles.controlsGradient}
        >
          {/* Left Controls */}
          <View style={styles.leftControls}>
            <TouchableOpacity
              style={styles.controlButton}
              onPress={() => setShowModeSelector(!showModeSelector)}
            >
              <Text style={styles.controlIcon}>{CameraUtils.getCameraModeIcon(cameraMode)}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.controlButton}
              onPress={handleFlashModePress}
            >
              <Text style={styles.controlIcon}>{CameraUtils.getFlashModeIcon(flashMode)}</Text>
            </TouchableOpacity>
          </View>
          
          {/* Center Capture Button */}
          <PanGestureHandler onGestureEvent={handleZoomGesture}>
            <Animated.View>
              <TouchableOpacity
                style={[styles.captureButton, getCaptureButtonStyle()]}
                onPress={handleCapturePress}
                disabled={isCapturing}
              >
                <View style={styles.captureButtonInner}>
                  {isCapturing && (
                    <View style={styles.captureProgress} />
                  )}
                </View>
              </TouchableOpacity>
            </Animated.View>
          </PanGestureHandler>
          
          {/* Right Controls */}
          <View style={styles.rightControls}>
            <TouchableOpacity
              style={styles.controlButton}
              onPress={onDeviceSwitch}
            >
              <Text style={styles.controlIcon}>🔄</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.controlButton}
              onPress={onSettingsPress}
            >
              <Text style={styles.controlIcon}>⚙️</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
  },
  recordingIndicator: {
    position: 'absolute',
    top: 10,
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ff4444',
    marginRight: 8,
  },
  recordingText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginRight: 8,
  },
  recordingDuration: {
    color: '#fff',
    fontSize: 12,
    fontFamily: 'monospace',
  },
  zoomIndicator: {
    position: 'absolute',
    top: 10,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  zoomText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  modeSelector: {
    position: 'absolute',
    bottom: 130,
    left: 20,
    right: 20,
    height: 80,
  },
  modeSelectorGradient: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderRadius: 40,
    paddingHorizontal: 20,
  },
  modeButton: {
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
  },
  activeModeButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  modeIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  modeLabel: {
    color: '#ccc',
    fontSize: 10,
    fontWeight: '500',
  },
  activeModeLabel: {
    color: '#fff',
    fontWeight: '600',
  },
  mainControls: {
    flex: 1,
  },
  controlsGradient: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 30,
    paddingVertical: 20,
  },
  leftControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  controlIcon: {
    fontSize: 20,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255,255,255,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureProgress: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
  },
});

export default CameraControls;
