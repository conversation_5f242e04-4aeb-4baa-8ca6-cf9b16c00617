/**
 * CameraOverlay - Information overlay for camera interface
 * Displays camera stats, performance metrics, and real-time information
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Text,
  Animated,
  Dimensions,
  TouchableOpacity
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import LoggerService from '../../services/LoggerService';
import { LogLevel, LogCategory } from '../../models/LogEntry';
import { CameraMode, FlashMode } from '../../types/CameraTypes';
import CameraUtils from '../../utils/CameraUtils';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const CameraOverlay = ({
  style,
  cameraMode,
  isRecording,
  zoomLevel,
  flashMode,
  statistics,
  performanceData,
  showGrid = false,
  showHistogram = false,
  ...props
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Animations
  const overlayOpacity = useRef(new Animated.Value(1)).current;
  const detailsOpacity = useRef(new Animated.Value(0)).current;
  const statsAnimation = useRef(new Animated.Value(0)).current;

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Animate stats updates
  useEffect(() => {
    Animated.sequence([
      Animated.timing(statsAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(statsAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, [statistics, performanceData]);

  // Details panel animation
  useEffect(() => {
    Animated.timing(detailsOpacity, {
      toValue: showDetails ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [showDetails]);

  // Toggle details panel
  const toggleDetails = () => {
    setShowDetails(!showDetails);
    
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.CAMERA_OPERATIONS,
      'CameraOverlay',
      'Details panel toggled',
      { showDetails: !showDetails }
    );
  };

  // Format file size
  const formatFileSize = (bytes) => {
    return CameraUtils.formatFileSize(bytes || 0);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  // Render top status bar
  const renderTopStatusBar = () => {
    return (
      <View style={styles.topStatusBar}>
        <LinearGradient
          colors={['rgba(0,0,0,0.6)', 'transparent']}
          style={styles.statusBarGradient}
        >
          <View style={styles.statusLeft}>
            <Text style={styles.timeText}>
              {currentTime.toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit',
                second: '2-digit'
              })}
            </Text>
            <Text style={styles.modeText}>
              {cameraMode.toUpperCase()}
            </Text>
          </View>
          
          <View style={styles.statusRight}>
            <TouchableOpacity
              style={styles.infoButton}
              onPress={toggleDetails}
            >
              <Text style={styles.infoIcon}>ℹ️</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </View>
    );
  };

  // Render camera stats
  const renderCameraStats = () => {
    if (!statistics) return null;

    return (
      <Animated.View style={[styles.statsContainer, { opacity: statsAnimation }]}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Photos</Text>
          <Text style={styles.statValue}>{statistics.totalPhotos || 0}</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Videos</Text>
          <Text style={styles.statValue}>{statistics.totalVideos || 0}</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Success</Text>
          <Text style={styles.statValue}>{formatPercentage(statistics.successRate)}</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Storage</Text>
          <Text style={styles.statValue}>{formatFileSize(statistics.totalStorageUsed)}</Text>
        </View>
      </Animated.View>
    );
  };

  // Render performance metrics
  const renderPerformanceMetrics = () => {
    if (!performanceData) return null;

    return (
      <View style={styles.performanceContainer}>
        <Text style={styles.performanceTitle}>Performance</Text>
        <View style={styles.performanceGrid}>
          <View style={styles.performanceItem}>
            <Text style={styles.performanceLabel}>FPS</Text>
            <Text style={styles.performanceValue}>
              {(performanceData.fps || 0).toFixed(1)}
            </Text>
          </View>
          <View style={styles.performanceItem}>
            <Text style={styles.performanceLabel}>Memory</Text>
            <Text style={styles.performanceValue}>
              {formatFileSize(performanceData.memoryUsage || 0)}
            </Text>
          </View>
          <View style={styles.performanceItem}>
            <Text style={styles.performanceLabel}>CPU</Text>
            <Text style={styles.performanceValue}>
              {formatPercentage(performanceData.cpuUsage)}
            </Text>
          </View>
          <View style={styles.performanceItem}>
            <Text style={styles.performanceLabel}>Battery</Text>
            <Text style={styles.performanceValue}>
              {formatPercentage(performanceData.batteryLevel)}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  // Render detailed information panel
  const renderDetailsPanel = () => {
    return (
      <Animated.View style={[styles.detailsPanel, { opacity: detailsOpacity }]}>
        <LinearGradient
          colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
          style={styles.detailsGradient}
        >
          <Text style={styles.detailsTitle}>Camera Information</Text>
          
          {/* Camera Settings */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Settings</Text>
            <View style={styles.detailsGrid}>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Mode</Text>
                <Text style={styles.detailValue}>{cameraMode}</Text>
              </View>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Flash</Text>
                <Text style={styles.detailValue}>{flashMode}</Text>
              </View>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Zoom</Text>
                <Text style={styles.detailValue}>{zoomLevel.toFixed(1)}x</Text>
              </View>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Recording</Text>
                <Text style={styles.detailValue}>{isRecording ? 'Yes' : 'No'}</Text>
              </View>
            </View>
          </View>

          {/* Statistics */}
          {statistics && (
            <View style={styles.detailsSection}>
              <Text style={styles.sectionTitle}>Session Statistics</Text>
              <View style={styles.detailsGrid}>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Total Captures</Text>
                  <Text style={styles.detailValue}>
                    {(statistics.totalPhotos || 0) + (statistics.totalVideos || 0)}
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Success Rate</Text>
                  <Text style={styles.detailValue}>{formatPercentage(statistics.successRate)}</Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Avg Capture Time</Text>
                  <Text style={styles.detailValue}>
                    {(statistics.averageCaptureTime || 0).toFixed(0)}ms
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Session Duration</Text>
                  <Text style={styles.detailValue}>
                    {CameraUtils.formatDuration(statistics.sessionDuration || 0)}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Performance Metrics */}
          {performanceData && (
            <View style={styles.detailsSection}>
              <Text style={styles.sectionTitle}>Performance</Text>
              <View style={styles.detailsGrid}>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Frame Rate</Text>
                  <Text style={styles.detailValue}>
                    {(performanceData.fps || 0).toFixed(1)} FPS
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Memory Usage</Text>
                  <Text style={styles.detailValue}>
                    {formatFileSize(performanceData.memoryUsage)}
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>CPU Usage</Text>
                  <Text style={styles.detailValue}>
                    {formatPercentage(performanceData.cpuUsage)}
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Temperature</Text>
                  <Text style={styles.detailValue}>
                    {(performanceData.temperature || 0).toFixed(1)}°C
                  </Text>
                </View>
              </View>
            </View>
          )}
        </LinearGradient>
      </Animated.View>
    );
  };

  return (
    <Animated.View style={[styles.container, style, { opacity: overlayOpacity }]} {...props}>
      {renderTopStatusBar()}
      {renderCameraStats()}
      {renderPerformanceMetrics()}
      {renderDetailsPanel()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'box-none',
  },
  topStatusBar: {
    height: 60,
    paddingTop: 10,
  },
  statusBarGradient: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  statusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 15,
    fontFamily: 'monospace',
  },
  modeText: {
    color: '#FFD700',
    fontSize: 12,
    fontWeight: '600',
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  infoButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoIcon: {
    fontSize: 16,
  },
  statsContainer: {
    position: 'absolute',
    top: 70,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 10,
    padding: 10,
    minWidth: 120,
  },
  statItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  statLabel: {
    color: '#ccc',
    fontSize: 10,
    fontWeight: '500',
  },
  statValue: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },
  performanceContainer: {
    position: 'absolute',
    bottom: 140,
    left: 10,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 10,
    padding: 10,
    minWidth: 150,
  },
  performanceTitle: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  performanceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  performanceItem: {
    width: '50%',
    alignItems: 'center',
    marginBottom: 4,
  },
  performanceLabel: {
    color: '#ccc',
    fontSize: 9,
    fontWeight: '500',
  },
  performanceValue: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },
  detailsPanel: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  detailsGradient: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
  },
  detailsTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
    textAlign: 'center',
  },
  detailsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    color: '#FFD700',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 10,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailItem: {
    width: '50%',
    marginBottom: 8,
  },
  detailLabel: {
    color: '#ccc',
    fontSize: 12,
    fontWeight: '500',
  },
  detailValue: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default CameraOverlay;
