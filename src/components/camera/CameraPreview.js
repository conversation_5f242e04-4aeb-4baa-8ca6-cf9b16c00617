/**
 * CameraPreview - Camera preview component with touch handling
 * Simulates camera preview with touch-to-focus functionality
 */

import React, { forwardRef, useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableWithoutFeedback,
  Animated,
  Dimensions,
  Text,
  Image
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import LoggerService from '../../services/LoggerService';
import { LogLevel, LogCategory } from '../../models/LogEntry';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const CameraPreview = forwardRef(({
  style,
  device,
  settings,
  onTouch,
  isActive = true,
  showGrid = false,
  ...props
}, ref) => {
  const [focusPoint, setFocusPoint] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const focusAnimation = useRef(new Animated.Value(0)).current;
  const loadingAnimation = useRef(new Animated.Value(0)).current;
  const gridOpacity = useRef(new Animated.Value(0)).current;

  // Simulate camera loading
  useEffect(() => {
    const loadTimer = setTimeout(() => {
      setIsLoading(false);
      Animated.timing(loadingAnimation, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }, 1000 + Math.random() * 1000);

    return () => clearTimeout(loadTimer);
  }, [device]);

  // Grid animation
  useEffect(() => {
    Animated.timing(gridOpacity, {
      toValue: showGrid ? 0.3 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [showGrid]);

  // Handle touch events
  const handleTouch = (event) => {
    if (!isActive || isLoading) return;

    const { locationX, locationY } = event.nativeEvent;
    
    // Set focus point
    setFocusPoint({ x: locationX, y: locationY });
    
    // Animate focus indicator
    focusAnimation.setValue(0);
    Animated.sequence([
      Animated.timing(focusAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.delay(1000),
      Animated.timing(focusAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      })
    ]).start(() => {
      setFocusPoint(null);
    });

    // Call parent handler
    if (onTouch) {
      onTouch(event);
    }

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.CAMERA_OPERATIONS,
      'CameraPreview',
      'Touch focus triggered',
      { x: locationX, y: locationY, device: device?.type }
    );
  };

  // Render grid lines
  const renderGrid = () => {
    if (!showGrid) return null;

    const lines = [];
    const width = screenWidth;
    const height = screenHeight * 0.5; // Camera section height

    // Vertical lines (rule of thirds)
    for (let i = 1; i < 3; i++) {
      const x = (width / 3) * i;
      lines.push(
        <View
          key={`v-${i}`}
          style={[
            styles.gridLine,
            {
              left: x,
              top: 0,
              width: 1,
              height: height,
            }
          ]}
        />
      );
    }

    // Horizontal lines (rule of thirds)
    for (let i = 1; i < 3; i++) {
      const y = (height / 3) * i;
      lines.push(
        <View
          key={`h-${i}`}
          style={[
            styles.gridLine,
            {
              left: 0,
              top: y,
              width: width,
              height: 1,
            }
          ]}
        />
      );
    }

    return (
      <Animated.View style={[styles.gridContainer, { opacity: gridOpacity }]}>
        {lines}
      </Animated.View>
    );
  };

  // Render focus indicator
  const renderFocusIndicator = () => {
    if (!focusPoint) return null;

    const focusSize = 80;
    const focusStyle = {
      position: 'absolute',
      left: focusPoint.x - focusSize / 2,
      top: focusPoint.y - focusSize / 2,
      width: focusSize,
      height: focusSize,
      borderWidth: 2,
      borderColor: '#FFD700',
      borderRadius: focusSize / 2,
      opacity: focusAnimation,
      transform: [
        {
          scale: focusAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [1.5, 1],
          }),
        },
      ],
    };

    return (
      <Animated.View style={focusStyle}>
        <View style={styles.focusInner} />
      </Animated.View>
    );
  };

  // Render loading overlay
  const renderLoadingOverlay = () => {
    if (!isLoading) return null;

    return (
      <View style={styles.loadingOverlay}>
        <View style={styles.loadingIndicator}>
          <Text style={styles.loadingText}>Loading Camera...</Text>
          <Text style={styles.loadingSubtext}>
            {device?.name || 'Camera Device'}
          </Text>
        </View>
      </View>
    );
  };

  // Render camera simulation
  const renderCameraSimulation = () => {
    if (isLoading) return null;

    // Simulate different camera views based on device type
    const gradientColors = device?.type === 'front' 
      ? ['#4a90e2', '#7b68ee', '#9370db']
      : ['#2c3e50', '#34495e', '#5d6d7e'];

    return (
      <Animated.View style={[styles.cameraSimulation, { opacity: loadingAnimation }]}>
        <LinearGradient
          colors={gradientColors}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Simulated camera content */}
          <View style={styles.cameraContent}>
            <Text style={styles.deviceLabel}>
              {device?.name || 'Camera Device'}
            </Text>
            <Text style={styles.settingsLabel}>
              {settings?.mode || 'Photo'} Mode
            </Text>
            <Text style={styles.zoomLabel}>
              {settings?.zoom ? `${settings.zoom.toFixed(1)}x` : '1.0x'}
            </Text>
          </View>

          {/* Simulated viewfinder elements */}
          <View style={styles.viewfinderElements}>
            {/* Corner brackets */}
            <View style={[styles.bracket, styles.topLeft]} />
            <View style={[styles.bracket, styles.topRight]} />
            <View style={[styles.bracket, styles.bottomLeft]} />
            <View style={[styles.bracket, styles.bottomRight]} />
            
            {/* Center crosshair */}
            <View style={styles.crosshair}>
              <View style={styles.crosshairHorizontal} />
              <View style={styles.crosshairVertical} />
            </View>
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  return (
    <TouchableWithoutFeedback onPress={handleTouch}>
      <View ref={ref} style={[styles.container, style]} {...props}>
        {renderCameraSimulation()}
        {renderGrid()}
        {renderFocusIndicator()}
        {renderLoadingOverlay()}
      </View>
    </TouchableWithoutFeedback>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    position: 'relative',
  },
  cameraSimulation: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraContent: {
    alignItems: 'center',
    marginBottom: 50,
  },
  deviceLabel: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  settingsLabel: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 5,
  },
  zoomLabel: {
    color: '#FFD700',
    fontSize: 16,
    fontWeight: '500',
  },
  viewfinderElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bracket: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: 'rgba(255, 255, 255, 0.6)',
  },
  topLeft: {
    top: 20,
    left: 20,
    borderTopWidth: 2,
    borderLeftWidth: 2,
  },
  topRight: {
    top: 20,
    right: 20,
    borderTopWidth: 2,
    borderRightWidth: 2,
  },
  bottomLeft: {
    bottom: 20,
    left: 20,
    borderBottomWidth: 2,
    borderLeftWidth: 2,
  },
  bottomRight: {
    bottom: 20,
    right: 20,
    borderBottomWidth: 2,
    borderRightWidth: 2,
  },
  crosshair: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 20,
    height: 20,
    marginTop: -10,
    marginLeft: -10,
  },
  crosshairHorizontal: {
    position: 'absolute',
    top: 9,
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  crosshairVertical: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 9,
    width: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  gridContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  focusInner: {
    flex: 1,
    borderRadius: 40,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.5)',
    margin: 10,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIndicator: {
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  loadingSubtext: {
    color: '#ccc',
    fontSize: 14,
  },
});

CameraPreview.displayName = 'CameraPreview';

export default CameraPreview;
