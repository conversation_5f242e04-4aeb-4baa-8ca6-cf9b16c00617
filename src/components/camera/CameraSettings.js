/**
 * CameraSettings - Camera settings modal with advanced controls
 * Provides comprehensive camera configuration options
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  Switch,
  Slider,
  Alert
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import LoggerService from '../../services/LoggerService';
import { LogLevel, LogCategory } from '../../models/LogEntry';
import {
  CameraMode,
  FlashMode,
  FocusMode,
  VideoQuality,
  PhotoQuality
} from '../../types/CameraTypes';
import CameraUtils from '../../utils/CameraUtils';

const CameraSettings = ({
  visible,
  currentSettings,
  deviceCapabilities,
  onSettingsChange,
  onClose,
  ...props
}) => {
  const [localSettings, setLocalSettings] = useState(currentSettings || {});
  const [hasChanges, setHasChanges] = useState(false);

  // Update local settings when props change
  useEffect(() => {
    if (currentSettings) {
      setLocalSettings(currentSettings);
      setHasChanges(false);
    }
  }, [currentSettings, visible]);

  // Handle setting change
  const handleSettingChange = (key, value) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    setHasChanges(true);

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.CAMERA_OPERATIONS,
      'CameraSettings',
      'Setting changed',
      { key, value, previousValue: localSettings[key] }
    );
  };

  // Apply settings
  const applySettings = () => {
    try {
      // Validate settings
      const validation = CameraUtils.validateSettings(localSettings, deviceCapabilities);
      
      if (!validation.isValid) {
        Alert.alert('Invalid Settings', validation.errors.join('\n'));
        return;
      }

      onSettingsChange(localSettings);
      setHasChanges(false);
      
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraSettings',
        'Settings applied',
        { settings: localSettings }
      );

      onClose();
    } catch (error) {
      LoggerService.error('CameraSettings', 'Failed to apply settings', error);
      Alert.alert('Error', 'Failed to apply settings');
    }
  };

  // Reset to defaults
  const resetToDefaults = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to defaults?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            const defaultSettings = CameraUtils.getOptimalSettings(
              deviceCapabilities,
              localSettings.mode
            );
            setLocalSettings(defaultSettings);
            setHasChanges(true);
            
            LoggerService.log(
              LogLevel.INFO,
              LogCategory.CAMERA_OPERATIONS,
              'CameraSettings',
              'Settings reset to defaults',
              { defaultSettings }
            );
          }
        }
      ]
    );
  };

  // Render setting section
  const renderSection = (title, children) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  // Render switch setting
  const renderSwitch = (label, key, description) => (
    <View style={styles.settingItem}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingLabel}>{label}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      <Switch
        value={localSettings[key] || false}
        onValueChange={(value) => handleSettingChange(key, value)}
        trackColor={{ false: '#767577', true: '#007AFF' }}
        thumbColor={localSettings[key] ? '#fff' : '#f4f3f4'}
      />
    </View>
  );

  // Render slider setting
  const renderSlider = (label, key, min, max, step, unit = '') => (
    <View style={styles.settingItem}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingLabel}>{label}</Text>
        <Text style={styles.settingValue}>
          {(localSettings[key] || min).toFixed(step < 1 ? 1 : 0)}{unit}
        </Text>
      </View>
      <Slider
        style={styles.slider}
        minimumValue={min}
        maximumValue={max}
        step={step}
        value={localSettings[key] || min}
        onValueChange={(value) => handleSettingChange(key, value)}
        minimumTrackTintColor="#007AFF"
        maximumTrackTintColor="#767577"
        thumbTintColor="#007AFF"
      />
    </View>
  );

  // Render picker setting
  const renderPicker = (label, key, options) => (
    <View style={styles.settingItem}>
      <Text style={styles.settingLabel}>{label}</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.pickerContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.pickerOption,
              localSettings[key] === option.value && styles.pickerOptionSelected
            ]}
            onPress={() => handleSettingChange(key, option.value)}
          >
            <Text style={[
              styles.pickerOptionText,
              localSettings[key] === option.value && styles.pickerOptionTextSelected
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
      {...props}
    >
      <View style={styles.container}>
        <LinearGradient
          colors={['#1a1a1a', '#2d2d2d']}
          style={styles.gradient}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.headerButton} onPress={onClose}>
              <Text style={styles.headerButtonText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Camera Settings</Text>
            <TouchableOpacity
              style={[styles.headerButton, !hasChanges && styles.headerButtonDisabled]}
              onPress={applySettings}
              disabled={!hasChanges}
            >
              <Text style={[
                styles.headerButtonText,
                styles.headerButtonPrimary,
                !hasChanges && styles.headerButtonTextDisabled
              ]}>
                Apply
              </Text>
            </TouchableOpacity>
          </View>

          {/* Settings Content */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Camera Mode */}
            {renderSection('Camera Mode', 
              renderPicker('Mode', 'mode', [
                { value: CameraMode.PHOTO, label: 'Photo' },
                { value: CameraMode.VIDEO, label: 'Video' },
                { value: CameraMode.PORTRAIT, label: 'Portrait' },
                { value: CameraMode.NIGHT, label: 'Night' },
              ])
            )}

            {/* Quality Settings */}
            {renderSection('Quality Settings', (
              <>
                {renderPicker('Photo Quality', 'photoQuality', [
                  { value: PhotoQuality.LOW, label: 'Low' },
                  { value: PhotoQuality.MEDIUM, label: 'Medium' },
                  { value: PhotoQuality.HIGH, label: 'High' },
                  { value: PhotoQuality.ULTRA_HIGH, label: 'Ultra High' },
                ])}
                {renderPicker('Video Quality', 'videoQuality', [
                  { value: VideoQuality.LOW, label: 'Low' },
                  { value: VideoQuality.MEDIUM, label: 'Medium' },
                  { value: VideoQuality.HIGH, label: 'High' },
                  { value: VideoQuality.ULTRA_HIGH, label: 'Ultra High' },
                ])}
              </>
            ))}

            {/* Flash Settings */}
            {deviceCapabilities?.hasFlash && renderSection('Flash Settings',
              renderPicker('Flash Mode', 'flashMode', [
                { value: FlashMode.OFF, label: 'Off' },
                { value: FlashMode.AUTO, label: 'Auto' },
                { value: FlashMode.ON, label: 'On' },
                { value: FlashMode.TORCH, label: 'Torch' },
              ])
            )}

            {/* Focus Settings */}
            {deviceCapabilities?.supportsFocus && renderSection('Focus Settings',
              renderPicker('Focus Mode', 'focusMode', [
                { value: FocusMode.AUTO, label: 'Auto' },
                { value: FocusMode.MANUAL, label: 'Manual' },
                { value: FocusMode.CONTINUOUS, label: 'Continuous' },
              ])
            )}

            {/* Advanced Settings */}
            {renderSection('Advanced Settings', (
              <>
                {deviceCapabilities?.supportsHDR && renderSwitch(
                  'HDR',
                  'enableHDR',
                  'High Dynamic Range for better exposure'
                )}
                {deviceCapabilities?.supportsStabilization && renderSwitch(
                  'Stabilization',
                  'enableStabilization',
                  'Reduce camera shake and blur'
                )}
                {renderSwitch(
                  'Audio Recording',
                  'enableAudio',
                  'Record audio with video'
                )}
                {renderSlider('Frame Rate', 'frameRate', 24, 60, 1, ' fps')}
              </>
            ))}

            {/* Manual Controls */}
            {renderSection('Manual Controls', (
              <>
                {renderSlider(
                  'Zoom',
                  'zoom',
                  deviceCapabilities?.minZoom || 1.0,
                  deviceCapabilities?.maxZoom || 10.0,
                  0.1,
                  'x'
                )}
                {deviceCapabilities?.supportsExposure && renderSlider(
                  'Exposure',
                  'exposure',
                  -2.0,
                  2.0,
                  0.1
                )}
              </>
            ))}

            {/* Reset Button */}
            <View style={styles.resetSection}>
              <TouchableOpacity style={styles.resetButton} onPress={resetToDefaults}>
                <Text style={styles.resetButtonText}>Reset to Defaults</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </LinearGradient>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  headerButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  headerButtonDisabled: {
    opacity: 0.5,
  },
  headerButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
  },
  headerButtonPrimary: {
    fontWeight: '600',
  },
  headerButtonTextDisabled: {
    color: '#666',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 20,
  },
  sectionTitle: {
    color: '#FFD700',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 15,
  },
  settingItem: {
    marginBottom: 20,
  },
  settingInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  settingLabel: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  settingDescription: {
    color: '#ccc',
    fontSize: 12,
    marginTop: 4,
  },
  settingValue: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
  },
  slider: {
    width: '100%',
    height: 40,
  },
  pickerContainer: {
    marginTop: 10,
  },
  pickerOption: {
    backgroundColor: '#333',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  pickerOptionSelected: {
    backgroundColor: '#007AFF',
  },
  pickerOptionText: {
    color: '#ccc',
    fontSize: 14,
    fontWeight: '500',
  },
  pickerOptionTextSelected: {
    color: '#fff',
    fontWeight: '600',
  },
  resetSection: {
    marginVertical: 30,
    alignItems: 'center',
  },
  resetButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 8,
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CameraSettings;
