/**
 * LogDisplay - Real-time logging display component
 * Shows filtered logs with search, animations, and real-time updates
 */

import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  StyleSheet,
  Text,
  FlatList,
  TouchableOpacity,
  TextInput,
  Animated,
  Dimensions
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import LoggerService from '../../services/LoggerService';
import { LogLevel, LogCategory } from '../../models/LogEntry';
import {
  selectFilteredLogs,
  selectLogStatistics,
  updateDisplaySettings,
  clearLogs
} from '../../store/slices/logSlice';

const { width: screenWidth } = Dimensions.get('window');

const LogDisplay = ({ style, ...props }) => {
  const dispatch = useDispatch();
  const logs = useSelector(selectFilteredLogs);
  const statistics = useSelector(selectLogStatistics);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('ALL');
  const [selectedCategory, setSelectedCategory] = useState('ALL');
  const [autoScroll, setAutoScroll] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  
  const flatListRef = useRef(null);
  const searchAnimation = useRef(new Animated.Value(0)).current;
  const filterAnimation = useRef(new Animated.Value(0)).current;
  const newLogAnimation = useRef(new Animated.Value(0)).current;

  // Filter logs based on search and filters
  const filteredLogs = useMemo(() => {
    let filtered = logs;

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(query) ||
        log.component.toLowerCase().includes(query) ||
        (log.data && JSON.stringify(log.data).toLowerCase().includes(query))
      );
    }

    // Level filter
    if (selectedLevel !== 'ALL') {
      filtered = filtered.filter(log => log.level === selectedLevel);
    }

    // Category filter
    if (selectedCategory !== 'ALL') {
      filtered = filtered.filter(log => log.category === selectedCategory);
    }

    return filtered.slice(-1000); // Keep last 1000 logs for performance
  }, [logs, searchQuery, selectedLevel, selectedCategory]);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && filteredLogs.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [filteredLogs.length, autoScroll]);

  // Animate new log arrival
  useEffect(() => {
    if (filteredLogs.length > 0) {
      Animated.sequence([
        Animated.timing(newLogAnimation, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(newLogAnimation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [filteredLogs.length]);

  // Search animation
  useEffect(() => {
    Animated.timing(searchAnimation, {
      toValue: searchQuery ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [searchQuery]);

  // Filter animation
  useEffect(() => {
    Animated.timing(filterAnimation, {
      toValue: showFilters ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [showFilters]);

  // Get log level color
  const getLogLevelColor = (level) => {
    const colors = {
      [LogLevel.DEBUG]: '#6c757d',
      [LogLevel.INFO]: '#17a2b8',
      [LogLevel.WARNING]: '#ffc107',
      [LogLevel.ERROR]: '#dc3545',
      [LogLevel.PERFORMANCE]: '#28a745',
      [LogLevel.CAMERA_OPERATIONS]: '#007bff',
      [LogLevel.LIFECYCLE]: '#6f42c1',
      [LogLevel.NETWORK]: '#fd7e14',
      [LogLevel.STORAGE]: '#20c997'
    };
    return colors[level] || '#6c757d';
  };

  // Get log category icon
  const getCategoryIcon = (category) => {
    const icons = {
      [LogCategory.CAMERA_OPERATIONS]: '📷',
      [LogCategory.PERFORMANCE_METRICS]: '⚡',
      [LogCategory.LIFECYCLE_EVENTS]: '🔄',
      [LogCategory.NETWORK_OPERATIONS]: '🌐',
      [LogCategory.STORAGE_OPERATIONS]: '💾',
      [LogCategory.USER_INTERACTIONS]: '👆',
      [LogCategory.ERROR_HANDLING]: '❌',
      [LogCategory.SYSTEM_EVENTS]: '⚙️'
    };
    return icons[category] || '📝';
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.USER_INTERACTIONS,
      'LogDisplay',
      'Log search performed',
      { query, resultCount: filteredLogs.length }
    );
  };

  // Handle filter change
  const handleFilterChange = (type, value) => {
    if (type === 'level') {
      setSelectedLevel(value);
    } else if (type === 'category') {
      setSelectedCategory(value);
    }

    dispatch(updateDisplaySettings({
      selectedLevel: type === 'level' ? value : selectedLevel,
      selectedCategory: type === 'category' ? value : selectedCategory
    }));

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.USER_INTERACTIONS,
      'LogDisplay',
      'Log filter changed',
      { type, value, resultCount: filteredLogs.length }
    );
  };

  // Clear logs
  const handleClearLogs = () => {
    dispatch(clearLogs());
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.USER_INTERACTIONS,
      'LogDisplay',
      'Logs cleared by user'
    );
  };

  // Render log item
  const renderLogItem = ({ item, index }) => {
    const isLastItem = index === filteredLogs.length - 1;
    
    return (
      <Animated.View style={[
        styles.logItem,
        isLastItem && { opacity: newLogAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: [1, 0.7]
        })}
      ]}>
        <View style={styles.logHeader}>
          <View style={styles.logMeta}>
            <Text style={styles.logIcon}>{getCategoryIcon(item.category)}</Text>
            <Text style={styles.logTimestamp}>{formatTimestamp(item.timestamp)}</Text>
            <View style={[styles.logLevel, { backgroundColor: getLogLevelColor(item.level) }]}>
              <Text style={styles.logLevelText}>{item.level}</Text>
            </View>
          </View>
          <Text style={styles.logComponent}>{item.component}</Text>
        </View>
        
        <Text style={styles.logMessage}>{item.message}</Text>
        
        {item.data && (
          <View style={styles.logData}>
            <Text style={styles.logDataText}>
              {typeof item.data === 'string' ? item.data : JSON.stringify(item.data, null, 2)}
            </Text>
          </View>
        )}
      </Animated.View>
    );
  };

  // Render header
  const renderHeader = () => (
    <View style={styles.header}>
      <LinearGradient
        colors={['#2d2d2d', '#1a1a1a']}
        style={styles.headerGradient}
      >
        <View style={styles.headerTop}>
          <Text style={styles.headerTitle}>System Logs</Text>
          <View style={styles.headerStats}>
            <Text style={styles.statText}>{filteredLogs.length} logs</Text>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => setShowFilters(!showFilters)}
            >
              <Text style={styles.headerButtonText}>🔍</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleClearLogs}
            >
              <Text style={styles.headerButtonText}>🗑️</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search logs..."
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={handleSearch}
          />
        </View>

        {/* Filters */}
        <Animated.View style={[
          styles.filtersContainer,
          {
            opacity: filterAnimation,
            transform: [{
              translateY: filterAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [-20, 0]
              })
            }]
          }
        ]}>
          {/* Level Filter */}
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Level:</Text>
            <View style={styles.filterButtons}>
              {['ALL', LogLevel.ERROR, LogLevel.WARNING, LogLevel.INFO, LogLevel.DEBUG].map(level => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.filterButton,
                    selectedLevel === level && styles.filterButtonActive
                  ]}
                  onPress={() => handleFilterChange('level', level)}
                >
                  <Text style={[
                    styles.filterButtonText,
                    selectedLevel === level && styles.filterButtonTextActive
                  ]}>
                    {level}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Category Filter */}
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Category:</Text>
            <View style={styles.filterButtons}>
              {['ALL', LogCategory.CAMERA_OPERATIONS, LogCategory.PERFORMANCE_METRICS, LogCategory.ERROR_HANDLING].map(category => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.filterButton,
                    selectedCategory === category && styles.filterButtonActive
                  ]}
                  onPress={() => handleFilterChange('category', category)}
                >
                  <Text style={[
                    styles.filterButtonText,
                    selectedCategory === category && styles.filterButtonTextActive
                  ]}>
                    {category === 'ALL' ? 'ALL' : category.split('_')[0]}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </Animated.View>
      </LinearGradient>
    </View>
  );

  return (
    <View style={[styles.container, style]} {...props}>
      {renderHeader()}
      
      <FlatList
        ref={flatListRef}
        data={filteredLogs}
        renderItem={renderLogItem}
        keyExtractor={(item) => item.id}
        style={styles.logList}
        showsVerticalScrollIndicator={false}
        onScrollBeginDrag={() => setAutoScroll(false)}
        onScrollEndDrag={(event) => {
          const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
          const isAtBottom = contentOffset.y + layoutMeasurement.height >= contentSize.height - 50;
          setAutoScroll(isAtBottom);
        }}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No logs to display</Text>
            <Text style={styles.emptySubtext}>Logs will appear here as the app runs</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  headerGradient: {
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  headerStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    color: '#ccc',
    fontSize: 12,
    marginRight: 10,
  },
  headerButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 5,
  },
  headerButtonText: {
    fontSize: 14,
  },
  searchContainer: {
    marginBottom: 10,
  },
  searchInput: {
    backgroundColor: '#333',
    color: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    fontSize: 14,
  },
  filtersContainer: {
    marginTop: 10,
  },
  filterGroup: {
    marginBottom: 10,
  },
  filterLabel: {
    color: '#ccc',
    fontSize: 12,
    marginBottom: 5,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterButton: {
    backgroundColor: '#333',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 5,
    marginBottom: 5,
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterButtonText: {
    color: '#ccc',
    fontSize: 10,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#fff',
    fontWeight: '600',
  },
  logList: {
    flex: 1,
  },
  logItem: {
    backgroundColor: '#2d2d2d',
    marginHorizontal: 10,
    marginVertical: 2,
    padding: 10,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#007AFF',
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  logMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logIcon: {
    fontSize: 12,
    marginRight: 5,
  },
  logTimestamp: {
    color: '#666',
    fontSize: 10,
    fontFamily: 'monospace',
    marginRight: 8,
  },
  logLevel: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  logLevelText: {
    color: '#fff',
    fontSize: 8,
    fontWeight: '600',
  },
  logComponent: {
    color: '#FFD700',
    fontSize: 10,
    fontWeight: '500',
  },
  logMessage: {
    color: '#fff',
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 5,
  },
  logData: {
    backgroundColor: '#1a1a1a',
    padding: 8,
    borderRadius: 4,
    marginTop: 5,
  },
  logDataText: {
    color: '#ccc',
    fontSize: 10,
    fontFamily: 'monospace',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 5,
  },
  emptySubtext: {
    color: '#555',
    fontSize: 12,
  },
});

export default LogDisplay;
