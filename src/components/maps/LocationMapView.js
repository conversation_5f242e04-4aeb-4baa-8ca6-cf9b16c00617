import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Circle } from 'react-native-maps';
import LinearGradient from 'react-native-linear-gradient';
import { LocationService } from '../../services/LocationService';

const { width, height } = Dimensions.get('window');

/**
 * LocationMapView - Interactive map with location tracking and visualization
 * Shows current location, tracking path, and location statistics
 */
const LocationMapView = ({ style }) => {
  const [location, setLocation] = useState(null);
  const [locationHistory, setLocationHistory] = useState([]);
  const [isTracking, setIsTracking] = useState(false);
  const [locationStats, setLocationStats] = useState({});
  const [mapRegion, setMapRegion] = useState({
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [animatedValue] = useState(new Animated.Value(0));
  const [isExpanded, setIsExpanded] = useState(false);
  const mapRef = useRef(null);

  useEffect(() => {
    initializeLocationService();
    
    return () => {
      const locationService = LocationService.getInstance();
      locationService.stopTracking();
    };
  }, []);

  const initializeLocationService = async () => {
    const locationService = LocationService.getInstance();
    
    if (!locationService.isInitialized) {
      const initialized = await locationService.initialize();
      if (!initialized) {
        Alert.alert('Location Error', 'Failed to initialize location services');
        return;
      }
    }
    
    // Get current location
    try {
      const currentLocation = await locationService.getCurrentLocation();
      updateLocationDisplay(currentLocation);
      
      // Center map on current location
      if (currentLocation) {
        const newRegion = {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setMapRegion(newRegion);
      }
    } catch (error) {
      console.log('Error getting current location:', error);
    }
    
    // Add location listener
    locationService.addListener((newLocation) => {
      updateLocationDisplay(newLocation);
    });
    
    // Update stats periodically
    const interval = setInterval(updateLocationStats, 5000);
    
    return () => clearInterval(interval);
  };

  const updateLocationDisplay = (newLocation) => {
    setLocation(newLocation);
    
    // Update location history
    const locationService = LocationService.getInstance();
    const history = locationService.getRecentHistory(50);
    setLocationHistory(history);
    
    // Update map region if tracking
    if (isTracking && newLocation) {
      const newRegion = {
        latitude: newLocation.latitude,
        longitude: newLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
      setMapRegion(newRegion);
      
      // Animate to new location
      if (mapRef.current) {
        mapRef.current.animateToRegion(newRegion, 1000);
      }
    }
  };

  const updateLocationStats = () => {
    const locationService = LocationService.getInstance();
    const stats = locationService.getLocationStatistics();
    setLocationStats(stats);
  };

  const toggleTracking = () => {
    const locationService = LocationService.getInstance();
    
    if (isTracking) {
      locationService.stopTracking();
      setIsTracking(false);
    } else {
      locationService.startTracking();
      setIsTracking(true);
    }
  };

  const centerOnCurrentLocation = async () => {
    const locationService = LocationService.getInstance();
    
    try {
      const currentLocation = await locationService.getCurrentLocation();
      if (currentLocation) {
        const newRegion = {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        
        setMapRegion(newRegion);
        if (mapRef.current) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      }
    } catch (error) {
      Alert.alert('Location Error', 'Unable to get current location');
    }
  };

  const clearLocationHistory = () => {
    Alert.alert(
      'Clear History',
      'Are you sure you want to clear location history?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            const locationService = LocationService.getInstance();
            locationService.clearHistory();
            setLocationHistory([]);
            updateLocationStats();
          },
        },
      ]
    );
  };

  const toggleExpanded = () => {
    const toValue = isExpanded ? 0 : 1;
    
    Animated.spring(animatedValue, {
      toValue,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
    
    setIsExpanded(!isExpanded);
  };

  const formatDistance = (meters) => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    }
    return `${(meters / 1000).toFixed(2)}km`;
  };

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const renderLocationPath = () => {
    if (locationHistory.length < 2) return null;
    
    const coordinates = locationHistory.map(loc => ({
      latitude: loc.latitude,
      longitude: loc.longitude,
    }));
    
    return (
      <Polyline
        coordinates={coordinates}
        strokeColor="#4facfe"
        strokeWidth={3}
        strokePattern={[1]}
      />
    );
  };

  const renderCurrentLocationMarker = () => {
    if (!location) return null;
    
    return (
      <Marker
        coordinate={{
          latitude: location.latitude,
          longitude: location.longitude,
        }}
        title="Current Location"
        description={`Accuracy: ${location.accuracy?.toFixed(1)}m`}
      >
        <View style={styles.currentLocationMarker}>
          <View style={styles.currentLocationDot} />
        </View>
      </Marker>
    );
  };

  const renderAccuracyCircle = () => {
    if (!location || !location.accuracy) return null;
    
    return (
      <Circle
        center={{
          latitude: location.latitude,
          longitude: location.longitude,
        }}
        radius={location.accuracy}
        strokeColor="rgba(79, 172, 254, 0.5)"
        fillColor="rgba(79, 172, 254, 0.1)"
        strokeWidth={1}
      />
    );
  };

  const animatedHeight = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [300, height * 0.7],
  });

  return (
    <Animated.View style={[styles.container, { height: animatedHeight }, style]}>
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          region={mapRegion}
          onRegionChangeComplete={setMapRegion}
          showsUserLocation={false}
          showsMyLocationButton={false}
          showsCompass={true}
          showsScale={true}
        >
          {renderLocationPath()}
          {renderCurrentLocationMarker()}
          {renderAccuracyCircle()}
        </MapView>
        
        <View style={styles.mapControls}>
          <TouchableOpacity style={styles.controlButton} onPress={centerOnCurrentLocation}>
            <Text style={styles.controlButtonText}>📍</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.controlButton, isTracking && styles.controlButtonActive]} 
            onPress={toggleTracking}
          >
            <Text style={styles.controlButtonText}>{isTracking ? '⏸️' : '▶️'}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.controlButton} onPress={clearLocationHistory}>
            <Text style={styles.controlButtonText}>🗑️</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.infoPanel}
      >
        <TouchableOpacity style={styles.infoPanelHeader} onPress={toggleExpanded}>
          <Text style={styles.infoPanelTitle}>📍 Location Tracking</Text>
          <Text style={[styles.expandIcon, { transform: [{ rotate: isExpanded ? '180deg' : '0deg' }] }]}>
            ▼
          </Text>
        </TouchableOpacity>
        
        <View style={styles.quickStats}>
          <View style={styles.quickStat}>
            <Text style={styles.quickStatValue}>
              {location ? `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}` : 'No location'}
            </Text>
            <Text style={styles.quickStatLabel}>Current Position</Text>
          </View>
        </View>
        
        {isExpanded && (
          <View style={styles.expandedStats}>
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Tracking Status</Text>
              <Text style={[styles.statValue, { color: isTracking ? '#51cf66' : '#ff6b6b' }]}>
                {isTracking ? 'Active' : 'Inactive'}
              </Text>
            </View>
            
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Total Locations</Text>
              <Text style={styles.statValue}>{locationStats.totalLocations || 0}</Text>
            </View>
            
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Distance Traveled</Text>
              <Text style={styles.statValue}>
                {formatDistance(locationStats.distanceTraveled || 0)}
              </Text>
            </View>
            
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Tracking Duration</Text>
              <Text style={styles.statValue}>
                {formatDuration(locationStats.trackingDuration || 0)}
              </Text>
            </View>
            
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Average Accuracy</Text>
              <Text style={styles.statValue}>
                {locationStats.averageAccuracy ? `${locationStats.averageAccuracy.toFixed(1)}m` : 'N/A'}
              </Text>
            </View>
            
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Current Accuracy</Text>
              <Text style={styles.statValue}>
                {location?.accuracy ? `${location.accuracy.toFixed(1)}m` : 'N/A'}
              </Text>
            </View>
          </View>
        )}
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 10,
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    top: 10,
    right: 10,
    flexDirection: 'column',
    gap: 8,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  controlButtonActive: {
    backgroundColor: '#4facfe',
  },
  controlButtonText: {
    fontSize: 16,
  },
  currentLocationMarker: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#4facfe',
    borderWidth: 3,
    borderColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  currentLocationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ffffff',
  },
  infoPanel: {
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  infoPanelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  infoPanelTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  expandIcon: {
    fontSize: 14,
    color: '#ffffff',
  },
  quickStats: {
    marginBottom: 10,
  },
  quickStat: {
    alignItems: 'center',
  },
  quickStatValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
  },
  quickStatLabel: {
    fontSize: 10,
    color: '#e9ecef',
    marginTop: 2,
  },
  expandedStats: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 10,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#e9ecef',
  },
  statValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default LocationMapView;
