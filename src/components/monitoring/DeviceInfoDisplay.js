import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { DeviceInfoService } from '../../services/DeviceInfoService';

const { width } = Dimensions.get('window');

/**
 * DeviceInfoDisplay - Comprehensive device information display
 * Shows device specs, performance metrics, and system status
 */
const DeviceInfoDisplay = ({ style }) => {
  const [deviceInfo, setDeviceInfo] = useState({});
  const [selectedCategory, setSelectedCategory] = useState('basic');
  const [animatedValue] = useState(new Animated.Value(0));
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    initializeDeviceInfo();
    
    // Update device info every 30 seconds
    const interval = setInterval(updateDeviceInfo, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const initializeDeviceInfo = async () => {
    const deviceService = DeviceInfoService.getInstance();
    
    if (!deviceService.isInitialized) {
      await deviceService.initialize();
    }
    
    const info = deviceService.getAllDeviceInfo();
    setDeviceInfo(info);
  };

  const updateDeviceInfo = () => {
    const deviceService = DeviceInfoService.getInstance();
    const info = deviceService.getAllDeviceInfo();
    setDeviceInfo(info);
  };

  const toggleExpanded = () => {
    const toValue = isExpanded ? 0 : 1;
    
    Animated.spring(animatedValue, {
      toValue,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
    
    setIsExpanded(!isExpanded);
  };

  const categories = [
    { key: 'basic', label: 'Basic', icon: '📱' },
    { key: 'performance', label: 'Performance', icon: '⚡' },
    { key: 'storage', label: 'Storage', icon: '💾' },
    { key: 'battery', label: 'Battery', icon: '🔋' },
  ];

  const renderCategorySelector = () => (
    <View style={styles.categorySelector}>
      {categories.map((category) => (
        <TouchableOpacity
          key={category.key}
          style={[
            styles.categoryButton,
            selectedCategory === category.key && styles.categoryButtonActive,
          ]}
          onPress={() => setSelectedCategory(category.key)}
        >
          <Text style={styles.categoryIcon}>{category.icon}</Text>
          <Text
            style={[
              styles.categoryLabel,
              selectedCategory === category.key && styles.categoryLabelActive,
            ]}
          >
            {category.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderBasicInfo = () => (
    <View style={styles.infoSection}>
      <InfoRow label="Device" value={deviceInfo.deviceName || 'Unknown'} />
      <InfoRow label="Model" value={deviceInfo.model || 'Unknown'} />
      <InfoRow label="OS" value={`${deviceInfo.systemName || 'Unknown'} ${deviceInfo.systemVersion || ''}`} />
      <InfoRow label="Platform" value={deviceInfo.platform || 'Unknown'} />
      <InfoRow label="Brand" value={deviceInfo.brand || 'Unknown'} />
      <InfoRow label="Manufacturer" value={deviceInfo.manufacturer || 'Unknown'} />
      <InfoRow label="Screen" value={`${deviceInfo.screenWidth || 0} × ${deviceInfo.screenHeight || 0}`} />
      <InfoRow label="App Version" value={`${deviceInfo.version || '1.0.0'} (${deviceInfo.buildNumber || '1'})`} />
    </View>
  );

  const renderPerformanceInfo = () => (
    <View style={styles.infoSection}>
      <InfoRow 
        label="Performance Score" 
        value={`${deviceInfo.performanceScore || 0}/100`}
        valueColor={getPerformanceColor(deviceInfo.performanceScore)}
      />
      <InfoRow 
        label="Memory Usage" 
        value={`${deviceInfo.memoryUtilization || 0}%`}
        valueColor={getUsageColor(deviceInfo.memoryUtilization)}
      />
      <InfoRow 
        label="Total Memory" 
        value={formatBytes(deviceInfo.totalMemory)}
      />
      <InfoRow 
        label="Available Memory" 
        value={formatBytes(deviceInfo.availableMemory)}
      />
      <InfoRow 
        label="CPU Cores" 
        value={`${deviceInfo.cpuCount || 1} cores`}
      />
      <InfoRow 
        label="Is Emulator" 
        value={deviceInfo.isEmulator ? 'Yes' : 'No'}
        valueColor={deviceInfo.isEmulator ? '#ff6b6b' : '#51cf66'}
      />
    </View>
  );

  const renderStorageInfo = () => (
    <View style={styles.infoSection}>
      <InfoRow 
        label="Storage Usage" 
        value={`${deviceInfo.storageUtilization || 0}%`}
        valueColor={getUsageColor(deviceInfo.storageUtilization)}
      />
      <InfoRow 
        label="Total Storage" 
        value={formatBytes(deviceInfo.totalDiskCapacity)}
      />
      <InfoRow 
        label="Free Storage" 
        value={formatBytes(deviceInfo.freeDiskStorage)}
      />
      <InfoRow 
        label="Used Storage" 
        value={formatBytes((deviceInfo.totalDiskCapacity || 0) - (deviceInfo.freeDiskStorage || 0))}
      />
    </View>
  );

  const renderBatteryInfo = () => (
    <View style={styles.infoSection}>
      <InfoRow 
        label="Battery Level" 
        value={`${Math.round((deviceInfo.batteryLevel || 0) * 100)}%`}
        valueColor={getBatteryColor(deviceInfo.batteryLevel)}
      />
      <InfoRow 
        label="Charging" 
        value={deviceInfo.isBatteryCharging ? 'Yes' : 'No'}
        valueColor={deviceInfo.isBatteryCharging ? '#51cf66' : '#868e96'}
      />
      <InfoRow 
        label="Power State" 
        value={deviceInfo.powerState?.batteryState || 'Unknown'}
      />
    </View>
  );

  const renderCategoryContent = () => {
    switch (selectedCategory) {
      case 'basic':
        return renderBasicInfo();
      case 'performance':
        return renderPerformanceInfo();
      case 'storage':
        return renderStorageInfo();
      case 'battery':
        return renderBatteryInfo();
      default:
        return renderBasicInfo();
    }
  };

  const animatedHeight = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [120, 400],
  });

  return (
    <Animated.View style={[styles.container, { height: animatedHeight }, style]}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.gradient}
      >
        <TouchableOpacity style={styles.header} onPress={toggleExpanded}>
          <View style={styles.headerContent}>
            <Text style={styles.title}>📱 Device Information</Text>
            <View style={styles.headerStats}>
              <Text style={styles.headerStat}>
                Score: {deviceInfo.performanceScore || 0}/100
              </Text>
              <Text style={styles.headerStat}>
                Memory: {deviceInfo.memoryUtilization || 0}%
              </Text>
            </View>
          </View>
          <Text style={[styles.expandIcon, { transform: [{ rotate: isExpanded ? '180deg' : '0deg' }] }]}>
            ▼
          </Text>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.expandedContent}>
            {renderCategorySelector()}
            <ScrollView style={styles.contentScroll} showsVerticalScrollIndicator={false}>
              {renderCategoryContent()}
            </ScrollView>
          </View>
        )}
      </LinearGradient>
    </Animated.View>
  );
};

const InfoRow = ({ label, value, valueColor = '#ffffff' }) => (
  <View style={styles.infoRow}>
    <Text style={styles.infoLabel}>{label}</Text>
    <Text style={[styles.infoValue, { color: valueColor }]}>{value}</Text>
  </View>
);

// Helper functions
const formatBytes = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

const getPerformanceColor = (score) => {
  if (score >= 80) return '#51cf66';
  if (score >= 60) return '#ffd43b';
  if (score >= 40) return '#ff8cc8';
  return '#ff6b6b';
};

const getUsageColor = (usage) => {
  if (usage >= 90) return '#ff6b6b';
  if (usage >= 75) return '#ff8cc8';
  if (usage >= 50) return '#ffd43b';
  return '#51cf66';
};

const getBatteryColor = (level) => {
  if (level >= 0.5) return '#51cf66';
  if (level >= 0.2) return '#ffd43b';
  return '#ff6b6b';
};

const styles = StyleSheet.create({
  container: {
    margin: 10,
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 5,
  },
  headerStats: {
    flexDirection: 'row',
    gap: 15,
  },
  headerStat: {
    fontSize: 12,
    color: '#e9ecef',
  },
  expandIcon: {
    fontSize: 16,
    color: '#ffffff',
    marginLeft: 10,
  },
  expandedContent: {
    flex: 1,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  categorySelector: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 5,
  },
  categoryButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    borderRadius: 8,
  },
  categoryButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  categoryIcon: {
    fontSize: 16,
    marginBottom: 2,
  },
  categoryLabel: {
    fontSize: 10,
    color: '#e9ecef',
    fontWeight: '500',
  },
  categoryLabelActive: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  contentScroll: {
    flex: 1,
  },
  infoSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 15,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  infoLabel: {
    fontSize: 14,
    color: '#e9ecef',
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'right',
    flex: 1,
  },
});

export default DeviceInfoDisplay;
