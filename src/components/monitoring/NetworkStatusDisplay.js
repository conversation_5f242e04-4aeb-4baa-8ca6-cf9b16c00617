import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { NetworkMonitoringService } from '../../services/NetworkMonitoringService';

const { width } = Dimensions.get('window');

/**
 * NetworkStatusDisplay - Real-time network status and quality monitoring
 * Shows connection status, speed, quality metrics, and usage statistics
 */
const NetworkStatusDisplay = ({ style }) => {
  const [networkState, setNetworkState] = useState({});
  const [qualityMetrics, setQualityMetrics] = useState({});
  const [usageStats, setUsageStats] = useState({});
  const [animatedValue] = useState(new Animated.Value(0));
  const [isExpanded, setIsExpanded] = useState(false);
  const [pulseAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    initializeNetworkMonitoring();
    startPulseAnimation();
    
    return () => {
      stopPulseAnimation();
    };
  }, []);

  const initializeNetworkMonitoring = async () => {
    const networkService = NetworkMonitoringService.getInstance();
    
    if (!networkService.isInitialized) {
      await networkService.initialize();
    }
    
    // Get initial state
    updateNetworkState();
    
    // Add listener for network changes
    const unsubscribe = networkService.addListener((state) => {
      updateNetworkState();
    });
    
    // Update every 10 seconds
    const interval = setInterval(updateNetworkState, 10000);
    
    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  };

  const updateNetworkState = () => {
    const networkService = NetworkMonitoringService.getInstance();
    const state = networkService.getNetworkState();
    const stats = networkService.getNetworkStatistics();
    const qualitySummary = networkService.getQualitySummary();
    
    setNetworkState(state);
    setQualityMetrics(state.qualityMetrics || {});
    setUsageStats(state.usageStats || {});
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopPulseAnimation = () => {
    pulseAnimation.stopAnimation();
  };

  const toggleExpanded = () => {
    const toValue = isExpanded ? 0 : 1;
    
    Animated.spring(animatedValue, {
      toValue,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
    
    setIsExpanded(!isExpanded);
  };

  const getConnectionIcon = () => {
    if (!networkState.isConnected) return '📵';
    
    switch (networkState.type) {
      case 'wifi': return '📶';
      case 'cellular': return '📱';
      case '5g': return '🚀';
      case '4g': return '📶';
      case '3g': return '📶';
      case 'ethernet': return '🌐';
      default: return '🔗';
    }
  };

  const getQualityColor = (quality) => {
    switch (quality) {
      case 'excellent': return '#51cf66';
      case 'good': return '#69db7c';
      case 'fair': return '#ffd43b';
      case 'poor': return '#ff6b6b';
      default: return '#868e96';
    }
  };

  const getSignalStrengthBars = () => {
    const { downloadSpeed = 0 } = qualityMetrics;
    let bars = 0;
    
    if (downloadSpeed > 50) bars = 4;
    else if (downloadSpeed > 25) bars = 3;
    else if (downloadSpeed > 10) bars = 2;
    else if (downloadSpeed > 1) bars = 1;
    
    return Array.from({ length: 4 }, (_, i) => (
      <View
        key={i}
        style={[
          styles.signalBar,
          {
            height: (i + 1) * 4 + 8,
            backgroundColor: i < bars ? '#51cf66' : 'rgba(255, 255, 255, 0.3)',
          },
        ]}
      />
    ));
  };

  const renderSpeedTest = () => {
    const { downloadSpeed = 0, uploadSpeed = 0, latency = 0 } = qualityMetrics;
    
    return (
      <View style={styles.speedTestContainer}>
        <Text style={styles.sectionTitle}>Speed Test Results</Text>
        <View style={styles.speedMetrics}>
          <View style={styles.speedMetric}>
            <Text style={styles.speedValue}>{downloadSpeed.toFixed(1)}</Text>
            <Text style={styles.speedUnit}>Mbps</Text>
            <Text style={styles.speedLabel}>Download</Text>
          </View>
          <View style={styles.speedMetric}>
            <Text style={styles.speedValue}>{uploadSpeed.toFixed(1)}</Text>
            <Text style={styles.speedUnit}>Mbps</Text>
            <Text style={styles.speedLabel}>Upload</Text>
          </View>
          <View style={styles.speedMetric}>
            <Text style={styles.speedValue}>{latency}</Text>
            <Text style={styles.speedUnit}>ms</Text>
            <Text style={styles.speedLabel}>Latency</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderUsageStats = () => (
    <View style={styles.usageContainer}>
      <Text style={styles.sectionTitle}>Usage Statistics</Text>
      <View style={styles.statRow}>
        <Text style={styles.statLabel}>Total Connections</Text>
        <Text style={styles.statValue}>{usageStats.totalConnections || 0}</Text>
      </View>
      <View style={styles.statRow}>
        <Text style={styles.statLabel}>Disconnections</Text>
        <Text style={styles.statValue}>{usageStats.disconnectionCount || 0}</Text>
      </View>
      <View style={styles.statRow}>
        <Text style={styles.statLabel}>Last Speed Test</Text>
        <Text style={styles.statValue}>
          {usageStats.lastSpeedTest ? new Date(usageStats.lastSpeedTest).toLocaleTimeString() : 'Never'}
        </Text>
      </View>
    </View>
  );

  const animatedHeight = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [100, 350],
  });

  const pulseOpacity = pulseAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0.5, 1],
  });

  return (
    <Animated.View style={[styles.container, { height: animatedHeight }, style]}>
      <LinearGradient
        colors={networkState.isConnected ? ['#4facfe', '#00f2fe'] : ['#ff6b6b', '#ee5a52']}
        style={styles.gradient}
      >
        <TouchableOpacity style={styles.header} onPress={toggleExpanded}>
          <View style={styles.headerLeft}>
            <Animated.View style={[styles.connectionIcon, { opacity: pulseOpacity }]}>
              <Text style={styles.connectionIconText}>{getConnectionIcon()}</Text>
            </Animated.View>
            <View style={styles.headerInfo}>
              <Text style={styles.title}>
                {networkState.isConnected ? 'Connected' : 'Disconnected'}
              </Text>
              <Text style={styles.subtitle}>
                {networkState.isConnected 
                  ? `${networkState.type?.toUpperCase() || 'Unknown'} • ${qualityMetrics.quality || 'Unknown'}`
                  : 'No network connection'
                }
              </Text>
            </View>
          </View>
          <View style={styles.headerRight}>
            <View style={styles.signalStrength}>
              {getSignalStrengthBars()}
            </View>
            <Text style={[styles.expandIcon, { transform: [{ rotate: isExpanded ? '180deg' : '0deg' }] }]}>
              ▼
            </Text>
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.expandedContent}>
            {networkState.isConnected && renderSpeedTest()}
            {renderUsageStats()}
            
            <View style={styles.qualityIndicator}>
              <Text style={styles.sectionTitle}>Network Quality</Text>
              <View style={styles.qualityBar}>
                <View 
                  style={[
                    styles.qualityFill,
                    { 
                      width: `${getQualityPercentage(qualityMetrics.quality)}%`,
                      backgroundColor: getQualityColor(qualityMetrics.quality),
                    }
                  ]}
                />
              </View>
              <Text style={[styles.qualityText, { color: getQualityColor(qualityMetrics.quality) }]}>
                {qualityMetrics.quality?.toUpperCase() || 'UNKNOWN'}
              </Text>
            </View>
          </View>
        )}
      </LinearGradient>
    </Animated.View>
  );
};

const getQualityPercentage = (quality) => {
  switch (quality) {
    case 'excellent': return 100;
    case 'good': return 75;
    case 'fair': return 50;
    case 'poor': return 25;
    default: return 0;
  }
};

const styles = StyleSheet.create({
  container: {
    margin: 10,
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  connectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  connectionIconText: {
    fontSize: 20,
  },
  headerInfo: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 12,
    color: '#e9ecef',
  },
  headerRight: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  signalStrength: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginRight: 10,
    gap: 2,
  },
  signalBar: {
    width: 3,
    borderRadius: 1.5,
  },
  expandIcon: {
    fontSize: 16,
    color: '#ffffff',
  },
  expandedContent: {
    flex: 1,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  speedTestContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 10,
  },
  speedMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  speedMetric: {
    alignItems: 'center',
  },
  speedValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  speedUnit: {
    fontSize: 12,
    color: '#e9ecef',
    marginTop: -2,
  },
  speedLabel: {
    fontSize: 10,
    color: '#e9ecef',
    marginTop: 2,
  },
  usageContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#e9ecef',
  },
  statValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  qualityIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 15,
  },
  qualityBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  qualityFill: {
    height: '100%',
    borderRadius: 4,
  },
  qualityText: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default NetworkStatusDisplay;
