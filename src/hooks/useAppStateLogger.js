/**
 * useAppStateLogger Hook
 * Custom hook for monitoring and logging app state changes
 */

import { useEffect, useRef, useState } from 'react';
import { AppState, Platform } from 'react-native';
import { useDispatch } from 'react-redux';
import DeviceInfo from 'react-native-device-info';
import LoggerService from '../services/LoggerService';
import { LogLevel, LogCategory } from '../models/LogEntry';
import { addLog } from '../store/slices/logSlice';

export const useAppStateLogger = () => {
  const dispatch = useDispatch();
  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const [memoryInfo, setMemoryInfo] = useState(null);
  const [batteryLevel, setBatteryLevel] = useState(null);
  const sessionStartTime = useRef(Date.now());
  const backgroundTime = useRef(null);
  const foregroundTime = useRef(null);

  // Memory monitoring
  useEffect(() => {
    const monitorMemory = async () => {
      try {
        const [totalMemory, usedMemory] = await Promise.all([
          DeviceInfo.getTotalMemory(),
          DeviceInfo.getUsedMemory()
        ]);

        const memoryData = {
          totalMemory,
          usedMemory,
          freeMemory: totalMemory - usedMemory,
          usagePercentage: (usedMemory / totalMemory) * 100,
          timestamp: Date.now()
        };

        setMemoryInfo(memoryData);

        // Log memory warnings
        if (memoryData.usagePercentage > 80) {
          const logEntry = {
            id: `memory_warning_${Date.now()}`,
            timestamp: new Date().toISOString(),
            level: LogLevel.WARNING,
            category: LogCategory.MEMORY_USAGE,
            component: 'MemoryMonitor',
            message: `High memory usage: ${memoryData.usagePercentage.toFixed(1)}%`,
            data: memoryData
          };

          LoggerService.log(
            LogLevel.WARNING,
            LogCategory.MEMORY_USAGE,
            'MemoryMonitor',
            `High memory usage: ${memoryData.usagePercentage.toFixed(1)}%`,
            memoryData
          );

          dispatch(addLog(logEntry));
        }

        // Log critical memory usage
        if (memoryData.usagePercentage > 90) {
          const logEntry = {
            id: `memory_critical_${Date.now()}`,
            timestamp: new Date().toISOString(),
            level: LogLevel.ERROR,
            category: LogCategory.MEMORY_USAGE,
            component: 'MemoryMonitor',
            message: `Critical memory usage: ${memoryData.usagePercentage.toFixed(1)}%`,
            data: memoryData
          };

          LoggerService.log(
            LogLevel.ERROR,
            LogCategory.MEMORY_USAGE,
            'MemoryMonitor',
            `Critical memory usage: ${memoryData.usagePercentage.toFixed(1)}%`,
            memoryData
          );

          dispatch(addLog(logEntry));
        }

      } catch (error) {
        const logEntry = {
          id: `memory_error_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: LogLevel.ERROR,
          category: LogCategory.MEMORY_USAGE,
          component: 'MemoryMonitor',
          message: 'Failed to get memory information',
          data: { error: error.message }
        };

        LoggerService.error('MemoryMonitor', 'Failed to get memory information', error);
        dispatch(addLog(logEntry));
      }
    };

    // Monitor memory every 10 seconds
    const memoryInterval = setInterval(monitorMemory, 10000);
    monitorMemory(); // Initial check

    return () => clearInterval(memoryInterval);
  }, [dispatch]);

  // Battery monitoring
  useEffect(() => {
    const monitorBattery = async () => {
      try {
        if (Platform.OS === 'android') {
          // Battery monitoring would be implemented here
          // For now, we'll simulate battery data
          const batteryData = {
            level: Math.random() * 100,
            isCharging: Math.random() > 0.5,
            timestamp: Date.now()
          };

          setBatteryLevel(batteryData);

          // Log low battery warnings
          if (batteryData.level < 20 && !batteryData.isCharging) {
            const logEntry = {
              id: `battery_low_${Date.now()}`,
              timestamp: new Date().toISOString(),
              level: LogLevel.WARNING,
              category: LogCategory.BATTERY_STATUS,
              component: 'BatteryMonitor',
              message: `Low battery: ${batteryData.level.toFixed(1)}%`,
              data: batteryData
            };

            LoggerService.log(
              LogLevel.WARNING,
              LogCategory.BATTERY_STATUS,
              'BatteryMonitor',
              `Low battery: ${batteryData.level.toFixed(1)}%`,
              batteryData
            );

            dispatch(addLog(logEntry));
          }
        }
      } catch (error) {
        LoggerService.error('BatteryMonitor', 'Failed to get battery information', error);
      }
    };

    // Monitor battery every 30 seconds
    const batteryInterval = setInterval(monitorBattery, 30000);
    monitorBattery(); // Initial check

    return () => clearInterval(batteryInterval);
  }, [dispatch]);

  // App state monitoring
  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      const previousState = appState.current;
      const now = Date.now();
      const sessionDuration = now - sessionStartTime.current;

      // Calculate time spent in previous state
      let timeInPreviousState = 0;
      if (previousState === 'background' && backgroundTime.current) {
        timeInPreviousState = now - backgroundTime.current;
      } else if (previousState === 'active' && foregroundTime.current) {
        timeInPreviousState = now - foregroundTime.current;
      }

      const stateChangeData = {
        previousState,
        currentState: nextAppState,
        sessionDuration,
        timeInPreviousState,
        timestamp: new Date().toISOString(),
        memoryInfo: memoryInfo,
        batteryLevel: batteryLevel
      };

      // Update time tracking
      if (nextAppState === 'background') {
        backgroundTime.current = now;
      } else if (nextAppState === 'active') {
        foregroundTime.current = now;
      }

      // Log state change
      const logEntry = {
        id: `app_state_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.LIFECYCLE,
        category: LogCategory.APP_LIFECYCLE,
        component: 'AppStateMonitor',
        message: `App state: ${previousState} → ${nextAppState}`,
        data: stateChangeData
      };

      LoggerService.log(
        LogLevel.LIFECYCLE,
        LogCategory.APP_LIFECYCLE,
        'AppStateMonitor',
        `App state: ${previousState} → ${nextAppState}`,
        stateChangeData
      );

      dispatch(addLog(logEntry));

      // Log specific transitions
      if (previousState === 'background' && nextAppState === 'active') {
        const foregroundLogEntry = {
          id: `app_foreground_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: LogLevel.INFO,
          category: LogCategory.APP_LIFECYCLE,
          component: 'AppStateMonitor',
          message: `App returned to foreground after ${(timeInPreviousState / 1000).toFixed(1)}s`,
          data: {
            backgroundDuration: timeInPreviousState,
            sessionDuration,
            memoryInfo,
            batteryLevel
          }
        };

        LoggerService.info(
          'AppStateMonitor',
          `App returned to foreground after ${(timeInPreviousState / 1000).toFixed(1)}s`,
          foregroundLogEntry.data
        );

        dispatch(addLog(foregroundLogEntry));

      } else if (previousState === 'active' && nextAppState === 'background') {
        const backgroundLogEntry = {
          id: `app_background_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: LogLevel.INFO,
          category: LogCategory.APP_LIFECYCLE,
          component: 'AppStateMonitor',
          message: `App moved to background after ${(timeInPreviousState / 1000).toFixed(1)}s active`,
          data: {
            activeDuration: timeInPreviousState,
            sessionDuration,
            memoryInfo,
            batteryLevel
          }
        };

        LoggerService.info(
          'AppStateMonitor',
          `App moved to background after ${(timeInPreviousState / 1000).toFixed(1)}s active`,
          backgroundLogEntry.data
        );

        dispatch(addLog(backgroundLogEntry));
      }

      appState.current = nextAppState;
      setAppStateVisible(appState.current);
    };

    // Subscribe to app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Log initial app state
    const initialLogEntry = {
      id: `app_start_${Date.now()}`,
      timestamp: new Date().toISOString(),
      level: LogLevel.LIFECYCLE,
      category: LogCategory.APP_LIFECYCLE,
      component: 'AppStateMonitor',
      message: `App started in ${appState.current} state`,
      data: {
        initialState: appState.current,
        sessionStartTime: sessionStartTime.current,
        platform: Platform.OS,
        platformVersion: Platform.Version
      }
    };

    LoggerService.log(
      LogLevel.LIFECYCLE,
      LogCategory.APP_LIFECYCLE,
      'AppStateMonitor',
      `App started in ${appState.current} state`,
      initialLogEntry.data
    );

    dispatch(addLog(initialLogEntry));

    return () => {
      subscription?.remove();
      
      // Log session end
      const sessionEndLogEntry = {
        id: `app_session_end_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.LIFECYCLE,
        category: LogCategory.APP_LIFECYCLE,
        component: 'AppStateMonitor',
        message: `App session ended after ${((Date.now() - sessionStartTime.current) / 1000).toFixed(1)}s`,
        data: {
          sessionDuration: Date.now() - sessionStartTime.current,
          finalState: appState.current,
          memoryInfo,
          batteryLevel
        }
      };

      LoggerService.log(
        LogLevel.LIFECYCLE,
        LogCategory.APP_LIFECYCLE,
        'AppStateMonitor',
        sessionEndLogEntry.message,
        sessionEndLogEntry.data
      );

      dispatch(addLog(sessionEndLogEntry));
    };
  }, [dispatch, memoryInfo, batteryLevel]);

  // Performance monitoring
  useEffect(() => {
    const monitorPerformance = () => {
      const performanceData = {
        timestamp: Date.now(),
        sessionDuration: Date.now() - sessionStartTime.current,
        appState: appStateVisible,
        memoryInfo,
        batteryLevel
      };

      const logEntry = {
        id: `performance_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.PERFORMANCE,
        category: LogCategory.PERFORMANCE_METRICS,
        component: 'PerformanceMonitor',
        message: 'Performance metrics update',
        data: performanceData,
        performanceMetrics: {
          sessionDuration: performanceData.sessionDuration,
          memoryUsage: memoryInfo?.usagePercentage || 0,
          batteryLevel: batteryLevel?.level || 100
        }
      };

      LoggerService.performance(
        'PerformanceMonitor',
        'metrics_update',
        logEntry.performanceMetrics
      );

      dispatch(addLog(logEntry));
    };

    // Monitor performance every 60 seconds
    const performanceInterval = setInterval(monitorPerformance, 60000);

    return () => clearInterval(performanceInterval);
  }, [dispatch, appStateVisible, memoryInfo, batteryLevel]);

  return {
    appState: appStateVisible,
    memoryInfo,
    batteryLevel,
    sessionDuration: Date.now() - sessionStartTime.current,
    isBackground: appStateVisible === 'background',
    isActive: appStateVisible === 'active'
  };
};

export default useAppStateLogger;
