/**
 * useCamera Hook
 * Custom hook for managing camera state and operations
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch } from 'react-redux';
import CameraService from '../services/CameraService';
import LoggerService from '../services/LoggerService';
import { LogLevel, LogCategory } from '../models/LogEntry';
import { addLog } from '../store/slices/logSlice';
import {
  CameraDeviceType,
  CameraMode,
  FlashMode,
  FocusMode,
  CameraPermission,
  CameraError
} from '../types/CameraTypes';

export const useCamera = () => {
  const dispatch = useDispatch();
  const [cameraStatus, setCameraStatus] = useState(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState(null);
  const [captureInProgress, setCaptureInProgress] = useState(false);
  const [lastCaptureResult, setLastCaptureResult] = useState(null);
  
  const statusUpdateInterval = useRef(null);
  const performanceTracker = useRef({
    operationCount: 0,
    totalOperationTime: 0,
    lastOperationTime: 0
  });

  // Initialize camera service
  const initializeCamera = useCallback(async () => {
    if (isInitializing) return;
    
    setIsInitializing(true);
    setError(null);
    
    const startTime = Date.now();
    
    try {
      const logEntry = {
        id: `camera_init_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Initializing camera service',
        data: { startTime }
      };

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'useCamera',
        'Initializing camera service'
      );

      dispatch(addLog(logEntry));

      await CameraService.initialize();
      const status = CameraService.getStatus();
      setCameraStatus(status);

      const initTime = Date.now() - startTime;
      performanceTracker.current.operationCount++;
      performanceTracker.current.totalOperationTime += initTime;
      performanceTracker.current.lastOperationTime = initTime;

      const successLogEntry = {
        id: `camera_init_success_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Camera service initialized successfully',
        data: {
          initializationTime: initTime,
          status,
          performanceMetrics: performanceTracker.current
        }
      };

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'useCamera',
        'Camera service initialized successfully',
        successLogEntry.data
      );

      dispatch(addLog(successLogEntry));

    } catch (err) {
      const initTime = Date.now() - startTime;
      setError(err.message);

      const errorLogEntry = {
        id: `camera_init_error_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.ERROR,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Camera initialization failed',
        data: {
          error: err.message,
          initializationTime: initTime,
          stack: err.stack
        }
      };

      LoggerService.error('useCamera', 'Camera initialization failed', err);
      dispatch(addLog(errorLogEntry));
    } finally {
      setIsInitializing(false);
    }
  }, [dispatch, isInitializing]);

  // Switch camera device
  const switchDevice = useCallback(async (deviceType) => {
    const startTime = Date.now();
    
    try {
      setCaptureInProgress(true);
      
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'useCamera',
        `Switching to ${deviceType} camera`
      );

      await CameraService.switchDevice(deviceType);
      const status = CameraService.getStatus();
      setCameraStatus(status);

      const operationTime = Date.now() - startTime;
      performanceTracker.current.operationCount++;
      performanceTracker.current.totalOperationTime += operationTime;
      performanceTracker.current.lastOperationTime = operationTime;

      const logEntry = {
        id: `camera_switch_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: `Camera switched to ${deviceType}`,
        data: {
          deviceType,
          operationTime,
          newStatus: status,
          performanceMetrics: performanceTracker.current
        }
      };

      dispatch(addLog(logEntry));
      setError(null);

    } catch (err) {
      const operationTime = Date.now() - startTime;
      setError(err.message);

      const errorLogEntry = {
        id: `camera_switch_error_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.ERROR,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Camera device switch failed',
        data: {
          error: err.message,
          deviceType,
          operationTime
        }
      };

      LoggerService.error('useCamera', 'Camera device switch failed', err);
      dispatch(addLog(errorLogEntry));
    } finally {
      setCaptureInProgress(false);
    }
  }, [dispatch]);

  // Capture photo
  const capturePhoto = useCallback(async (options = {}) => {
    const startTime = Date.now();
    
    try {
      setCaptureInProgress(true);
      setError(null);

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'useCamera',
        'Starting photo capture',
        { options }
      );

      const result = await CameraService.capturePhoto(options);
      setLastCaptureResult(result);

      const status = CameraService.getStatus();
      setCameraStatus(status);

      const operationTime = Date.now() - startTime;
      performanceTracker.current.operationCount++;
      performanceTracker.current.totalOperationTime += operationTime;
      performanceTracker.current.lastOperationTime = operationTime;

      const logEntry = {
        id: `photo_capture_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Photo captured successfully',
        data: {
          result: result.toJSON(),
          operationTime,
          options,
          performanceMetrics: performanceTracker.current
        }
      };

      dispatch(addLog(logEntry));
      return result;

    } catch (err) {
      const operationTime = Date.now() - startTime;
      setError(err.message);

      const errorLogEntry = {
        id: `photo_capture_error_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.ERROR,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Photo capture failed',
        data: {
          error: err.message,
          operationTime,
          options
        }
      };

      LoggerService.error('useCamera', 'Photo capture failed', err);
      dispatch(addLog(errorLogEntry));
      throw err;
    } finally {
      setCaptureInProgress(false);
    }
  }, [dispatch]);

  // Start video recording
  const startVideoRecording = useCallback(async (options = {}) => {
    const startTime = Date.now();
    
    try {
      setCaptureInProgress(true);
      setError(null);

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'useCamera',
        'Starting video recording',
        { options }
      );

      await CameraService.startVideoRecording(options);
      const status = CameraService.getStatus();
      setCameraStatus(status);

      const operationTime = Date.now() - startTime;
      const logEntry = {
        id: `video_start_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Video recording started',
        data: {
          operationTime,
          options,
          status
        }
      };

      dispatch(addLog(logEntry));

    } catch (err) {
      const operationTime = Date.now() - startTime;
      setError(err.message);

      const errorLogEntry = {
        id: `video_start_error_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.ERROR,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Failed to start video recording',
        data: {
          error: err.message,
          operationTime,
          options
        }
      };

      LoggerService.error('useCamera', 'Failed to start video recording', err);
      dispatch(addLog(errorLogEntry));
      throw err;
    } finally {
      setCaptureInProgress(false);
    }
  }, [dispatch]);

  // Stop video recording
  const stopVideoRecording = useCallback(async () => {
    const startTime = Date.now();
    
    try {
      setCaptureInProgress(true);
      setError(null);

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'useCamera',
        'Stopping video recording'
      );

      const result = await CameraService.stopVideoRecording();
      setLastCaptureResult(result);

      const status = CameraService.getStatus();
      setCameraStatus(status);

      const operationTime = Date.now() - startTime;
      performanceTracker.current.operationCount++;
      performanceTracker.current.totalOperationTime += operationTime;
      performanceTracker.current.lastOperationTime = operationTime;

      const logEntry = {
        id: `video_stop_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Video recording stopped',
        data: {
          result: result.toJSON(),
          operationTime,
          performanceMetrics: performanceTracker.current
        }
      };

      dispatch(addLog(logEntry));
      return result;

    } catch (err) {
      const operationTime = Date.now() - startTime;
      setError(err.message);

      const errorLogEntry = {
        id: `video_stop_error_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.ERROR,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Failed to stop video recording',
        data: {
          error: err.message,
          operationTime
        }
      };

      LoggerService.error('useCamera', 'Failed to stop video recording', err);
      dispatch(addLog(errorLogEntry));
      throw err;
    } finally {
      setCaptureInProgress(false);
    }
  }, [dispatch]);

  // Update camera settings
  const updateSettings = useCallback((newSettings) => {
    try {
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'useCamera',
        'Updating camera settings',
        { newSettings }
      );

      const updatedSettings = CameraService.updateSettings(newSettings);
      const status = CameraService.getStatus();
      setCameraStatus(status);

      const logEntry = {
        id: `settings_update_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Camera settings updated',
        data: {
          newSettings,
          updatedSettings: updatedSettings.toJSON()
        }
      };

      dispatch(addLog(logEntry));
      setError(null);

    } catch (err) {
      setError(err.message);

      const errorLogEntry = {
        id: `settings_update_error_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.ERROR,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Failed to update camera settings',
        data: {
          error: err.message,
          newSettings
        }
      };

      LoggerService.error('useCamera', 'Failed to update camera settings', err);
      dispatch(addLog(errorLogEntry));
    }
  }, [dispatch]);

  // Set zoom level
  const setZoom = useCallback(async (zoomLevel) => {
    try {
      await CameraService.setZoom(zoomLevel);
      const status = CameraService.getStatus();
      setCameraStatus(status);
      setError(null);
    } catch (err) {
      setError(err.message);
      LoggerService.error('useCamera', 'Failed to set zoom level', err);
    }
  }, []);

  // Set flash mode
  const setFlashMode = useCallback(async (flashMode) => {
    try {
      await CameraService.setFlashMode(flashMode);
      const status = CameraService.getStatus();
      setCameraStatus(status);
      setError(null);
    } catch (err) {
      setError(err.message);
      LoggerService.error('useCamera', 'Failed to set flash mode', err);
    }
  }, []);

  // Focus at point
  const focusAtPoint = useCallback(async (x, y) => {
    try {
      await CameraService.focusAtPoint(x, y);
      setError(null);
    } catch (err) {
      setError(err.message);
      LoggerService.error('useCamera', 'Failed to focus at point', err);
    }
  }, []);

  // Update camera status periodically
  useEffect(() => {
    if (cameraStatus?.isInitialized) {
      statusUpdateInterval.current = setInterval(() => {
        const status = CameraService.getStatus();
        setCameraStatus(status);
      }, 1000);

      return () => {
        if (statusUpdateInterval.current) {
          clearInterval(statusUpdateInterval.current);
        }
      };
    }
  }, [cameraStatus?.isInitialized]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (statusUpdateInterval.current) {
        clearInterval(statusUpdateInterval.current);
      }
      
      CameraService.cleanup();
      
      const logEntry = {
        id: `camera_cleanup_${Date.now()}`,
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        category: LogCategory.CAMERA_OPERATIONS,
        component: 'useCamera',
        message: 'Camera hook cleaned up',
        data: {
          performanceMetrics: performanceTracker.current
        }
      };

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'useCamera',
        'Camera hook cleaned up',
        logEntry.data
      );

      dispatch(addLog(logEntry));
    };
  }, [dispatch]);

  return {
    // State
    cameraStatus,
    isInitializing,
    error,
    captureInProgress,
    lastCaptureResult,
    performanceMetrics: performanceTracker.current,

    // Actions
    initializeCamera,
    switchDevice,
    capturePhoto,
    startVideoRecording,
    stopVideoRecording,
    updateSettings,
    setZoom,
    setFlashMode,
    focusAtPoint,

    // Computed values
    isInitialized: cameraStatus?.isInitialized || false,
    hasPermission: cameraStatus?.permissionStatus === CameraPermission.GRANTED,
    isRecording: cameraStatus?.isRecording || false,
    currentDevice: cameraStatus?.currentDevice,
    availableDevices: cameraStatus?.availableDevices || [],
    currentSettings: cameraStatus?.currentSettings,
    statistics: cameraStatus?.statistics
  };
};

export default useCamera;
