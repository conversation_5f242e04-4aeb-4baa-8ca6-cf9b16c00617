/**
 * LogEntry Model
 * Defines the structure for log entries in the application
 */

export const LogLevel = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  PERFORMANCE: 'PERFORMANCE',
  CAMERA: 'CAMERA',
  LIFECYCLE: 'LIFECYCLE',
  NETWORK: 'NETWORK',
  STORAGE: 'STORAGE'
};

export const LogCategory = {
  APP_LIFECYCLE: 'APP_LIFECYCLE',
  CAMERA_OPERATIONS: 'CAMERA_OPERATIONS',
  UI_INTERACTIONS: 'UI_INTERACTIONS',
  PERFORMANCE_METRICS: 'PERFORMANCE_METRICS',
  NETWORK_REQUESTS: 'NETWORK_REQUESTS',
  STORAGE_OPERATIONS: 'STORAGE_OPERATIONS',
  ERROR_HANDLING: 'ERROR_HANDLING',
  DEVICE_INFO: 'DEVICE_INFO',
  MEMORY_USAGE: 'MEMORY_USAGE',
  BATTERY_STATUS: 'BATTERY_STATUS'
};

export class LogEntry {
  constructor({
    id = null,
    timestamp = new Date().toISOString(),
    level = LogLevel.INFO,
    category = LogCategory.APP_LIFECYCLE,
    component = 'Unknown',
    message = '',
    data = null,
    performanceMetrics = null,
    stackTrace = null,
    userId = null,
    sessionId = null,
    deviceInfo = null
  } = {}) {
    this.id = id || this.generateId();
    this.timestamp = timestamp;
    this.level = level;
    this.category = category;
    this.component = component;
    this.message = message;
    this.data = data;
    this.performanceMetrics = performanceMetrics;
    this.stackTrace = stackTrace;
    this.userId = userId;
    this.sessionId = sessionId;
    this.deviceInfo = deviceInfo;
  }

  generateId() {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Convert log entry to JSON string
   */
  toJSON() {
    return {
      id: this.id,
      timestamp: this.timestamp,
      level: this.level,
      category: this.category,
      component: this.component,
      message: this.message,
      data: this.data,
      performanceMetrics: this.performanceMetrics,
      stackTrace: this.stackTrace,
      userId: this.userId,
      sessionId: this.sessionId,
      deviceInfo: this.deviceInfo
    };
  }

  /**
   * Create LogEntry from JSON object
   */
  static fromJSON(json) {
    return new LogEntry(json);
  }

  /**
   * Get formatted timestamp for display
   */
  getFormattedTimestamp() {
    const date = new Date(this.timestamp);
    return date.toLocaleTimeString() + '.' + date.getMilliseconds().toString().padStart(3, '0');
  }

  /**
   * Get color for log level
   */
  getLevelColor() {
    switch (this.level) {
      case LogLevel.DEBUG: return '#6B7280';
      case LogLevel.INFO: return '#3B82F6';
      case LogLevel.WARNING: return '#F59E0B';
      case LogLevel.ERROR: return '#EF4444';
      case LogLevel.PERFORMANCE: return '#8B5CF6';
      case LogLevel.CAMERA: return '#10B981';
      case LogLevel.LIFECYCLE: return '#F97316';
      case LogLevel.NETWORK: return '#06B6D4';
      case LogLevel.STORAGE: return '#84CC16';
      default: return '#6B7280';
    }
  }

  /**
   * Get icon for log category
   */
  getCategoryIcon() {
    switch (this.category) {
      case LogCategory.APP_LIFECYCLE: return '🔄';
      case LogCategory.CAMERA_OPERATIONS: return '📷';
      case LogCategory.UI_INTERACTIONS: return '👆';
      case LogCategory.PERFORMANCE_METRICS: return '⚡';
      case LogCategory.NETWORK_REQUESTS: return '🌐';
      case LogCategory.STORAGE_OPERATIONS: return '💾';
      case LogCategory.ERROR_HANDLING: return '❌';
      case LogCategory.DEVICE_INFO: return '📱';
      case LogCategory.MEMORY_USAGE: return '🧠';
      case LogCategory.BATTERY_STATUS: return '🔋';
      default: return '📝';
    }
  }

  /**
   * Check if log entry matches filter criteria
   */
  matchesFilter(filter) {
    if (!filter) return true;

    const {
      levels = [],
      categories = [],
      components = [],
      searchText = '',
      startTime = null,
      endTime = null
    } = filter;

    // Level filter
    if (levels.length > 0 && !levels.includes(this.level)) {
      return false;
    }

    // Category filter
    if (categories.length > 0 && !categories.includes(this.category)) {
      return false;
    }

    // Component filter
    if (components.length > 0 && !components.includes(this.component)) {
      return false;
    }

    // Search text filter
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      const messageMatch = this.message.toLowerCase().includes(searchLower);
      const componentMatch = this.component.toLowerCase().includes(searchLower);
      const dataMatch = this.data && JSON.stringify(this.data).toLowerCase().includes(searchLower);
      
      if (!messageMatch && !componentMatch && !dataMatch) {
        return false;
      }
    }

    // Time range filter
    const logTime = new Date(this.timestamp).getTime();
    if (startTime && logTime < startTime) {
      return false;
    }
    if (endTime && logTime > endTime) {
      return false;
    }

    return true;
  }

  /**
   * Get log entry size in bytes (approximate)
   */
  getSize() {
    return JSON.stringify(this.toJSON()).length;
  }

  /**
   * Create performance log entry
   */
  static createPerformanceLog(component, operation, metrics) {
    return new LogEntry({
      level: LogLevel.PERFORMANCE,
      category: LogCategory.PERFORMANCE_METRICS,
      component,
      message: `Performance: ${operation}`,
      performanceMetrics: {
        operation,
        duration: metrics.duration,
        memoryUsage: metrics.memoryUsage,
        cpuUsage: metrics.cpuUsage,
        fps: metrics.fps,
        ...metrics
      }
    });
  }

  /**
   * Create camera operation log entry
   */
  static createCameraLog(operation, result, error = null) {
    return new LogEntry({
      level: error ? LogLevel.ERROR : LogLevel.CAMERA,
      category: LogCategory.CAMERA_OPERATIONS,
      component: 'CameraService',
      message: `Camera: ${operation}${error ? ' failed' : ' completed'}`,
      data: {
        operation,
        result,
        error: error ? error.message : null
      },
      stackTrace: error ? error.stack : null
    });
  }

  /**
   * Create lifecycle log entry
   */
  static createLifecycleLog(event, state, additionalData = null) {
    return new LogEntry({
      level: LogLevel.LIFECYCLE,
      category: LogCategory.APP_LIFECYCLE,
      component: 'AppLifecycle',
      message: `Lifecycle: ${event} - ${state}`,
      data: {
        event,
        state,
        ...additionalData
      }
    });
  }

  /**
   * Create error log entry
   */
  static createErrorLog(component, error, context = null) {
    return new LogEntry({
      level: LogLevel.ERROR,
      category: LogCategory.ERROR_HANDLING,
      component,
      message: `Error: ${error.message}`,
      data: {
        errorName: error.name,
        errorMessage: error.message,
        context
      },
      stackTrace: error.stack
    });
  }
}

export default LogEntry;
