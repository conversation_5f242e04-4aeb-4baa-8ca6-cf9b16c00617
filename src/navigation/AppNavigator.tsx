/**
 * AppNavigator - Main navigation component with accessibility support
 * Handles all navigation flows and accessibility features
 */

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Platform } from 'react-native';

// Screens
import CameraScreen from '../screens/CameraScreen';
import GalleryScreen from '../screens/GalleryScreen';
import SettingsScreen from '../screens/SettingsScreen';
import LogsScreen from '../screens/LogsScreen';
import PerformanceScreen from '../screens/PerformanceScreen';
import SecurityScreen from '../screens/SecurityScreen';

// Components
import TabBarIcon from '../components/TabBarIcon';

// Theme
import { AppTheme } from '../styles/AppTheme';

// Types
export type RootStackParamList = {
  MainTabs: undefined;
  Camera: undefined;
  Gallery: undefined;
  Settings: undefined;
  Logs: undefined;
  Performance: undefined;
  Security: undefined;
};

export type MainTabParamList = {
  Camera: undefined;
  Gallery: undefined;
  Settings: undefined;
  Logs: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

/**
 * Main tab navigator
 */
const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => (
          <TabBarIcon
            name={getTabIconName(route.name)}
            focused={focused}
            color={color}
            size={size}
          />
        ),
        tabBarActiveTintColor: AppTheme.colors.primary,
        tabBarInactiveTintColor: AppTheme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: AppTheme.colors.background,
          borderTopColor: AppTheme.colors.border,
          borderTopWidth: 1,
          height: Platform.select({
            ios: 88,
            android: 68,
          }),
          paddingBottom: Platform.select({
            ios: 34,
            android: 8,
          }),
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: AppTheme.fontSizes.small,
          fontFamily: AppTheme.fonts.medium,
          marginTop: 4,
        },
        headerStyle: {
          backgroundColor: AppTheme.colors.background,
          borderBottomColor: AppTheme.colors.border,
          borderBottomWidth: 1,
        },
        headerTitleStyle: {
          fontSize: AppTheme.fontSizes.large,
          fontFamily: AppTheme.fonts.bold,
          color: AppTheme.colors.text,
        },
        headerTintColor: AppTheme.colors.primary,
        // Accessibility
        tabBarAccessibilityLabel: getTabAccessibilityLabel(route.name),
        tabBarTestID: `tab-${route.name.toLowerCase()}`,
      })}
    >
      <Tab.Screen
        name="Camera"
        component={CameraScreen}
        options={{
          title: 'Camera',
          headerShown: false, // Camera screen handles its own header
          tabBarLabel: 'Camera',
        }}
      />
      <Tab.Screen
        name="Gallery"
        component={GalleryScreen}
        options={{
          title: 'Gallery',
          tabBarLabel: 'Gallery',
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Settings',
          tabBarLabel: 'Settings',
        }}
      />
      <Tab.Screen
        name="Logs"
        component={LogsScreen}
        options={{
          title: 'Logs',
          tabBarLabel: 'Logs',
        }}
      />
    </Tab.Navigator>
  );
};

/**
 * Root stack navigator
 */
const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer
      theme={{
        dark: false,
        colors: {
          primary: AppTheme.colors.primary,
          background: AppTheme.colors.background,
          card: AppTheme.colors.background,
          text: AppTheme.colors.text,
          border: AppTheme.colors.border,
          notification: AppTheme.colors.error,
        },
      }}
    >
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: AppTheme.colors.background,
          },
          headerTitleStyle: {
            fontSize: AppTheme.fontSizes.large,
            fontFamily: AppTheme.fonts.bold,
            color: AppTheme.colors.text,
          },
          headerTintColor: AppTheme.colors.primary,
          headerBackTitleVisible: false,
          gestureEnabled: true,
          animation: Platform.select({
            ios: 'slide_from_right',
            android: 'slide_from_right',
          }),
        }}
      >
        <Stack.Screen
          name="MainTabs"
          component={MainTabNavigator}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Performance"
          component={PerformanceScreen}
          options={{
            title: 'Performance Monitor',
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="Security"
          component={SecurityScreen}
          options={{
            title: 'Security Status',
            presentation: 'modal',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

/**
 * Get tab icon name based on route
 */
const getTabIconName = (routeName: string): string => {
  switch (routeName) {
    case 'Camera':
      return 'camera';
    case 'Gallery':
      return 'image';
    case 'Settings':
      return 'settings';
    case 'Logs':
      return 'list';
    default:
      return 'circle';
  }
};

/**
 * Get accessibility label for tab
 */
const getTabAccessibilityLabel = (routeName: string): string => {
  switch (routeName) {
    case 'Camera':
      return 'Camera tab, take photos and videos';
    case 'Gallery':
      return 'Gallery tab, view captured media';
    case 'Settings':
      return 'Settings tab, configure app preferences';
    case 'Logs':
      return 'Logs tab, view application logs';
    default:
      return `${routeName} tab`;
  }
};

export default AppNavigator;
