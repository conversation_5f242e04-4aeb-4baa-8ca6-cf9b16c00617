/**
 * CameraScreen - Main camera interface with split screen layout
 * Top half: Camera preview and controls
 * Bottom half: Real-time logging display
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  StatusBar,
  Platform,
  SafeAreaView,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// Camera components
import CameraPreview from '../components/camera/CameraPreview';
import CameraControls from '../components/camera/CameraControls';
import CameraSettings from '../components/camera/CameraSettings';
import CameraOverlay from '../components/camera/CameraOverlay';

// Logging components
import LogDisplay from '../components/logging/LogDisplay';

// Monitoring components
import DeviceInfoDisplay from '../components/monitoring/DeviceInfoDisplay';
import NetworkStatusDisplay from '../components/monitoring/NetworkStatusDisplay';
import LocationMapView from '../components/maps/LocationMapView';

// Hooks and services
import useCamera from '../hooks/useCamera';
import LoggerService from '../services/LoggerService';
import PerformanceMonitor from '../utils/PerformanceMonitor';
import { DeviceInfoService } from '../services/DeviceInfoService';
import { NetworkMonitoringService } from '../services/NetworkMonitoringService';
import { LocationService } from '../services/LocationService';
import { BackgroundProcessingService } from '../services/BackgroundProcessingService';

// Redux selectors
import { selectFilteredLogs, selectDisplaySettings } from '../store/slices/logSlice';

// Types and constants
import { LogLevel, LogCategory } from '../models/LogEntry';
import { CameraMode, FlashMode, CameraDeviceType } from '../types/CameraTypes';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const CAMERA_HEIGHT = screenHeight * 0.5; // Top 50% for camera
const LOG_HEIGHT = screenHeight * 0.5; // Bottom 50% for logs

const CameraScreen = () => {
  const dispatch = useDispatch();
  const logs = useSelector(selectFilteredLogs);
  const displaySettings = useSelector(selectDisplaySettings);
  
  // Camera hook
  const {
    cameraStatus,
    isInitializing,
    error,
    captureInProgress,
    lastCaptureResult,
    initializeCamera,
    switchDevice,
    capturePhoto,
    startVideoRecording,
    stopVideoRecording,
    updateSettings,
    setZoom,
    setFlashMode,
    focusAtPoint,
    isInitialized,
    hasPermission,
    isRecording,
    currentDevice,
    availableDevices,
    currentSettings,
    statistics
  } = useCamera();

  // Local state
  const [showSettings, setShowSettings] = useState(false);
  const [showOverlay, setShowOverlay] = useState(true);
  const [zoomLevel, setZoomLevel] = useState(1.0);
  const [flashMode, setFlashModeState] = useState(FlashMode.OFF);
  const [cameraMode, setCameraMode] = useState(CameraMode.PHOTO);
  const [isCapturing, setIsCapturing] = useState(false);
  const [performanceData, setPerformanceData] = useState(null);

  // Refs
  const cameraRef = useRef(null);
  const performanceInterval = useRef(null);
  const renderTracker = useRef(null);

  // Initialize camera on mount
  useEffect(() => {
    const initCamera = async () => {
      try {
        renderTracker.current = PerformanceMonitor.startRenderTracking('CameraScreen');
        
        LoggerService.log(
          LogLevel.INFO,
          LogCategory.CAMERA_OPERATIONS,
          'CameraScreen',
          'Initializing camera screen'
        );

        await initializeCamera();

        // Initialize advanced services
        await initializeAdvancedServices();

        LoggerService.log(
          LogLevel.INFO,
          LogCategory.CAMERA_OPERATIONS,
          'CameraScreen',
          'Camera screen initialized successfully'
        );

      } catch (err) {
        LoggerService.error('CameraScreen', 'Failed to initialize camera screen', err);
        Alert.alert('Camera Error', err.message);
      } finally {
        if (renderTracker.current) {
          renderTracker.current();
        }
      }
    };

    initCamera();
  }, [initializeCamera]);

  // Performance monitoring
  useEffect(() => {
    performanceInterval.current = setInterval(() => {
      const metrics = PerformanceMonitor.getCurrentMetrics();
      setPerformanceData(metrics);
      
      LoggerService.log(
        LogLevel.PERFORMANCE,
        LogCategory.PERFORMANCE_METRICS,
        'CameraScreen',
        'Performance metrics updated',
        metrics
      );
    }, 5000);

    return () => {
      if (performanceInterval.current) {
        clearInterval(performanceInterval.current);
      }
    };
  }, []);

  // Initialize advanced services
  const initializeAdvancedServices = async () => {
    try {
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.LIFECYCLE,
        'CameraScreen',
        'Initializing advanced services'
      );

      // Initialize Device Info Service
      const deviceInfoService = DeviceInfoService.getInstance();
      await deviceInfoService.initialize();

      // Initialize Network Monitoring Service
      const networkService = NetworkMonitoringService.getInstance();
      await networkService.initialize();

      // Initialize Location Service
      const locationService = LocationService.getInstance();
      await locationService.initialize();

      // Initialize Background Processing Service
      const backgroundService = BackgroundProcessingService.getInstance();
      await backgroundService.initialize();

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.LIFECYCLE,
        'CameraScreen',
        'Advanced services initialized successfully'
      );
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.LIFECYCLE,
        'CameraScreen',
        'Failed to initialize advanced services',
        { error: error.message }
      );
    }
  };

  // Handle camera mode change
  const handleModeChange = (mode) => {
    try {
      setCameraMode(mode);
      updateSettings({ mode });
      
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraScreen',
        `Camera mode changed to ${mode}`
      );
    } catch (err) {
      LoggerService.error('CameraScreen', 'Failed to change camera mode', err);
    }
  };

  // Handle device switch
  const handleDeviceSwitch = async () => {
    try {
      const newDeviceType = currentDevice?.type === CameraDeviceType.BACK 
        ? CameraDeviceType.FRONT 
        : CameraDeviceType.BACK;
      
      await switchDevice(newDeviceType);
      
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraScreen',
        `Switched to ${newDeviceType} camera`
      );
    } catch (err) {
      LoggerService.error('CameraScreen', 'Failed to switch camera device', err);
      Alert.alert('Camera Error', 'Failed to switch camera');
    }
  };

  // Handle capture
  const handleCapture = async () => {
    if (isCapturing) return;
    
    setIsCapturing(true);
    
    try {
      if (cameraMode === CameraMode.VIDEO) {
        if (isRecording) {
          const result = await stopVideoRecording();
          LoggerService.log(
            LogLevel.INFO,
            LogCategory.CAMERA_OPERATIONS,
            'CameraScreen',
            'Video recording stopped',
            { result: result?.toJSON() }
          );
        } else {
          await startVideoRecording();
          LoggerService.log(
            LogLevel.INFO,
            LogCategory.CAMERA_OPERATIONS,
            'CameraScreen',
            'Video recording started'
          );
        }
      } else {
        const result = await capturePhoto();
        LoggerService.log(
          LogLevel.INFO,
          LogCategory.CAMERA_OPERATIONS,
          'CameraScreen',
          'Photo captured',
          { result: result?.toJSON() }
        );
      }
    } catch (err) {
      LoggerService.error('CameraScreen', 'Capture failed', err);
      Alert.alert('Capture Error', err.message);
    } finally {
      setIsCapturing(false);
    }
  };

  // Handle zoom change
  const handleZoomChange = async (newZoom) => {
    try {
      setZoomLevel(newZoom);
      await setZoom(newZoom);
    } catch (err) {
      LoggerService.error('CameraScreen', 'Failed to set zoom', err);
    }
  };

  // Handle flash mode change
  const handleFlashModeChange = async (mode) => {
    try {
      setFlashModeState(mode);
      await setFlashMode(mode);
    } catch (err) {
      LoggerService.error('CameraScreen', 'Failed to set flash mode', err);
    }
  };

  // Handle focus
  const handleFocus = async (event) => {
    try {
      const { locationX, locationY } = event.nativeEvent;
      await focusAtPoint(locationX, locationY);
      
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraScreen',
        'Focus point set',
        { x: locationX, y: locationY }
      );
    } catch (err) {
      LoggerService.error('CameraScreen', 'Focus failed', err);
    }
  };

  // Show loading screen while initializing
  if (isInitializing || !isInitialized) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000" />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Initializing Camera...</Text>
          <Text style={styles.loadingSubtext}>Setting up camera and logging systems</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error screen if no permission
  if (!hasPermission) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000" />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Camera Permission Required</Text>
          <Text style={styles.errorSubtext}>Please grant camera permission to use this feature</Text>
          <TouchableOpacity style={styles.retryButton} onPress={initializeCamera}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />
      
      {/* Camera Section - Top 50% */}
      <View style={styles.cameraSection}>
        <CameraPreview
          ref={cameraRef}
          style={styles.cameraPreview}
          device={currentDevice}
          settings={currentSettings}
          onTouch={handleFocus}
          isActive={true}
        />
        
        {showOverlay && (
          <CameraOverlay
            style={styles.overlay}
            cameraMode={cameraMode}
            isRecording={isRecording}
            zoomLevel={zoomLevel}
            flashMode={flashMode}
            statistics={statistics}
            performanceData={performanceData}
          />
        )}
        
        <CameraControls
          style={styles.controls}
          cameraMode={cameraMode}
          isRecording={isRecording}
          isCapturing={isCapturing || captureInProgress}
          flashMode={flashMode}
          zoomLevel={zoomLevel}
          availableDevices={availableDevices}
          currentDevice={currentDevice}
          onCapture={handleCapture}
          onModeChange={handleModeChange}
          onDeviceSwitch={handleDeviceSwitch}
          onFlashModeChange={handleFlashModeChange}
          onZoomChange={handleZoomChange}
          onSettingsPress={() => setShowSettings(true)}
          onOverlayToggle={() => setShowOverlay(!showOverlay)}
        />
      </View>
      
      {/* Monitoring and Logging Section - Bottom 50% */}
      <View style={styles.logSection}>
        <ScrollView
          style={styles.monitoringScroll}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
        >
          {/* Device Information */}
          <DeviceInfoDisplay style={styles.monitoringComponent} />

          {/* Network Status */}
          <NetworkStatusDisplay style={styles.monitoringComponent} />

          {/* Location Map */}
          <LocationMapView style={styles.locationMapComponent} />

          {/* Logging Display */}
          <LogDisplay
            logs={logs}
            displaySettings={displaySettings}
            style={styles.logDisplay}
          />
        </ScrollView>
      </View>
      
      {/* Settings Modal */}
      {showSettings && (
        <CameraSettings
          visible={showSettings}
          currentSettings={currentSettings}
          deviceCapabilities={currentDevice}
          onSettingsChange={updateSettings}
          onClose={() => setShowSettings(false)}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  cameraSection: {
    height: CAMERA_HEIGHT,
    position: 'relative',
  },
  cameraPreview: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  logSection: {
    height: LOG_HEIGHT,
    backgroundColor: '#1a1a1a',
    borderTopWidth: 1,
    borderTopColor: '#333',
  },
  monitoringScroll: {
    flex: 1,
  },
  monitoringComponent: {
    marginHorizontal: 5,
    marginVertical: 2,
  },
  locationMapComponent: {
    marginHorizontal: 5,
    marginVertical: 2,
    height: 300,
  },
  logDisplay: {
    marginHorizontal: 10,
    marginVertical: 5,
    minHeight: 200,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  loadingSubtext: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
    paddingHorizontal: 20,
  },
  errorText: {
    color: '#ff4444',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    textAlign: 'center',
  },
  errorSubtext: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CameraScreen;
