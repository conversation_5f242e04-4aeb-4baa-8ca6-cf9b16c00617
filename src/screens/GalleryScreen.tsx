/**
 * GalleryScreen - Media gallery with accessibility features
 * Displays captured photos and videos with comprehensive accessibility support
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

// Services
import MultimediaService from '../services/MultimediaService';
import LoggerService from '../services/LoggerService';

// Components
import AccessibleButton from '../components/AccessibleButton';
import LoadingScreen from '../components/LoadingScreen';

// Types
import { LogLevel, LogCategory } from '../models/LogEntry';

// Theme
import { AppTheme } from '../styles/AppTheme';

interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'audio';
  name: string;
  path: string;
  size: number;
  duration?: number;
  resolution?: { width: number; height: number };
  thumbnails: Array<{
    width: number;
    height: number;
    name: string;
    path: string;
    size: number;
  }>;
  created: number;
  modified: number;
}

const { width: screenWidth } = Dimensions.get('window');
const itemSize = (screenWidth - AppTheme.spacing.large * 3) / 2;

const GalleryScreen: React.FC = () => {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectionMode, setSelectionMode] = useState(false);

  useFocusEffect(
    useCallback(() => {
      loadMediaItems();
    }, [])
  );

  /**
   * Load media items from multimedia service
   */
  const loadMediaItems = async () => {
    try {
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.MEDIA,
        'GalleryScreen',
        'Loading media items'
      );

      setLoading(true);
      
      // Get media library statistics and convert to items
      const stats = MultimediaService.getStatistics();
      const mockItems: MediaItem[] = [];
      
      // Generate mock media items for display
      for (let i = 0; i < Math.min(50, stats.totalItems); i++) {
        const type = ['image', 'video', 'audio'][i % 3] as 'image' | 'video' | 'audio';
        mockItems.push({
          id: `media_${i}`,
          type,
          name: `${type}_${i}.${type === 'image' ? 'jpg' : type === 'video' ? 'mp4' : 'mp3'}`,
          path: `assets/${type}s/sample_${i}`,
          size: Math.floor(Math.random() * 10000000) + 1000000,
          duration: type !== 'image' ? Math.floor(Math.random() * 300) + 30 : undefined,
          resolution: type !== 'audio' ? { width: 1920, height: 1080 } : undefined,
          thumbnails: [
            {
              width: 150,
              height: 150,
              name: 'small',
              path: `thumbnails/${i}_small.jpg`,
              size: 15000,
            },
          ],
          created: Date.now() - (i * 86400000),
          modified: Date.now() - (i * 43200000),
        });
      }

      setMediaItems(mockItems);
      
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.MEDIA,
        'GalleryScreen',
        'Media items loaded successfully',
        { count: mockItems.length }
      );
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.MEDIA,
        'GalleryScreen',
        'Failed to load media items',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );

      Alert.alert(
        'Error',
        'Failed to load media items. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  /**
   * Handle refresh
   */
  const handleRefresh = () => {
    setRefreshing(true);
    loadMediaItems();
  };

  /**
   * Handle item selection
   */
  const handleItemPress = (item: MediaItem) => {
    if (selectionMode) {
      const newSelection = new Set(selectedItems);
      if (newSelection.has(item.id)) {
        newSelection.delete(item.id);
      } else {
        newSelection.add(item.id);
      }
      setSelectedItems(newSelection);
    } else {
      // Open item in full screen view
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.MEDIA,
        'GalleryScreen',
        'Opening media item',
        { itemId: item.id, type: item.type }
      );
      
      Alert.alert(
        'Media Item',
        `${item.name}\nType: ${item.type}\nSize: ${formatFileSize(item.size)}`,
        [{ text: 'OK' }]
      );
    }
  };

  /**
   * Handle long press to enter selection mode
   */
  const handleItemLongPress = (item: MediaItem) => {
    if (!selectionMode) {
      setSelectionMode(true);
      setSelectedItems(new Set([item.id]));
    }
  };

  /**
   * Exit selection mode
   */
  const exitSelectionMode = () => {
    setSelectionMode(false);
    setSelectedItems(new Set());
  };

  /**
   * Delete selected items
   */
  const deleteSelectedItems = () => {
    Alert.alert(
      'Delete Items',
      `Are you sure you want to delete ${selectedItems.size} item(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const newItems = mediaItems.filter(item => !selectedItems.has(item.id));
            setMediaItems(newItems);
            exitSelectionMode();
            
            LoggerService.log(
              LogLevel.INFO,
              LogCategory.MEDIA,
              'GalleryScreen',
              'Deleted media items',
              { count: selectedItems.size }
            );
          },
        },
      ]
    );
  };

  /**
   * Format file size
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Format duration
   */
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  /**
   * Render media item
   */
  const renderMediaItem = ({ item }: { item: MediaItem }) => {
    const isSelected = selectedItems.has(item.id);
    
    return (
      <TouchableOpacity
        style={[
          styles.mediaItem,
          isSelected && styles.selectedMediaItem,
        ]}
        onPress={() => handleItemPress(item)}
        onLongPress={() => handleItemLongPress(item)}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel={`${item.type} ${item.name}, ${formatFileSize(item.size)}${
          item.duration ? `, duration ${formatDuration(item.duration)}` : ''
        }`}
        accessibilityHint={
          selectionMode
            ? isSelected
              ? 'Double tap to deselect'
              : 'Double tap to select'
            : 'Double tap to view'
        }
        testID={`media-item-${item.id}`}
      >
        <View style={styles.mediaItemContent}>
          {item.type === 'image' ? (
            <View style={styles.imagePlaceholder}>
              <Text style={styles.placeholderText}>IMG</Text>
            </View>
          ) : item.type === 'video' ? (
            <View style={styles.videoPlaceholder}>
              <Text style={styles.placeholderText}>VID</Text>
              {item.duration && (
                <Text style={styles.durationText}>
                  {formatDuration(item.duration)}
                </Text>
              )}
            </View>
          ) : (
            <View style={styles.audioPlaceholder}>
              <Text style={styles.placeholderText}>AUD</Text>
              {item.duration && (
                <Text style={styles.durationText}>
                  {formatDuration(item.duration)}
                </Text>
              )}
            </View>
          )}
          
          {selectionMode && (
            <View style={[styles.selectionIndicator, isSelected && styles.selectedIndicator]}>
              {isSelected && <Text style={styles.checkmark}>✓</Text>}
            </View>
          )}
        </View>
        
        <Text style={styles.mediaItemName} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={styles.mediaItemSize}>
          {formatFileSize(item.size)}
        </Text>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return <LoadingScreen message="Loading gallery..." />;
  }

  return (
    <View style={styles.container}>
      {selectionMode && (
        <View style={styles.selectionHeader}>
          <Text style={styles.selectionText}>
            {selectedItems.size} selected
          </Text>
          <View style={styles.selectionActions}>
            <AccessibleButton
              title="Cancel"
              onPress={exitSelectionMode}
              variant="secondary"
              size="small"
              accessibilityLabel="Cancel selection"
            />
            <AccessibleButton
              title="Delete"
              onPress={deleteSelectedItems}
              variant="danger"
              size="small"
              disabled={selectedItems.size === 0}
              accessibilityLabel={`Delete ${selectedItems.size} selected items`}
            />
          </View>
        </View>
      )}
      
      <FlatList
        data={mediaItems}
        renderItem={renderMediaItem}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={AppTheme.colors.primary}
            colors={[AppTheme.colors.primary]}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No media items found</Text>
            <AccessibleButton
              title="Refresh"
              onPress={handleRefresh}
              variant="primary"
              accessibilityLabel="Refresh gallery"
            />
          </View>
        }
        accessible={false} // Let individual items handle accessibility
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppTheme.colors.background,
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: AppTheme.spacing.medium,
    backgroundColor: AppTheme.colors.backgroundSecondary,
    borderBottomWidth: 1,
    borderBottomColor: AppTheme.colors.border,
  },
  selectionText: {
    fontSize: AppTheme.fontSizes.medium,
    fontFamily: AppTheme.fonts.medium,
    color: AppTheme.colors.text,
  },
  selectionActions: {
    flexDirection: 'row',
    gap: AppTheme.spacing.small,
  },
  listContent: {
    padding: AppTheme.spacing.medium,
  },
  mediaItem: {
    width: itemSize,
    marginRight: AppTheme.spacing.medium,
    marginBottom: AppTheme.spacing.medium,
    borderRadius: AppTheme.borderRadius.medium,
    backgroundColor: AppTheme.colors.white,
    ...AppTheme.shadows.small,
  },
  selectedMediaItem: {
    borderWidth: 2,
    borderColor: AppTheme.colors.primary,
  },
  mediaItemContent: {
    position: 'relative',
    width: '100%',
    height: itemSize,
    borderRadius: AppTheme.borderRadius.medium,
    overflow: 'hidden',
  },
  imagePlaceholder: {
    flex: 1,
    backgroundColor: AppTheme.colors.gray300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoPlaceholder: {
    flex: 1,
    backgroundColor: AppTheme.colors.gray400,
    justifyContent: 'center',
    alignItems: 'center',
  },
  audioPlaceholder: {
    flex: 1,
    backgroundColor: AppTheme.colors.gray500,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: AppTheme.fontSizes.large,
    fontFamily: AppTheme.fonts.bold,
    color: AppTheme.colors.white,
  },
  durationText: {
    position: 'absolute',
    bottom: AppTheme.spacing.small,
    right: AppTheme.spacing.small,
    fontSize: AppTheme.fontSizes.small,
    fontFamily: AppTheme.fonts.medium,
    color: AppTheme.colors.white,
    backgroundColor: AppTheme.colors.overlay,
    paddingHorizontal: AppTheme.spacing.xsmall,
    paddingVertical: 2,
    borderRadius: AppTheme.borderRadius.small,
  },
  selectionIndicator: {
    position: 'absolute',
    top: AppTheme.spacing.small,
    right: AppTheme.spacing.small,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: AppTheme.colors.white,
    borderWidth: 2,
    borderColor: AppTheme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedIndicator: {
    backgroundColor: AppTheme.colors.primary,
    borderColor: AppTheme.colors.primary,
  },
  checkmark: {
    color: AppTheme.colors.white,
    fontSize: AppTheme.fontSizes.small,
    fontFamily: AppTheme.fonts.bold,
  },
  mediaItemName: {
    fontSize: AppTheme.fontSizes.small,
    fontFamily: AppTheme.fonts.medium,
    color: AppTheme.colors.text,
    padding: AppTheme.spacing.small,
    paddingBottom: AppTheme.spacing.xsmall,
  },
  mediaItemSize: {
    fontSize: AppTheme.fontSizes.xsmall,
    fontFamily: AppTheme.fonts.regular,
    color: AppTheme.colors.textSecondary,
    paddingHorizontal: AppTheme.spacing.small,
    paddingBottom: AppTheme.spacing.small,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: AppTheme.spacing.xxxlarge,
  },
  emptyText: {
    fontSize: AppTheme.fontSizes.large,
    fontFamily: AppTheme.fonts.medium,
    color: AppTheme.colors.textSecondary,
    marginBottom: AppTheme.spacing.large,
    textAlign: 'center',
  },
});

export default GalleryScreen;
