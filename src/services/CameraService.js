/**
 * CameraService - Comprehensive camera functionality service
 * Handles all camera operations with advanced features and logging
 */

import { Platform, PermissionsAndroid } from 'react-native';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import RNFS from 'react-native-fs';
import LoggerService from './LoggerService';
import { LogLevel, LogCategory } from '../models/LogEntry';
import {
  CameraDeviceType,
  CameraMode,
  FlashMode,
  FocusMode,
  VideoQuality,
  PhotoQuality,
  CameraPermission,
  CameraError,
  CameraSettings,
  CameraCaptureResult,
  CameraDeviceInfo,
  CameraStatistics
} from '../types/CameraTypes';

class CameraService {
  constructor() {
    this.isInitialized = false;
    this.currentDevice = null;
    this.availableDevices = [];
    this.currentSettings = new CameraSettings();
    this.statistics = new CameraStatistics();
    this.isRecording = false;
    this.recordingStartTime = null;
    this.cameraRef = null;
    this.permissionStatus = CameraPermission.NOT_DETERMINED;
    
    // Performance tracking
    this.performanceMetrics = {
      initializationTime: 0,
      averageCaptureTime: 0,
      totalCaptures: 0,
      failedCaptures: 0
    };
  }

  /**
   * Initialize camera service
   */
  async initialize() {
    const startTime = Date.now();
    
    try {
      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Initializing camera service'
      );

      // Check and request permissions
      await this.checkPermissions();
      
      if (this.permissionStatus !== CameraPermission.GRANTED) {
        throw new Error('Camera permission not granted');
      }

      // Discover available devices (simulated for now)
      await this.discoverDevices();
      
      // Set default device
      if (this.availableDevices.length > 0) {
        this.currentDevice = this.availableDevices.find(d => d.type === CameraDeviceType.BACK) || this.availableDevices[0];
      }

      // Create camera directory
      await this.createCameraDirectory();

      this.performanceMetrics.initializationTime = Date.now() - startTime;
      this.isInitialized = true;

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Camera service initialized successfully',
        {
          initializationTime: this.performanceMetrics.initializationTime,
          availableDevices: this.availableDevices.length,
          currentDevice: this.currentDevice?.type,
          permissionStatus: this.permissionStatus
        }
      );

      return true;
    } catch (error) {
      this.performanceMetrics.initializationTime = Date.now() - startTime;
      
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Failed to initialize camera service',
        {
          error: error.message,
          initializationTime: this.performanceMetrics.initializationTime
        }
      );

      throw error;
    }
  }

  /**
   * Check and request camera permissions
   */
  async checkPermissions() {
    try {
      let cameraPermission;
      let microphonePermission;

      if (Platform.OS === 'ios') {
        cameraPermission = await request(PERMISSIONS.IOS.CAMERA);
        microphonePermission = await request(PERMISSIONS.IOS.MICROPHONE);
      } else {
        cameraPermission = await request(PERMISSIONS.ANDROID.CAMERA);
        microphonePermission = await request(PERMISSIONS.ANDROID.RECORD_AUDIO);
      }

      // Map permission results
      const mapPermissionResult = (result) => {
        switch (result) {
          case RESULTS.GRANTED: return CameraPermission.GRANTED;
          case RESULTS.DENIED: return CameraPermission.DENIED;
          case RESULTS.BLOCKED: return CameraPermission.RESTRICTED;
          default: return CameraPermission.NOT_DETERMINED;
        }
      };

      this.permissionStatus = mapPermissionResult(cameraPermission);
      const micPermissionStatus = mapPermissionResult(microphonePermission);

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Permission check completed',
        {
          cameraPermission: this.permissionStatus,
          microphonePermission: micPermissionStatus,
          platform: Platform.OS
        }
      );

      return this.permissionStatus === CameraPermission.GRANTED;
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Permission check failed',
        { error: error.message }
      );
      
      this.permissionStatus = CameraPermission.DENIED;
      return false;
    }
  }

  /**
   * Discover available camera devices
   */
  async discoverDevices() {
    try {
      // Simulated device discovery (in real implementation, use react-native-vision-camera)
      const devices = [
        new CameraDeviceInfo({
          id: 'back-camera',
          type: CameraDeviceType.BACK,
          name: 'Back Camera',
          hasFlash: true,
          hasTorch: true,
          supportedFormats: ['1920x1080', '3840x2160'],
          supportedFrameRates: [30, 60],
          minZoom: 1.0,
          maxZoom: 10.0,
          supportsHDR: true,
          supportsStabilization: true,
          isAvailable: true
        }),
        new CameraDeviceInfo({
          id: 'front-camera',
          type: CameraDeviceType.FRONT,
          name: 'Front Camera',
          hasFlash: false,
          hasTorch: false,
          supportedFormats: ['1920x1080'],
          supportedFrameRates: [30],
          minZoom: 1.0,
          maxZoom: 3.0,
          supportsHDR: false,
          supportsStabilization: false,
          isAvailable: true
        })
      ];

      this.availableDevices = devices;

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Device discovery completed',
        {
          deviceCount: devices.length,
          devices: devices.map(d => ({ id: d.id, type: d.type, name: d.name }))
        }
      );

      return devices;
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Device discovery failed',
        { error: error.message }
      );
      
      throw error;
    }
  }

  /**
   * Create camera directory for storing captures
   */
  async createCameraDirectory() {
    try {
      const cameraDir = `${RNFS.DocumentDirectoryPath}/Camera`;
      const exists = await RNFS.exists(cameraDir);
      
      if (!exists) {
        await RNFS.mkdir(cameraDir);
        LoggerService.log(
          LogLevel.INFO,
          LogCategory.STORAGE_OPERATIONS,
          'CameraService',
          'Camera directory created',
          { path: cameraDir }
        );
      }

      return cameraDir;
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.STORAGE_OPERATIONS,
        'CameraService',
        'Failed to create camera directory',
        { error: error.message }
      );
      
      throw error;
    }
  }

  /**
   * Switch camera device
   */
  async switchDevice(deviceType) {
    try {
      const device = this.availableDevices.find(d => d.type === deviceType);
      
      if (!device) {
        throw new Error(`Device type ${deviceType} not available`);
      }

      if (!device.isAvailable) {
        throw new Error(`Device ${device.name} is not available`);
      }

      const previousDevice = this.currentDevice;
      this.currentDevice = device;

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Camera device switched',
        {
          previousDevice: previousDevice?.type,
          currentDevice: device.type,
          deviceName: device.name
        }
      );

      return device;
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Failed to switch camera device',
        { error: error.message, requestedDevice: deviceType }
      );
      
      throw error;
    }
  }

  /**
   * Update camera settings
   */
  updateSettings(newSettings) {
    try {
      const previousSettings = { ...this.currentSettings };
      this.currentSettings = { ...this.currentSettings, ...newSettings };

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Camera settings updated',
        {
          previousSettings: previousSettings.toJSON(),
          newSettings: this.currentSettings.toJSON(),
          changedSettings: Object.keys(newSettings)
        }
      );

      return this.currentSettings;
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Failed to update camera settings',
        { error: error.message, newSettings }
      );
      
      throw error;
    }
  }

  /**
   * Capture photo
   */
  async capturePhoto(options = {}) {
    const startTime = Date.now();
    
    try {
      if (!this.isInitialized) {
        throw new Error('Camera service not initialized');
      }

      if (!this.currentDevice) {
        throw new Error('No camera device selected');
      }

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Starting photo capture',
        {
          device: this.currentDevice.type,
          settings: this.currentSettings.toJSON(),
          options
        }
      );

      // Simulate photo capture (in real implementation, use camera ref)
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

      const timestamp = new Date().toISOString();
      const filename = `photo_${Date.now()}.jpg`;
      const cameraDir = await this.createCameraDirectory();
      const filePath = `${cameraDir}/${filename}`;

      // Simulate capture result
      const captureResult = new CameraCaptureResult({
        uri: `file://${filePath}`,
        path: filePath,
        width: 1920,
        height: 1080,
        size: 2048000 + Math.random() * 1024000, // 2-3MB
        format: 'jpeg',
        timestamp,
        metadata: {
          device: this.currentDevice.type,
          settings: this.currentSettings.toJSON(),
          captureTime: Date.now() - startTime
        }
      });

      // Update statistics
      this.statistics.updateStats(captureResult, Date.now() - startTime, true);
      this.performanceMetrics.totalCaptures++;
      this.performanceMetrics.averageCaptureTime = 
        (this.performanceMetrics.averageCaptureTime * (this.performanceMetrics.totalCaptures - 1) + 
         (Date.now() - startTime)) / this.performanceMetrics.totalCaptures;

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Photo capture completed',
        {
          captureTime: Date.now() - startTime,
          result: captureResult.toJSON(),
          statistics: this.statistics.toJSON()
        }
      );

      return captureResult;
    } catch (error) {
      this.statistics.updateStats(null, Date.now() - startTime, false);
      this.performanceMetrics.failedCaptures++;

      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Photo capture failed',
        {
          error: error.message,
          captureTime: Date.now() - startTime,
          device: this.currentDevice?.type,
          settings: this.currentSettings.toJSON()
        }
      );

      throw error;
    }
  }

  /**
   * Start video recording
   */
  async startVideoRecording(options = {}) {
    try {
      if (!this.isInitialized) {
        throw new Error('Camera service not initialized');
      }

      if (!this.currentDevice) {
        throw new Error('No camera device selected');
      }

      if (this.isRecording) {
        throw new Error('Already recording');
      }

      this.isRecording = true;
      this.recordingStartTime = Date.now();

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Video recording started',
        {
          device: this.currentDevice.type,
          settings: this.currentSettings.toJSON(),
          options
        }
      );

      return true;
    } catch (error) {
      this.isRecording = false;
      this.recordingStartTime = null;

      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Failed to start video recording',
        { error: error.message }
      );

      throw error;
    }
  }

  /**
   * Stop video recording
   */
  async stopVideoRecording() {
    const startTime = Date.now();
    
    try {
      if (!this.isRecording) {
        throw new Error('Not currently recording');
      }

      const recordingDuration = Date.now() - this.recordingStartTime;
      this.isRecording = false;

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000));

      const timestamp = new Date().toISOString();
      const filename = `video_${Date.now()}.mp4`;
      const cameraDir = await this.createCameraDirectory();
      const filePath = `${cameraDir}/${filename}`;

      const captureResult = new CameraCaptureResult({
        uri: `file://${filePath}`,
        path: filePath,
        width: 1920,
        height: 1080,
        size: recordingDuration * 1000, // Approximate size based on duration
        duration: recordingDuration,
        format: 'mp4',
        timestamp,
        metadata: {
          device: this.currentDevice.type,
          settings: this.currentSettings.toJSON(),
          recordingDuration,
          processingTime: Date.now() - startTime
        }
      });

      // Update statistics
      this.statistics.updateStats(captureResult, Date.now() - startTime, true);
      this.performanceMetrics.totalCaptures++;

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Video recording completed',
        {
          recordingDuration,
          processingTime: Date.now() - startTime,
          result: captureResult.toJSON(),
          statistics: this.statistics.toJSON()
        }
      );

      this.recordingStartTime = null;
      return captureResult;
    } catch (error) {
      this.isRecording = false;
      this.recordingStartTime = null;
      this.statistics.updateStats(null, Date.now() - startTime, false);
      this.performanceMetrics.failedCaptures++;

      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Video recording failed',
        {
          error: error.message,
          processingTime: Date.now() - startTime
        }
      );

      throw error;
    }
  }

  /**
   * Set zoom level
   */
  async setZoom(zoomLevel) {
    try {
      if (!this.currentDevice) {
        throw new Error('No camera device selected');
      }

      const clampedZoom = Math.max(
        this.currentDevice.minZoom,
        Math.min(this.currentDevice.maxZoom, zoomLevel)
      );

      this.currentSettings.zoom = clampedZoom;

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Zoom level updated',
        {
          requestedZoom: zoomLevel,
          actualZoom: clampedZoom,
          minZoom: this.currentDevice.minZoom,
          maxZoom: this.currentDevice.maxZoom
        }
      );

      return clampedZoom;
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Failed to set zoom level',
        { error: error.message, requestedZoom: zoomLevel }
      );

      throw error;
    }
  }

  /**
   * Set flash mode
   */
  async setFlashMode(flashMode) {
    try {
      if (!this.currentDevice) {
        throw new Error('No camera device selected');
      }

      if (!this.currentDevice.hasFlash && flashMode !== FlashMode.OFF) {
        throw new Error('Flash not supported on current device');
      }

      this.currentSettings.flashMode = flashMode;

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Flash mode updated',
        {
          flashMode,
          deviceSupportsFlash: this.currentDevice.hasFlash
        }
      );

      return flashMode;
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Failed to set flash mode',
        { error: error.message, requestedFlashMode: flashMode }
      );

      throw error;
    }
  }

  /**
   * Focus at point
   */
  async focusAtPoint(x, y) {
    try {
      if (!this.currentDevice) {
        throw new Error('No camera device selected');
      }

      if (!this.currentDevice.supportsFocus) {
        throw new Error('Focus not supported on current device');
      }

      // Simulate focus operation
      await new Promise(resolve => setTimeout(resolve, 200));

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Focus completed',
        { x, y, device: this.currentDevice.type }
      );

      return { x, y, focused: true };
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Focus operation failed',
        { error: error.message, x, y }
      );

      throw error;
    }
  }

  /**
   * Get current camera status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      permissionStatus: this.permissionStatus,
      currentDevice: this.currentDevice?.toJSON(),
      availableDevices: this.availableDevices.map(d => d.toJSON()),
      currentSettings: this.currentSettings.toJSON(),
      isRecording: this.isRecording,
      recordingDuration: this.isRecording ? Date.now() - this.recordingStartTime : 0,
      statistics: this.statistics.toJSON(),
      performanceMetrics: this.performanceMetrics
    };
  }

  /**
   * Cleanup camera service
   */
  cleanup() {
    try {
      if (this.isRecording) {
        this.stopVideoRecording().catch(error => {
          LoggerService.log(
            LogLevel.WARNING,
            LogCategory.CAMERA_OPERATIONS,
            'CameraService',
            'Failed to stop recording during cleanup',
            { error: error.message }
          );
        });
      }

      this.cameraRef = null;
      this.isInitialized = false;

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Camera service cleaned up',
        {
          finalStatistics: this.statistics.toJSON(),
          performanceMetrics: this.performanceMetrics
        }
      );
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.CAMERA_OPERATIONS,
        'CameraService',
        'Error during camera service cleanup',
        { error: error.message }
      );
    }
  }
}

// Export singleton instance
export default new CameraService();
