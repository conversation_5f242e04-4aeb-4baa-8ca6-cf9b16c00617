import DeviceInfo from 'react-native-device-info';
import { Platform } from 'react-native';
import { LoggerService } from './LoggerService';

/**
 * DeviceInfoService - Comprehensive device information monitoring
 * Provides detailed device information, system stats, and hardware monitoring
 */
class DeviceInfoService {
  constructor() {
    this.deviceInfo = {};
    this.systemStats = {};
    this.isInitialized = false;
    this.updateInterval = null;
    this.logger = LoggerService.getInstance();
  }

  static getInstance() {
    if (!DeviceInfoService.instance) {
      DeviceInfoService.instance = new DeviceInfoService();
    }
    return DeviceInfoService.instance;
  }

  /**
   * Initialize device info service
   */
  async initialize() {
    try {
      this.logger.log('INFO', 'DEVICE', 'Initializing DeviceInfoService');
      
      await this.collectStaticDeviceInfo();
      await this.collectDynamicSystemInfo();
      
      // Start periodic updates for dynamic info
      this.startPeriodicUpdates();
      
      this.isInitialized = true;
      this.logger.log('INFO', 'DEVICE', 'DeviceInfoService initialized successfully');
      
      return true;
    } catch (error) {
      this.logger.log('ERROR', 'DEVICE', `Failed to initialize DeviceInfoService: ${error.message}`);
      return false;
    }
  }

  /**
   * Collect static device information (doesn't change during app lifecycle)
   */
  async collectStaticDeviceInfo() {
    try {
      const staticInfo = {
        // Basic device info
        deviceId: await DeviceInfo.getDeviceId(),
        deviceName: await DeviceInfo.getDeviceName(),
        brand: DeviceInfo.getBrand(),
        model: DeviceInfo.getModel(),
        systemName: DeviceInfo.getSystemName(),
        systemVersion: DeviceInfo.getSystemVersion(),
        
        // App info
        appName: DeviceInfo.getApplicationName(),
        bundleId: DeviceInfo.getBundleId(),
        version: DeviceInfo.getVersion(),
        buildNumber: DeviceInfo.getBuildNumber(),
        
        // Hardware info
        manufacturer: await DeviceInfo.getManufacturer(),
        hardware: await DeviceInfo.getHardware(),
        product: await DeviceInfo.getProduct(),
        tags: await DeviceInfo.getTags(),
        type: await DeviceInfo.getType(),
        
        // Display info
        screenWidth: DeviceInfo.getScreenWidth(),
        screenHeight: DeviceInfo.getScreenHeight(),
        
        // Platform specific
        platform: Platform.OS,
        platformVersion: Platform.Version,
        
        // Capabilities
        hasNotch: DeviceInfo.hasNotch(),
        hasDynamicIsland: DeviceInfo.hasDynamicIsland(),
        hasSystemFeature: await this.checkSystemFeatures(),
        
        // Security
        isPinOrFingerprintSet: await DeviceInfo.isPinOrFingerprintSet(),
        supportedAbis: await DeviceInfo.supportedAbis(),
        
        // Network
        macAddress: await DeviceInfo.getMacAddress(),
        ipAddress: await DeviceInfo.getIpAddress(),
        
        // Storage
        totalDiskCapacity: await DeviceInfo.getTotalDiskCapacity(),
        
        // Timestamps
        firstInstallTime: await DeviceInfo.getFirstInstallTime(),
        lastUpdateTime: await DeviceInfo.getLastUpdateTime(),
        installReferrer: await DeviceInfo.getInstallReferrer(),
      };

      this.deviceInfo = { ...this.deviceInfo, ...staticInfo };
      this.logger.log('INFO', 'DEVICE', `Collected static device info for ${staticInfo.deviceName}`);
      
    } catch (error) {
      this.logger.log('ERROR', 'DEVICE', `Error collecting static device info: ${error.message}`);
    }
  }

  /**
   * Collect dynamic system information (changes during app lifecycle)
   */
  async collectDynamicSystemInfo() {
    try {
      const dynamicInfo = {
        // Memory info
        totalMemory: await DeviceInfo.getTotalMemory(),
        usedMemory: await DeviceInfo.getUsedMemory(),
        availableMemory: await DeviceInfo.getAvailableMemory(),
        
        // Storage info
        freeDiskStorage: await DeviceInfo.getFreeDiskStorage(),
        
        // Battery info
        batteryLevel: await DeviceInfo.getBatteryLevel(),
        isBatteryCharging: await DeviceInfo.isBatteryCharging(),
        powerState: await DeviceInfo.getPowerState(),
        
        // Network info
        carrier: await DeviceInfo.getCarrier(),
        
        // System state
        isEmulator: await DeviceInfo.isEmulator(),
        isTablet: DeviceInfo.isTablet(),
        isLandscape: DeviceInfo.isLandscape(),
        
        // Performance metrics
        cpuCount: await this.getCpuCount(),
        maxMemory: await DeviceInfo.getMaxMemory(),
        
        // Timestamp
        lastUpdated: new Date().toISOString(),
      };

      this.systemStats = { ...this.systemStats, ...dynamicInfo };
      
      // Calculate derived metrics
      this.calculateDerivedMetrics();
      
    } catch (error) {
      this.logger.log('ERROR', 'DEVICE', `Error collecting dynamic system info: ${error.message}`);
    }
  }

  /**
   * Check system features availability
   */
  async checkSystemFeatures() {
    const features = {};
    
    try {
      // Camera features
      features.camera = await DeviceInfo.hasSystemFeature('android.hardware.camera');
      features.cameraFlash = await DeviceInfo.hasSystemFeature('android.hardware.camera.flash');
      features.cameraFront = await DeviceInfo.hasSystemFeature('android.hardware.camera.front');
      
      // Sensor features
      features.accelerometer = await DeviceInfo.hasSystemFeature('android.hardware.sensor.accelerometer');
      features.gyroscope = await DeviceInfo.hasSystemFeature('android.hardware.sensor.gyroscope');
      features.magnetometer = await DeviceInfo.hasSystemFeature('android.hardware.sensor.compass');
      
      // Connectivity features
      features.bluetooth = await DeviceInfo.hasSystemFeature('android.hardware.bluetooth');
      features.wifi = await DeviceInfo.hasSystemFeature('android.hardware.wifi');
      features.nfc = await DeviceInfo.hasSystemFeature('android.hardware.nfc');
      
      // Location features
      features.gps = await DeviceInfo.hasSystemFeature('android.hardware.location.gps');
      features.locationNetwork = await DeviceInfo.hasSystemFeature('android.hardware.location.network');
      
    } catch (error) {
      this.logger.log('WARNING', 'DEVICE', `Some system features unavailable: ${error.message}`);
    }
    
    return features;
  }

  /**
   * Get CPU count (simulated for cross-platform compatibility)
   */
  async getCpuCount() {
    try {
      // This is a simulation since react-native-device-info doesn't provide CPU count directly
      const totalMemory = await DeviceInfo.getTotalMemory();
      
      // Estimate CPU cores based on memory (rough approximation)
      if (totalMemory > 8000000000) return 8; // 8GB+ likely has 8 cores
      if (totalMemory > 6000000000) return 6; // 6GB+ likely has 6 cores
      if (totalMemory > 4000000000) return 4; // 4GB+ likely has 4 cores
      if (totalMemory > 2000000000) return 2; // 2GB+ likely has 2 cores
      return 1; // Default to 1 core
      
    } catch (error) {
      return 1; // Default fallback
    }
  }

  /**
   * Calculate derived performance metrics
   */
  calculateDerivedMetrics() {
    try {
      const { totalMemory, usedMemory, availableMemory, freeDiskStorage, totalDiskCapacity } = this.systemStats;
      
      // Memory utilization percentage
      if (totalMemory && usedMemory) {
        this.systemStats.memoryUtilization = ((usedMemory / totalMemory) * 100).toFixed(2);
      }
      
      // Available memory percentage
      if (totalMemory && availableMemory) {
        this.systemStats.availableMemoryPercentage = ((availableMemory / totalMemory) * 100).toFixed(2);
      }
      
      // Storage utilization percentage
      if (totalDiskCapacity && freeDiskStorage) {
        const usedStorage = totalDiskCapacity - freeDiskStorage;
        this.systemStats.storageUtilization = ((usedStorage / totalDiskCapacity) * 100).toFixed(2);
      }
      
      // Performance score (0-100 based on available resources)
      this.systemStats.performanceScore = this.calculatePerformanceScore();
      
    } catch (error) {
      this.logger.log('WARNING', 'DEVICE', `Error calculating derived metrics: ${error.message}`);
    }
  }

  /**
   * Calculate overall performance score
   */
  calculatePerformanceScore() {
    try {
      let score = 100;
      
      // Deduct points for high memory usage
      if (this.systemStats.memoryUtilization > 80) score -= 20;
      else if (this.systemStats.memoryUtilization > 60) score -= 10;
      
      // Deduct points for high storage usage
      if (this.systemStats.storageUtilization > 90) score -= 15;
      else if (this.systemStats.storageUtilization > 75) score -= 8;
      
      // Deduct points for low battery
      if (this.systemStats.batteryLevel < 0.2) score -= 10;
      else if (this.systemStats.batteryLevel < 0.5) score -= 5;
      
      // Bonus points for charging
      if (this.systemStats.isBatteryCharging) score += 5;
      
      return Math.max(0, Math.min(100, score));
      
    } catch (error) {
      return 50; // Default middle score
    }
  }

  /**
   * Start periodic updates for dynamic information
   */
  startPeriodicUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    
    // Update every 30 seconds
    this.updateInterval = setInterval(async () => {
      await this.collectDynamicSystemInfo();
      this.logger.log('DEBUG', 'DEVICE', 'Updated dynamic system information');
    }, 30000);
  }

  /**
   * Stop periodic updates
   */
  stopPeriodicUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      this.logger.log('INFO', 'DEVICE', 'Stopped periodic device info updates');
    }
  }

  /**
   * Get all device information
   */
  getAllDeviceInfo() {
    return {
      ...this.deviceInfo,
      ...this.systemStats,
      isInitialized: this.isInitialized,
    };
  }

  /**
   * Get specific device info category
   */
  getDeviceInfoCategory(category) {
    const allInfo = this.getAllDeviceInfo();
    
    switch (category) {
      case 'basic':
        return {
          deviceName: allInfo.deviceName,
          model: allInfo.model,
          systemName: allInfo.systemName,
          systemVersion: allInfo.systemVersion,
          platform: allInfo.platform,
        };
      
      case 'performance':
        return {
          totalMemory: allInfo.totalMemory,
          usedMemory: allInfo.usedMemory,
          memoryUtilization: allInfo.memoryUtilization,
          performanceScore: allInfo.performanceScore,
          cpuCount: allInfo.cpuCount,
        };
      
      case 'storage':
        return {
          totalDiskCapacity: allInfo.totalDiskCapacity,
          freeDiskStorage: allInfo.freeDiskStorage,
          storageUtilization: allInfo.storageUtilization,
        };
      
      case 'battery':
        return {
          batteryLevel: allInfo.batteryLevel,
          isBatteryCharging: allInfo.isBatteryCharging,
          powerState: allInfo.powerState,
        };
      
      default:
        return allInfo;
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopPeriodicUpdates();
    this.logger.log('INFO', 'DEVICE', 'DeviceInfoService cleaned up');
  }
}

export { DeviceInfoService };
