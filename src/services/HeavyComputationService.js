/**
 * HeavyComputationService - CPU and memory intensive operations
 * Provides computationally expensive operations to make the app heavier
 */

import LoggerService from './LoggerService';
import { LogLevel, LogCategory } from '../models/LogEntry';

class HeavyComputationService {
  constructor() {
    this.isProcessing = false;
    this.computationResults = new Map();
    this.largeDataSets = new Map();
    this.processingQueue = [];
    this.workers = [];
    
    // Initialize large data structures
    this.initializeLargeDataStructures();
    
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.PERFORMANCE,
      'HeavyComputationService',
      'Heavy computation service initialized'
    );
  }

  /**
   * Initialize large data structures to consume memory
   */
  initializeLargeDataStructures() {
    // Create large arrays with complex objects
    for (let i = 0; i < 10000; i++) {
      const largeObject = {
        id: i,
        data: new Array(1000).fill(0).map((_, j) => ({
          index: j,
          value: Math.random() * 1000000,
          timestamp: Date.now() + j,
          metadata: {
            category: `category_${j % 100}`,
            tags: Array.from({length: 20}, (_, k) => `tag_${k}_${j}`),
            properties: {
              prop1: `property_${j}_1`,
              prop2: `property_${j}_2`,
              prop3: `property_${j}_3`,
              prop4: new Array(50).fill(0).map((_, l) => `nested_${l}_${j}`)
            }
          }
        })),
        matrix: this.generateLargeMatrix(100, 100),
        complexCalculations: this.performComplexCalculations(i)
      };
      
      this.largeDataSets.set(`dataset_${i}`, largeObject);
    }

    LoggerService.log(
      LogLevel.INFO,
      LogCategory.PERFORMANCE,
      'HeavyComputationService',
      'Large data structures initialized',
      { dataSetCount: this.largeDataSets.size }
    );
  }

  /**
   * Generate a large matrix with random values
   */
  generateLargeMatrix(rows, cols) {
    const matrix = [];
    for (let i = 0; i < rows; i++) {
      const row = [];
      for (let j = 0; j < cols; j++) {
        row.push({
          value: Math.random() * 1000,
          computed: Math.sin(i) * Math.cos(j),
          metadata: `cell_${i}_${j}`,
          operations: this.performMatrixOperations(i, j)
        });
      }
      matrix.push(row);
    }
    return matrix;
  }

  /**
   * Perform matrix operations
   */
  performMatrixOperations(i, j) {
    const operations = [];
    for (let k = 0; k < 50; k++) {
      operations.push({
        operation: `op_${k}`,
        result: Math.pow(i + j + k, 2) * Math.sqrt(k + 1),
        timestamp: Date.now() + k,
        complexity: this.calculateComplexity(i, j, k)
      });
    }
    return operations;
  }

  /**
   * Calculate complexity metrics
   */
  calculateComplexity(i, j, k) {
    const base = i * j * k;
    const fibonacci = this.calculateFibonacci(Math.min(base % 30, 25));
    const factorial = this.calculateFactorial(Math.min(base % 10, 8));
    const prime = this.isPrime(base % 1000);
    
    return {
      fibonacci,
      factorial,
      prime,
      logarithmic: Math.log(base + 1),
      exponential: Math.exp(Math.min(base % 5, 3)),
      trigonometric: {
        sin: Math.sin(base),
        cos: Math.cos(base),
        tan: Math.tan(base)
      }
    };
  }

  /**
   * Calculate Fibonacci number (recursive for CPU intensity)
   */
  calculateFibonacci(n) {
    if (n <= 1) return n;
    return this.calculateFibonacci(n - 1) + this.calculateFibonacci(n - 2);
  }

  /**
   * Calculate factorial
   */
  calculateFactorial(n) {
    if (n <= 1) return 1;
    return n * this.calculateFactorial(n - 1);
  }

  /**
   * Check if number is prime
   */
  isPrime(n) {
    if (n <= 1) return false;
    if (n <= 3) return true;
    if (n % 2 === 0 || n % 3 === 0) return false;
    
    for (let i = 5; i * i <= n; i += 6) {
      if (n % i === 0 || n % (i + 2) === 0) return false;
    }
    return true;
  }

  /**
   * Perform complex calculations
   */
  performComplexCalculations(seed) {
    const calculations = [];
    
    for (let i = 0; i < 100; i++) {
      const base = seed * i;
      calculations.push({
        iteration: i,
        polynomial: this.calculatePolynomial(base, 5),
        trigonometric: this.calculateTrigonometric(base),
        logarithmic: this.calculateLogarithmic(base),
        statistical: this.calculateStatistics(base),
        geometric: this.calculateGeometric(base)
      });
    }
    
    return calculations;
  }

  /**
   * Calculate polynomial
   */
  calculatePolynomial(x, degree) {
    let result = 0;
    for (let i = 0; i <= degree; i++) {
      result += Math.pow(x, i) * (i + 1);
    }
    return result;
  }

  /**
   * Calculate trigonometric functions
   */
  calculateTrigonometric(x) {
    return {
      sin: Math.sin(x),
      cos: Math.cos(x),
      tan: Math.tan(x),
      asin: Math.asin(Math.sin(x)),
      acos: Math.acos(Math.cos(x)),
      atan: Math.atan(x),
      sinh: Math.sinh(x / 1000),
      cosh: Math.cosh(x / 1000),
      tanh: Math.tanh(x / 1000)
    };
  }

  /**
   * Calculate logarithmic functions
   */
  calculateLogarithmic(x) {
    const absX = Math.abs(x) + 1;
    return {
      natural: Math.log(absX),
      base10: Math.log10(absX),
      base2: Math.log2(absX),
      custom: Math.log(absX) / Math.log(5)
    };
  }

  /**
   * Calculate statistics
   */
  calculateStatistics(seed) {
    const data = Array.from({length: 1000}, (_, i) => seed + i * Math.random());
    
    const sum = data.reduce((a, b) => a + b, 0);
    const mean = sum / data.length;
    const variance = data.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / data.length;
    const stdDev = Math.sqrt(variance);
    
    return {
      sum,
      mean,
      variance,
      stdDev,
      min: Math.min(...data),
      max: Math.max(...data),
      median: this.calculateMedian(data),
      mode: this.calculateMode(data)
    };
  }

  /**
   * Calculate median
   */
  calculateMedian(data) {
    const sorted = [...data].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid];
  }

  /**
   * Calculate mode
   */
  calculateMode(data) {
    const frequency = {};
    let maxFreq = 0;
    let mode = null;
    
    data.forEach(value => {
      const rounded = Math.round(value);
      frequency[rounded] = (frequency[rounded] || 0) + 1;
      if (frequency[rounded] > maxFreq) {
        maxFreq = frequency[rounded];
        mode = rounded;
      }
    });
    
    return mode;
  }

  /**
   * Calculate geometric functions
   */
  calculateGeometric(seed) {
    const shapes = [];
    
    for (let i = 0; i < 50; i++) {
      const radius = Math.abs(seed + i);
      const side = Math.abs(seed + i * 2);
      
      shapes.push({
        circle: {
          radius,
          area: Math.PI * radius * radius,
          circumference: 2 * Math.PI * radius
        },
        square: {
          side,
          area: side * side,
          perimeter: 4 * side,
          diagonal: side * Math.sqrt(2)
        },
        triangle: {
          side,
          area: (Math.sqrt(3) / 4) * side * side,
          perimeter: 3 * side,
          height: (Math.sqrt(3) / 2) * side
        }
      });
    }
    
    return shapes;
  }

  /**
   * Process heavy computation task
   */
  async processHeavyTask(taskId, complexity = 'medium') {
    this.isProcessing = true;
    
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.PERFORMANCE,
      'HeavyComputationService',
      'Starting heavy computation task',
      { taskId, complexity }
    );

    const startTime = Date.now();
    
    try {
      const result = await this.performHeavyComputation(taskId, complexity);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.computationResults.set(taskId, {
        result,
        duration,
        timestamp: endTime,
        complexity
      });

      LoggerService.log(
        LogLevel.INFO,
        LogCategory.PERFORMANCE,
        'HeavyComputationService',
        'Heavy computation task completed',
        { taskId, duration, complexity }
      );

      return result;
    } catch (error) {
      LoggerService.log(
        LogLevel.ERROR,
        LogCategory.PERFORMANCE,
        'HeavyComputationService',
        'Heavy computation task failed',
        { taskId, error: error.message }
      );
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Perform heavy computation based on complexity
   */
  async performHeavyComputation(taskId, complexity) {
    const complexityLevels = {
      light: 1000,
      medium: 10000,
      heavy: 100000,
      extreme: 1000000
    };

    const iterations = complexityLevels[complexity] || complexityLevels.medium;
    const results = [];

    for (let i = 0; i < iterations; i++) {
      const computation = {
        iteration: i,
        fibonacci: this.calculateFibonacci(i % 20),
        factorial: this.calculateFactorial(i % 8),
        prime: this.isPrime(i),
        matrix: this.generateLargeMatrix(10, 10),
        statistics: this.calculateStatistics(i),
        polynomial: this.calculatePolynomial(i, 3)
      };

      results.push(computation);

      // Yield control periodically to prevent blocking
      if (i % 1000 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }

    return {
      taskId,
      iterations,
      results: results.slice(0, 100), // Store only first 100 for memory
      summary: this.generateComputationSummary(results)
    };
  }

  /**
   * Generate computation summary
   */
  generateComputationSummary(results) {
    return {
      totalIterations: results.length,
      averageFibonacci: results.reduce((sum, r) => sum + r.fibonacci, 0) / results.length,
      primeCount: results.filter(r => r.prime).length,
      memoryUsage: this.estimateMemoryUsage(),
      processingTime: Date.now()
    };
  }

  /**
   * Estimate memory usage
   */
  estimateMemoryUsage() {
    const dataSetSize = this.largeDataSets.size;
    const resultSize = this.computationResults.size;
    
    return {
      dataSets: dataSetSize,
      results: resultSize,
      estimatedMB: (dataSetSize * 0.5) + (resultSize * 0.1)
    };
  }

  /**
   * Clear computation results to free memory
   */
  clearResults() {
    this.computationResults.clear();
    
    LoggerService.log(
      LogLevel.INFO,
      LogCategory.PERFORMANCE,
      'HeavyComputationService',
      'Computation results cleared'
    );
  }

  /**
   * Get computation statistics
   */
  getStatistics() {
    return {
      isProcessing: this.isProcessing,
      totalDataSets: this.largeDataSets.size,
      totalResults: this.computationResults.size,
      memoryUsage: this.estimateMemoryUsage(),
      queueLength: this.processingQueue.length
    };
  }
}

export default new HeavyComputationService();
