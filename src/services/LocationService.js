import Geolocation from '@react-native-community/geolocation';
import { PermissionsAndroid, Platform } from 'react-native';
import { LoggerService } from './LoggerService';

/**
 * LocationService - Comprehensive location tracking and geofencing
 * Provides location monitoring, tracking, and location-based features
 */
class LocationService {
  constructor() {
    this.currentLocation = null;
    this.locationHistory = [];
    this.watchId = null;
    this.isTracking = false;
    this.isInitialized = false;
    this.logger = LoggerService.getInstance();
    this.listeners = new Set();
    
    // Location settings
    this.settings = {
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 10000,
      distanceFilter: 10, // meters
    };
    
    // Tracking statistics
    this.trackingStats = {
      totalLocations: 0,
      trackingDuration: 0,
      distanceTraveled: 0,
      averageAccuracy: 0,
      lastUpdate: null,
      startTime: null,
    };
    
    // Geofences (for future implementation)
    this.geofences = [];
  }

  static getInstance() {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  /**
   * Initialize location service
   */
  async initialize() {
    try {
      this.logger.log('INFO', 'LOCATION', 'Initializing LocationService');
      
      // Request location permissions
      const hasPermission = await this.requestLocationPermission();
      if (!hasPermission) {
        throw new Error('Location permission denied');
      }
      
      // Get initial location
      await this.getCurrentLocation();
      
      this.isInitialized = true;
      this.logger.log('INFO', 'LOCATION', 'LocationService initialized successfully');
      
      return true;
    } catch (error) {
      this.logger.log('ERROR', 'LOCATION', `Failed to initialize LocationService: ${error.message}`);
      return false;
    }
  }

  /**
   * Request location permissions
   */
  async requestLocationPermission() {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        ]);
        
        return (
          granted[PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED ||
          granted[PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION] === PermissionsAndroid.RESULTS.GRANTED
        );
      }
      
      // iOS permissions are handled automatically by the system
      return true;
    } catch (error) {
      this.logger.log('ERROR', 'LOCATION', `Error requesting location permission: ${error.message}`);
      return false;
    }
  }

  /**
   * Get current location
   */
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const location = this.processLocationData(position);
          this.currentLocation = location;
          this.addToLocationHistory(location);
          this.notifyListeners(location);
          
          this.logger.log('INFO', 'LOCATION', `Current location: ${location.latitude}, ${location.longitude}`);
          resolve(location);
        },
        (error) => {
          this.logger.log('ERROR', 'LOCATION', `Error getting current location: ${error.message}`);
          reject(error);
        },
        this.settings
      );
    });
  }

  /**
   * Start location tracking
   */
  startTracking() {
    if (this.isTracking) {
      this.logger.log('WARNING', 'LOCATION', 'Location tracking already started');
      return;
    }
    
    try {
      this.watchId = Geolocation.watchPosition(
        (position) => {
          const location = this.processLocationData(position);
          this.updateLocation(location);
        },
        (error) => {
          this.logger.log('ERROR', 'LOCATION', `Location tracking error: ${error.message}`);
        },
        this.settings
      );
      
      this.isTracking = true;
      this.trackingStats.startTime = new Date().toISOString();
      
      this.logger.log('INFO', 'LOCATION', 'Started location tracking');
    } catch (error) {
      this.logger.log('ERROR', 'LOCATION', `Failed to start location tracking: ${error.message}`);
    }
  }

  /**
   * Stop location tracking
   */
  stopTracking() {
    if (!this.isTracking) {
      this.logger.log('WARNING', 'LOCATION', 'Location tracking not started');
      return;
    }
    
    try {
      if (this.watchId !== null) {
        Geolocation.clearWatch(this.watchId);
        this.watchId = null;
      }
      
      this.isTracking = false;
      
      // Update tracking duration
      if (this.trackingStats.startTime) {
        const endTime = new Date();
        const startTime = new Date(this.trackingStats.startTime);
        this.trackingStats.trackingDuration += (endTime - startTime) / 1000; // seconds
      }
      
      this.logger.log('INFO', 'LOCATION', 'Stopped location tracking');
    } catch (error) {
      this.logger.log('ERROR', 'LOCATION', `Failed to stop location tracking: ${error.message}`);
    }
  }

  /**
   * Process raw location data
   */
  processLocationData(position) {
    const { coords, timestamp } = position;
    
    return {
      latitude: coords.latitude,
      longitude: coords.longitude,
      altitude: coords.altitude,
      accuracy: coords.accuracy,
      altitudeAccuracy: coords.altitudeAccuracy,
      heading: coords.heading,
      speed: coords.speed,
      timestamp: new Date(timestamp).toISOString(),
      id: Date.now(),
    };
  }

  /**
   * Update location and statistics
   */
  updateLocation(location) {
    const previousLocation = this.currentLocation;
    this.currentLocation = location;
    
    // Update statistics
    this.updateTrackingStats(location, previousLocation);
    
    // Add to history
    this.addToLocationHistory(location);
    
    // Check geofences
    this.checkGeofences(location);
    
    // Notify listeners
    this.notifyListeners(location);
    
    this.logger.log('DEBUG', 'LOCATION', `Location updated: ${location.latitude}, ${location.longitude}`);
  }

  /**
   * Update tracking statistics
   */
  updateTrackingStats(currentLocation, previousLocation) {
    this.trackingStats.totalLocations++;
    this.trackingStats.lastUpdate = currentLocation.timestamp;
    
    // Calculate average accuracy
    const totalAccuracy = this.trackingStats.averageAccuracy * (this.trackingStats.totalLocations - 1) + currentLocation.accuracy;
    this.trackingStats.averageAccuracy = totalAccuracy / this.trackingStats.totalLocations;
    
    // Calculate distance traveled
    if (previousLocation) {
      const distance = this.calculateDistance(
        previousLocation.latitude,
        previousLocation.longitude,
        currentLocation.latitude,
        currentLocation.longitude
      );
      this.trackingStats.distanceTraveled += distance;
    }
  }

  /**
   * Calculate distance between two coordinates (Haversine formula)
   */
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371000; // Earth's radius in meters
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in meters
  }

  /**
   * Convert degrees to radians
   */
  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  /**
   * Add location to history
   */
  addToLocationHistory(location) {
    this.locationHistory.push(location);
    
    // Keep only last 1000 locations
    if (this.locationHistory.length > 1000) {
      this.locationHistory = this.locationHistory.slice(-1000);
    }
  }

  /**
   * Check geofences (placeholder for future implementation)
   */
  checkGeofences(location) {
    // Future implementation for geofencing
    // This would check if the current location enters or exits any defined geofences
  }

  /**
   * Add geofence
   */
  addGeofence(geofence) {
    const newGeofence = {
      id: Date.now(),
      ...geofence,
      created: new Date().toISOString(),
    };
    
    this.geofences.push(newGeofence);
    this.logger.log('INFO', 'LOCATION', `Added geofence: ${newGeofence.name || newGeofence.id}`);
    
    return newGeofence.id;
  }

  /**
   * Remove geofence
   */
  removeGeofence(geofenceId) {
    const index = this.geofences.findIndex(g => g.id === geofenceId);
    if (index !== -1) {
      const removed = this.geofences.splice(index, 1)[0];
      this.logger.log('INFO', 'LOCATION', `Removed geofence: ${removed.name || removed.id}`);
      return true;
    }
    return false;
  }

  /**
   * Get location statistics
   */
  getLocationStatistics() {
    return {
      ...this.trackingStats,
      currentLocation: this.currentLocation,
      totalGeofences: this.geofences.length,
      historySize: this.locationHistory.length,
      isTracking: this.isTracking,
    };
  }

  /**
   * Get recent location history
   */
  getRecentHistory(count = 10) {
    return this.locationHistory.slice(-count);
  }

  /**
   * Get location path for mapping
   */
  getLocationPath() {
    return this.locationHistory.map(location => ({
      latitude: location.latitude,
      longitude: location.longitude,
      timestamp: location.timestamp,
    }));
  }

  /**
   * Add location listener
   */
  addListener(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of location updates
   */
  notifyListeners(location) {
    this.listeners.forEach(listener => {
      try {
        listener(location);
      } catch (error) {
        this.logger.log('ERROR', 'LOCATION', `Error notifying location listener: ${error.message}`);
      }
    });
  }

  /**
   * Update location settings
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    
    // Restart tracking if currently tracking to apply new settings
    if (this.isTracking) {
      this.stopTracking();
      setTimeout(() => this.startTracking(), 1000);
    }
    
    this.logger.log('INFO', 'LOCATION', 'Location settings updated');
  }

  /**
   * Get current settings
   */
  getSettings() {
    return { ...this.settings };
  }

  /**
   * Clear location history
   */
  clearHistory() {
    this.locationHistory = [];
    this.trackingStats = {
      totalLocations: 0,
      trackingDuration: 0,
      distanceTraveled: 0,
      averageAccuracy: 0,
      lastUpdate: null,
      startTime: null,
    };
    
    this.logger.log('INFO', 'LOCATION', 'Location history cleared');
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopTracking();
    this.listeners.clear();
    this.logger.log('INFO', 'LOCATION', 'LocationService cleaned up');
  }
}

export { LocationService };
