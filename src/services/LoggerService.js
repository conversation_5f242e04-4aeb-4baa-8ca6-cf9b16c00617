/**
 * LoggerService - Comprehensive logging system with persistence and performance tracking
 * Singleton service for managing application logs
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';
import { AppState, Platform } from 'react-native';
import LogEntry, { LogLevel, LogCategory } from '../models/LogEntry';

class LoggerService {
  constructor() {
    this.logs = [];
    this.maxLogs = 10000; // Maximum number of logs to keep in memory
    this.sessionId = this.generateSessionId();
    this.userId = null;
    this.deviceInfo = null;
    this.isInitialized = false;
    this.logListeners = [];
    this.performanceMetrics = {
      startTime: Date.now(),
      memoryWarnings: 0,
      crashCount: 0,
      totalLogs: 0
    };
    
    // Performance monitoring
    this.performanceInterval = null;
    this.memoryUsageHistory = [];
    this.fpsHistory = [];
    
    // App state monitoring
    this.appStateSubscription = null;
    this.currentAppState = AppState.currentState;
  }

  /**
   * Initialize the logger service
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Get device information
      this.deviceInfo = await this.collectDeviceInfo();
      
      // Load persisted logs
      await this.loadPersistedLogs();
      
      // Start app state monitoring
      this.startAppStateMonitoring();
      
      // Start performance monitoring
      this.startPerformanceMonitoring();
      
      // Log initialization
      this.log(LogLevel.LIFECYCLE, LogCategory.APP_LIFECYCLE, 'LoggerService', 
        'Logger service initialized', {
          sessionId: this.sessionId,
          deviceInfo: this.deviceInfo,
          platform: Platform.OS
        });

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize LoggerService:', error);
    }
  }

  /**
   * Generate unique session ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Collect device information
   */
  async collectDeviceInfo() {
    try {
      const [
        brand,
        model,
        systemVersion,
        buildNumber,
        bundleId,
        version,
        readableVersion,
        deviceId,
        isEmulator,
        totalMemory,
        usedMemory
      ] = await Promise.all([
        DeviceInfo.getBrand(),
        DeviceInfo.getModel(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.getBuildNumber(),
        DeviceInfo.getBundleId(),
        DeviceInfo.getVersion(),
        DeviceInfo.getReadableVersion(),
        DeviceInfo.getDeviceId(),
        DeviceInfo.isEmulator(),
        DeviceInfo.getTotalMemory(),
        DeviceInfo.getUsedMemory()
      ]);

      return {
        brand,
        model,
        systemVersion,
        buildNumber,
        bundleId,
        version,
        readableVersion,
        deviceId,
        isEmulator,
        totalMemory,
        usedMemory,
        platform: Platform.OS,
        platformVersion: Platform.Version
      };
    } catch (error) {
      console.error('Error collecting device info:', error);
      return {
        platform: Platform.OS,
        platformVersion: Platform.Version,
        error: error.message
      };
    }
  }

  /**
   * Start app state monitoring
   */
  startAppStateMonitoring() {
    this.appStateSubscription = AppState.addEventListener('change', (nextAppState) => {
      const previousState = this.currentAppState;
      this.currentAppState = nextAppState;

      this.log(LogLevel.LIFECYCLE, LogCategory.APP_LIFECYCLE, 'AppStateMonitor',
        `App state changed: ${previousState} -> ${nextAppState}`, {
          previousState,
          currentState: nextAppState,
          timestamp: new Date().toISOString()
        });

      // Log specific state transitions
      if (previousState === 'background' && nextAppState === 'active') {
        this.log(LogLevel.INFO, LogCategory.APP_LIFECYCLE, 'AppStateMonitor',
          'App returned to foreground');
      } else if (previousState === 'active' && nextAppState === 'background') {
        this.log(LogLevel.INFO, LogCategory.APP_LIFECYCLE, 'AppStateMonitor',
          'App moved to background');
      }
    });
  }

  /**
   * Start performance monitoring
   */
  startPerformanceMonitoring() {
    this.performanceInterval = setInterval(async () => {
      try {
        const memoryInfo = await this.getMemoryInfo();
        const performanceInfo = this.getPerformanceInfo();
        
        this.memoryUsageHistory.push({
          timestamp: Date.now(),
          ...memoryInfo
        });

        // Keep only last 100 entries
        if (this.memoryUsageHistory.length > 100) {
          this.memoryUsageHistory = this.memoryUsageHistory.slice(-100);
        }

        // Log performance metrics every 30 seconds
        if (this.memoryUsageHistory.length % 6 === 0) {
          this.log(LogLevel.PERFORMANCE, LogCategory.PERFORMANCE_METRICS, 'PerformanceMonitor',
            'Performance metrics update', {
              memory: memoryInfo,
              performance: performanceInfo,
              sessionDuration: Date.now() - this.performanceMetrics.startTime
            });
        }

        // Check for memory warnings
        if (memoryInfo.usedMemory / memoryInfo.totalMemory > 0.8) {
          this.performanceMetrics.memoryWarnings++;
          this.log(LogLevel.WARNING, LogCategory.MEMORY_USAGE, 'PerformanceMonitor',
            'High memory usage detected', memoryInfo);
        }

      } catch (error) {
        this.log(LogLevel.ERROR, LogCategory.PERFORMANCE_METRICS, 'PerformanceMonitor',
          'Error collecting performance metrics', { error: error.message });
      }
    }, 5000); // Every 5 seconds
  }

  /**
   * Get memory information
   */
  async getMemoryInfo() {
    try {
      const [totalMemory, usedMemory] = await Promise.all([
        DeviceInfo.getTotalMemory(),
        DeviceInfo.getUsedMemory()
      ]);

      return {
        totalMemory,
        usedMemory,
        freeMemory: totalMemory - usedMemory,
        memoryUsagePercentage: (usedMemory / totalMemory) * 100
      };
    } catch (error) {
      return {
        error: error.message,
        totalMemory: 0,
        usedMemory: 0,
        freeMemory: 0,
        memoryUsagePercentage: 0
      };
    }
  }

  /**
   * Get performance information
   */
  getPerformanceInfo() {
    return {
      sessionDuration: Date.now() - this.performanceMetrics.startTime,
      totalLogs: this.performanceMetrics.totalLogs,
      memoryWarnings: this.performanceMetrics.memoryWarnings,
      crashCount: this.performanceMetrics.crashCount,
      logsInMemory: this.logs.length
    };
  }

  /**
   * Main logging method
   */
  log(level, category, component, message, data = null, performanceMetrics = null) {
    try {
      const logEntry = new LogEntry({
        level,
        category,
        component,
        message,
        data,
        performanceMetrics,
        userId: this.userId,
        sessionId: this.sessionId,
        deviceInfo: this.deviceInfo
      });

      // Add to memory
      this.logs.push(logEntry);
      this.performanceMetrics.totalLogs++;

      // Maintain max logs limit
      if (this.logs.length > this.maxLogs) {
        this.logs = this.logs.slice(-this.maxLogs);
      }

      // Console output for development
      if (__DEV__) {
        const consoleMethod = this.getConsoleMethod(level);
        consoleMethod(`[${level}] ${component}: ${message}`, data);
      }

      // Notify listeners
      this.notifyListeners(logEntry);

      // Persist critical logs immediately
      if (level === LogLevel.ERROR || level === LogLevel.WARNING) {
        this.persistLogs();
      }

      return logEntry;
    } catch (error) {
      console.error('Error in LoggerService.log:', error);
    }
  }

  /**
   * Get appropriate console method for log level
   */
  getConsoleMethod(level) {
    switch (level) {
      case LogLevel.ERROR: return console.error;
      case LogLevel.WARNING: return console.warn;
      case LogLevel.DEBUG: return console.debug;
      default: return console.log;
    }
  }

  /**
   * Convenience methods for different log levels
   */
  debug(component, message, data) {
    return this.log(LogLevel.DEBUG, LogCategory.APP_LIFECYCLE, component, message, data);
  }

  info(component, message, data) {
    return this.log(LogLevel.INFO, LogCategory.APP_LIFECYCLE, component, message, data);
  }

  warn(component, message, data) {
    return this.log(LogLevel.WARNING, LogCategory.ERROR_HANDLING, component, message, data);
  }

  error(component, message, error) {
    const errorData = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack
    } : error;

    return this.log(LogLevel.ERROR, LogCategory.ERROR_HANDLING, component, message, errorData);
  }

  performance(component, operation, metrics) {
    return this.log(LogLevel.PERFORMANCE, LogCategory.PERFORMANCE_METRICS, component, 
      `Performance: ${operation}`, null, metrics);
  }

  camera(operation, result, error = null) {
    const level = error ? LogLevel.ERROR : LogLevel.CAMERA;
    const message = `Camera: ${operation}${error ? ' failed' : ' completed'}`;
    const data = { operation, result, error: error?.message };
    
    return this.log(level, LogCategory.CAMERA_OPERATIONS, 'CameraService', message, data);
  }

  /**
   * Add log listener
   */
  addListener(listener) {
    this.logListeners.push(listener);
    return () => {
      const index = this.logListeners.indexOf(listener);
      if (index > -1) {
        this.logListeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners of new log entry
   */
  notifyListeners(logEntry) {
    this.logListeners.forEach(listener => {
      try {
        listener(logEntry);
      } catch (error) {
        console.error('Error in log listener:', error);
      }
    });
  }

  /**
   * Get logs with optional filtering
   */
  getLogs(filter = null, limit = null) {
    let filteredLogs = filter ? 
      this.logs.filter(log => log.matchesFilter(filter)) : 
      this.logs;

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }

    return filteredLogs;
  }

  /**
   * Clear all logs
   */
  clearLogs() {
    this.logs = [];
    this.log(LogLevel.INFO, LogCategory.APP_LIFECYCLE, 'LoggerService', 'Logs cleared');
  }

  /**
   * Persist logs to storage
   */
  async persistLogs() {
    try {
      const logsToStore = this.logs.slice(-1000); // Store last 1000 logs
      const serializedLogs = JSON.stringify(logsToStore.map(log => log.toJSON()));
      await AsyncStorage.setItem('app_logs', serializedLogs);
      await AsyncStorage.setItem('app_logs_metadata', JSON.stringify({
        sessionId: this.sessionId,
        lastPersisted: new Date().toISOString(),
        logCount: logsToStore.length
      }));
    } catch (error) {
      console.error('Error persisting logs:', error);
    }
  }

  /**
   * Load persisted logs
   */
  async loadPersistedLogs() {
    try {
      const serializedLogs = await AsyncStorage.getItem('app_logs');
      if (serializedLogs) {
        const logData = JSON.parse(serializedLogs);
        this.logs = logData.map(data => LogEntry.fromJSON(data));
        
        this.log(LogLevel.INFO, LogCategory.STORAGE_OPERATIONS, 'LoggerService',
          `Loaded ${this.logs.length} persisted logs`);
      }
    } catch (error) {
      console.error('Error loading persisted logs:', error);
    }
  }

  /**
   * Export logs for sharing or debugging
   */
  async exportLogs(format = 'json') {
    try {
      const logs = this.getLogs();
      const exportData = {
        sessionId: this.sessionId,
        deviceInfo: this.deviceInfo,
        exportTimestamp: new Date().toISOString(),
        logCount: logs.length,
        logs: logs.map(log => log.toJSON())
      };

      if (format === 'json') {
        return JSON.stringify(exportData, null, 2);
      } else if (format === 'csv') {
        return this.convertLogsToCSV(logs);
      }

      return exportData;
    } catch (error) {
      this.error('LoggerService', 'Error exporting logs', error);
      throw error;
    }
  }

  /**
   * Convert logs to CSV format
   */
  convertLogsToCSV(logs) {
    const headers = ['Timestamp', 'Level', 'Category', 'Component', 'Message', 'Data'];
    const csvRows = [headers.join(',')];

    logs.forEach(log => {
      const row = [
        log.timestamp,
        log.level,
        log.category,
        log.component,
        `"${log.message.replace(/"/g, '""')}"`,
        `"${JSON.stringify(log.data || {}).replace(/"/g, '""')}"`
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  /**
   * Get logger statistics
   */
  getStatistics() {
    const logsByLevel = {};
    const logsByCategory = {};
    const logsByComponent = {};

    this.logs.forEach(log => {
      logsByLevel[log.level] = (logsByLevel[log.level] || 0) + 1;
      logsByCategory[log.category] = (logsByCategory[log.category] || 0) + 1;
      logsByComponent[log.component] = (logsByComponent[log.component] || 0) + 1;
    });

    return {
      totalLogs: this.logs.length,
      sessionId: this.sessionId,
      sessionDuration: Date.now() - this.performanceMetrics.startTime,
      logsByLevel,
      logsByCategory,
      logsByComponent,
      performanceMetrics: this.performanceMetrics,
      memoryUsage: this.memoryUsageHistory.slice(-10) // Last 10 memory readings
    };
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    if (this.performanceInterval) {
      clearInterval(this.performanceInterval);
      this.performanceInterval = null;
    }

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    this.persistLogs();
    this.logListeners = [];
    
    this.log(LogLevel.LIFECYCLE, LogCategory.APP_LIFECYCLE, 'LoggerService', 
      'Logger service cleaned up');
  }
}

// Export singleton instance
export default new LoggerService();
