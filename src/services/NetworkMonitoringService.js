import NetInfo from '@react-native-community/netinfo';
import { LoggerService } from './LoggerService';

/**
 * NetworkMonitoringService - Comprehensive network monitoring and analytics
 * Tracks connection status, speed, quality, and usage patterns
 */
class NetworkMonitoringService {
  constructor() {
    this.networkState = {
      isConnected: false,
      type: 'unknown',
      isInternetReachable: false,
      details: {},
    };
    this.connectionHistory = [];
    this.speedTests = [];
    this.unsubscribe = null;
    this.isInitialized = false;
    this.logger = LoggerService.getInstance();
    this.listeners = new Set();
    
    // Network quality metrics
    this.qualityMetrics = {
      latency: 0,
      downloadSpeed: 0,
      uploadSpeed: 0,
      packetLoss: 0,
      jitter: 0,
      quality: 'unknown', // poor, fair, good, excellent
    };
    
    // Usage statistics
    this.usageStats = {
      totalConnections: 0,
      connectionDuration: 0,
      dataTransferred: 0,
      connectionTypes: {},
      disconnectionCount: 0,
      lastSpeedTest: null,
    };
  }

  static getInstance() {
    if (!NetworkMonitoringService.instance) {
      NetworkMonitoringService.instance = new NetworkMonitoringService();
    }
    return NetworkMonitoringService.instance;
  }

  /**
   * Initialize network monitoring service
   */
  async initialize() {
    try {
      this.logger.log('INFO', 'NETWORK', 'Initializing NetworkMonitoringService');
      
      // Get initial network state
      const initialState = await NetInfo.fetch();
      this.updateNetworkState(initialState);
      
      // Subscribe to network state changes
      this.unsubscribe = NetInfo.addEventListener(state => {
        this.updateNetworkState(state);
      });
      
      // Start periodic network quality tests
      this.startPeriodicQualityTests();
      
      this.isInitialized = true;
      this.logger.log('INFO', 'NETWORK', 'NetworkMonitoringService initialized successfully');
      
      return true;
    } catch (error) {
      this.logger.log('ERROR', 'NETWORK', `Failed to initialize NetworkMonitoringService: ${error.message}`);
      return false;
    }
  }

  /**
   * Update network state and notify listeners
   */
  updateNetworkState(state) {
    const previousState = { ...this.networkState };
    
    this.networkState = {
      isConnected: state.isConnected,
      type: state.type,
      isInternetReachable: state.isInternetReachable,
      details: state.details || {},
      timestamp: new Date().toISOString(),
    };
    
    // Update usage statistics
    this.updateUsageStats(previousState, this.networkState);
    
    // Add to connection history
    this.addToConnectionHistory(this.networkState);
    
    // Log network change
    this.logNetworkChange(previousState, this.networkState);
    
    // Notify listeners
    this.notifyListeners(this.networkState);
    
    // Trigger speed test on connection change
    if (this.networkState.isConnected && !previousState.isConnected) {
      setTimeout(() => this.performSpeedTest(), 2000); // Wait 2 seconds after connection
    }
  }

  /**
   * Update usage statistics
   */
  updateUsageStats(previousState, currentState) {
    // Count total connections
    if (currentState.isConnected && !previousState.isConnected) {
      this.usageStats.totalConnections++;
    }
    
    // Count disconnections
    if (!currentState.isConnected && previousState.isConnected) {
      this.usageStats.disconnectionCount++;
    }
    
    // Track connection types
    if (currentState.isConnected) {
      const type = currentState.type;
      this.usageStats.connectionTypes[type] = (this.usageStats.connectionTypes[type] || 0) + 1;
    }
  }

  /**
   * Add network state to connection history
   */
  addToConnectionHistory(state) {
    this.connectionHistory.push({
      ...state,
      id: Date.now(),
    });
    
    // Keep only last 100 entries
    if (this.connectionHistory.length > 100) {
      this.connectionHistory = this.connectionHistory.slice(-100);
    }
  }

  /**
   * Log network state changes
   */
  logNetworkChange(previousState, currentState) {
    if (previousState.isConnected !== currentState.isConnected) {
      const status = currentState.isConnected ? 'Connected' : 'Disconnected';
      const type = currentState.type || 'unknown';
      this.logger.log('INFO', 'NETWORK', `Network ${status} - Type: ${type}`);
    }
    
    if (previousState.type !== currentState.type && currentState.isConnected) {
      this.logger.log('INFO', 'NETWORK', `Network type changed: ${previousState.type} → ${currentState.type}`);
    }
  }

  /**
   * Perform network speed test
   */
  async performSpeedTest() {
    if (!this.networkState.isConnected) {
      this.logger.log('WARNING', 'NETWORK', 'Cannot perform speed test - no network connection');
      return null;
    }
    
    try {
      this.logger.log('INFO', 'NETWORK', 'Starting network speed test');
      
      const startTime = Date.now();
      
      // Simulate speed test with actual network request
      const testResult = await this.simulateSpeedTest();
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const speedTestResult = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        duration,
        downloadSpeed: testResult.downloadSpeed,
        uploadSpeed: testResult.uploadSpeed,
        latency: testResult.latency,
        jitter: testResult.jitter,
        packetLoss: testResult.packetLoss,
        quality: this.calculateNetworkQuality(testResult),
        networkType: this.networkState.type,
      };
      
      // Update quality metrics
      this.qualityMetrics = {
        ...testResult,
        quality: speedTestResult.quality,
      };
      
      // Add to speed test history
      this.speedTests.push(speedTestResult);
      if (this.speedTests.length > 50) {
        this.speedTests = this.speedTests.slice(-50);
      }
      
      // Update usage stats
      this.usageStats.lastSpeedTest = speedTestResult.timestamp;
      
      this.logger.log('INFO', 'NETWORK', `Speed test completed - Quality: ${speedTestResult.quality}, Download: ${speedTestResult.downloadSpeed}Mbps`);
      
      return speedTestResult;
      
    } catch (error) {
      this.logger.log('ERROR', 'NETWORK', `Speed test failed: ${error.message}`);
      return null;
    }
  }

  /**
   * Simulate speed test (replace with actual implementation)
   */
  async simulateSpeedTest() {
    // Simulate network test with random but realistic values based on connection type
    const baseSpeed = this.getBaseSpeedForConnectionType(this.networkState.type);
    const variation = 0.3; // 30% variation
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const downloadSpeed = baseSpeed.download * (1 + (Math.random() - 0.5) * variation);
        const uploadSpeed = baseSpeed.upload * (1 + (Math.random() - 0.5) * variation);
        const latency = baseSpeed.latency * (1 + (Math.random() - 0.5) * 0.5);
        
        resolve({
          downloadSpeed: Math.round(downloadSpeed * 100) / 100,
          uploadSpeed: Math.round(uploadSpeed * 100) / 100,
          latency: Math.round(latency),
          jitter: Math.round(latency * 0.1 * Math.random()),
          packetLoss: Math.round(Math.random() * 2 * 100) / 100, // 0-2% packet loss
        });
      }, 2000 + Math.random() * 3000); // 2-5 second test duration
    });
  }

  /**
   * Get base speed values for different connection types
   */
  getBaseSpeedForConnectionType(type) {
    const speedProfiles = {
      wifi: { download: 50, upload: 25, latency: 20 },
      cellular: { download: 25, upload: 10, latency: 50 },
      '5g': { download: 100, upload: 50, latency: 15 },
      '4g': { download: 30, upload: 15, latency: 40 },
      '3g': { download: 5, upload: 2, latency: 100 },
      '2g': { download: 0.5, upload: 0.2, latency: 300 },
      ethernet: { download: 100, upload: 100, latency: 10 },
      unknown: { download: 10, upload: 5, latency: 80 },
    };
    
    return speedProfiles[type] || speedProfiles.unknown;
  }

  /**
   * Calculate network quality based on speed test results
   */
  calculateNetworkQuality(testResult) {
    const { downloadSpeed, latency, packetLoss } = testResult;
    
    let score = 100;
    
    // Download speed scoring
    if (downloadSpeed < 1) score -= 40;
    else if (downloadSpeed < 5) score -= 25;
    else if (downloadSpeed < 15) score -= 10;
    else if (downloadSpeed > 50) score += 10;
    
    // Latency scoring
    if (latency > 200) score -= 30;
    else if (latency > 100) score -= 20;
    else if (latency > 50) score -= 10;
    else if (latency < 20) score += 10;
    
    // Packet loss scoring
    if (packetLoss > 3) score -= 25;
    else if (packetLoss > 1) score -= 15;
    else if (packetLoss > 0.5) score -= 5;
    
    // Determine quality level
    if (score >= 85) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'fair';
    return 'poor';
  }

  /**
   * Start periodic network quality tests
   */
  startPeriodicQualityTests() {
    // Perform speed test every 5 minutes when connected
    this.qualityTestInterval = setInterval(async () => {
      if (this.networkState.isConnected) {
        await this.performSpeedTest();
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  /**
   * Stop periodic quality tests
   */
  stopPeriodicQualityTests() {
    if (this.qualityTestInterval) {
      clearInterval(this.qualityTestInterval);
      this.qualityTestInterval = null;
    }
  }

  /**
   * Add network state listener
   */
  addListener(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of network state changes
   */
  notifyListeners(networkState) {
    this.listeners.forEach(listener => {
      try {
        listener(networkState);
      } catch (error) {
        this.logger.log('ERROR', 'NETWORK', `Error notifying network listener: ${error.message}`);
      }
    });
  }

  /**
   * Get current network state
   */
  getNetworkState() {
    return {
      ...this.networkState,
      qualityMetrics: this.qualityMetrics,
      usageStats: this.usageStats,
    };
  }

  /**
   * Get network statistics
   */
  getNetworkStatistics() {
    return {
      usageStats: this.usageStats,
      qualityMetrics: this.qualityMetrics,
      recentSpeedTests: this.speedTests.slice(-10),
      connectionHistory: this.connectionHistory.slice(-20),
    };
  }

  /**
   * Get network quality summary
   */
  getQualitySummary() {
    const recentTests = this.speedTests.slice(-5);
    if (recentTests.length === 0) {
      return { quality: 'unknown', message: 'No recent speed tests available' };
    }
    
    const avgDownload = recentTests.reduce((sum, test) => sum + test.downloadSpeed, 0) / recentTests.length;
    const avgLatency = recentTests.reduce((sum, test) => sum + test.latency, 0) / recentTests.length;
    const avgPacketLoss = recentTests.reduce((sum, test) => sum + test.packetLoss, 0) / recentTests.length;
    
    const quality = this.calculateNetworkQuality({
      downloadSpeed: avgDownload,
      latency: avgLatency,
      packetLoss: avgPacketLoss,
    });
    
    return {
      quality,
      avgDownload: Math.round(avgDownload * 100) / 100,
      avgLatency: Math.round(avgLatency),
      avgPacketLoss: Math.round(avgPacketLoss * 100) / 100,
      message: this.getQualityMessage(quality),
    };
  }

  /**
   * Get quality message for UI display
   */
  getQualityMessage(quality) {
    const messages = {
      excellent: 'Network performance is excellent',
      good: 'Network performance is good',
      fair: 'Network performance is fair',
      poor: 'Network performance is poor',
      unknown: 'Network quality unknown',
    };
    
    return messages[quality] || messages.unknown;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    
    this.stopPeriodicQualityTests();
    this.listeners.clear();
    
    this.logger.log('INFO', 'NETWORK', 'NetworkMonitoringService cleaned up');
  }
}

export { NetworkMonitoringService };
