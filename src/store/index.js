/**
 * Redux Store Configuration
 * Configures the main Redux store with all slices and middleware
 */

import { configureStore } from '@reduxjs/toolkit';
import { 
  persistStore, 
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import reducers
import logReducer from './slices/logSlice';

// Persist configuration
const persistConfig = {
  key: 'root',
  version: 1,
  storage: AsyncStorage,
  whitelist: ['logs'], // Only persist logs slice
  blacklist: [], // Don't persist these slices
};

// Root reducer
const rootReducer = combineReducers({
  logs: logReducer,
  // Add more reducers here as needed
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Custom middleware for logging actions in development
const loggerMiddleware = (store) => (next) => (action) => {
  if (__DEV__ && action.type !== 'logs/addLog') {
    console.log('Redux Action:', action.type, action.payload);
  }
  return next(action);
};

// Performance monitoring middleware
const performanceMiddleware = (store) => (next) => (action) => {
  const start = performance.now();
  const result = next(action);
  const end = performance.now();
  
  // Log slow actions (> 10ms)
  if (end - start > 10) {
    console.warn(`Slow Redux action: ${action.type} took ${(end - start).toFixed(2)}ms`);
  }
  
  return result;
};

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        // Ignore these field paths in all actions
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        // Ignore these paths in the state
        ignoredPaths: ['logs.logs', 'logs.filteredLogs'],
      },
      immutableCheck: {
        // Ignore these paths in the state for immutability checks
        ignoredPaths: ['logs.logs', 'logs.filteredLogs'],
      },
    })
    .concat(loggerMiddleware)
    .concat(performanceMiddleware),
  devTools: __DEV__ && {
    name: 'React Native Camera App',
    trace: true,
    traceLimit: 25,
  },
});

// Create persistor
export const persistor = persistStore(store);

// Export types for TypeScript (if needed)
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Store utilities
export const getStoreState = () => store.getState();

export const getLogState = () => store.getState().logs;

// Store statistics
export const getStoreStatistics = () => {
  const state = store.getState();
  return {
    totalLogs: state.logs.logs.length,
    filteredLogs: state.logs.filteredLogs.length,
    filters: state.logs.filters,
    performance: state.logs.performance,
    statistics: state.logs.statistics,
  };
};

// Clear all persisted data
export const clearPersistedData = async () => {
  try {
    await persistor.purge();
    await AsyncStorage.clear();
    console.log('Persisted data cleared successfully');
  } catch (error) {
    console.error('Error clearing persisted data:', error);
  }
};

// Reset store to initial state
export const resetStore = () => {
  store.dispatch({ type: 'RESET_STORE' });
};

export default store;
