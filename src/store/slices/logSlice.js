/**
 * Log Redux Slice
 * Manages logging state in Redux store
 */

import { createSlice, createSelector } from '@reduxjs/toolkit';
import { LogLevel, LogCategory } from '../../models/LogEntry';

const initialState = {
  logs: [],
  filteredLogs: [],
  filters: {
    levels: [],
    categories: [],
    components: [],
    searchText: '',
    startTime: null,
    endTime: null,
    isActive: false
  },
  display: {
    maxDisplayLogs: 1000,
    autoScroll: true,
    showTimestamps: true,
    showCategories: true,
    showComponents: true,
    compactMode: false
  },
  statistics: {
    totalLogs: 0,
    logsByLevel: {},
    logsByCategory: {},
    logsByComponent: {},
    sessionStartTime: Date.now()
  },
  performance: {
    renderTime: 0,
    lastUpdateTime: 0,
    updateCount: 0
  },
  isLoading: false,
  error: null
};

const logSlice = createSlice({
  name: 'logs',
  initialState,
  reducers: {
    // Add new log entry
    addLog: (state, action) => {
      const logEntry = action.payload;
      
      // Add to logs array
      state.logs.push(logEntry);
      
      // Maintain max logs limit
      if (state.logs.length > 10000) {
        state.logs = state.logs.slice(-10000);
      }
      
      // Update statistics
      state.statistics.totalLogs = state.logs.length;
      state.statistics.logsByLevel[logEntry.level] = 
        (state.statistics.logsByLevel[logEntry.level] || 0) + 1;
      state.statistics.logsByCategory[logEntry.category] = 
        (state.statistics.logsByCategory[logEntry.category] || 0) + 1;
      state.statistics.logsByComponent[logEntry.component] = 
        (state.statistics.logsByComponent[logEntry.component] || 0) + 1;
      
      // Update performance metrics
      state.performance.lastUpdateTime = Date.now();
      state.performance.updateCount += 1;
      
      // Apply filters if active
      if (state.filters.isActive) {
        state.filteredLogs = applyFilters(state.logs, state.filters);
      } else {
        state.filteredLogs = state.logs.slice(-state.display.maxDisplayLogs);
      }
    },

    // Add multiple logs (bulk operation)
    addLogs: (state, action) => {
      const logEntries = action.payload;
      
      // Add all logs
      state.logs.push(...logEntries);
      
      // Maintain max logs limit
      if (state.logs.length > 10000) {
        state.logs = state.logs.slice(-10000);
      }
      
      // Update statistics
      state.statistics.totalLogs = state.logs.length;
      
      logEntries.forEach(logEntry => {
        state.statistics.logsByLevel[logEntry.level] = 
          (state.statistics.logsByLevel[logEntry.level] || 0) + 1;
        state.statistics.logsByCategory[logEntry.category] = 
          (state.statistics.logsByCategory[logEntry.category] || 0) + 1;
        state.statistics.logsByComponent[logEntry.component] = 
          (state.statistics.logsByComponent[logEntry.component] || 0) + 1;
      });
      
      // Update performance metrics
      state.performance.lastUpdateTime = Date.now();
      state.performance.updateCount += logEntries.length;
      
      // Apply filters if active
      if (state.filters.isActive) {
        state.filteredLogs = applyFilters(state.logs, state.filters);
      } else {
        state.filteredLogs = state.logs.slice(-state.display.maxDisplayLogs);
      }
    },

    // Clear all logs
    clearLogs: (state) => {
      state.logs = [];
      state.filteredLogs = [];
      state.statistics = {
        ...initialState.statistics,
        sessionStartTime: Date.now()
      };
      state.performance.updateCount = 0;
    },

    // Update filters
    updateFilters: (state, action) => {
      const newFilters = action.payload;
      state.filters = { ...state.filters, ...newFilters };
      
      // Apply filters
      if (state.filters.isActive) {
        state.filteredLogs = applyFilters(state.logs, state.filters);
      } else {
        state.filteredLogs = state.logs.slice(-state.display.maxDisplayLogs);
      }
    },

    // Toggle filter active state
    toggleFilters: (state) => {
      state.filters.isActive = !state.filters.isActive;
      
      if (state.filters.isActive) {
        state.filteredLogs = applyFilters(state.logs, state.filters);
      } else {
        state.filteredLogs = state.logs.slice(-state.display.maxDisplayLogs);
      }
    },

    // Reset filters
    resetFilters: (state) => {
      state.filters = {
        ...initialState.filters,
        isActive: false
      };
      state.filteredLogs = state.logs.slice(-state.display.maxDisplayLogs);
    },

    // Update display settings
    updateDisplaySettings: (state, action) => {
      const newSettings = action.payload;
      state.display = { ...state.display, ...newSettings };
      
      // Reapply display limits
      if (state.filters.isActive) {
        state.filteredLogs = applyFilters(state.logs, state.filters);
      } else {
        state.filteredLogs = state.logs.slice(-state.display.maxDisplayLogs);
      }
    },

    // Set loading state
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },

    // Set error state
    setError: (state, action) => {
      state.error = action.payload;
      state.isLoading = false;
    },

    // Update performance metrics
    updatePerformanceMetrics: (state, action) => {
      const metrics = action.payload;
      state.performance = { ...state.performance, ...metrics };
    }
  }
});

// Helper function to apply filters
function applyFilters(logs, filters) {
  let filtered = logs;

  // Level filter
  if (filters.levels.length > 0) {
    filtered = filtered.filter(log => filters.levels.includes(log.level));
  }

  // Category filter
  if (filters.categories.length > 0) {
    filtered = filtered.filter(log => filters.categories.includes(log.category));
  }

  // Component filter
  if (filters.components.length > 0) {
    filtered = filtered.filter(log => filters.components.includes(log.component));
  }

  // Search text filter
  if (filters.searchText) {
    const searchLower = filters.searchText.toLowerCase();
    filtered = filtered.filter(log => {
      const messageMatch = log.message.toLowerCase().includes(searchLower);
      const componentMatch = log.component.toLowerCase().includes(searchLower);
      const dataMatch = log.data && JSON.stringify(log.data).toLowerCase().includes(searchLower);
      return messageMatch || componentMatch || dataMatch;
    });
  }

  // Time range filter
  if (filters.startTime || filters.endTime) {
    filtered = filtered.filter(log => {
      const logTime = new Date(log.timestamp).getTime();
      if (filters.startTime && logTime < filters.startTime) return false;
      if (filters.endTime && logTime > filters.endTime) return false;
      return true;
    });
  }

  return filtered;
}

// Export actions
export const {
  addLog,
  addLogs,
  clearLogs,
  updateFilters,
  toggleFilters,
  resetFilters,
  updateDisplaySettings,
  setLoading,
  setError,
  updatePerformanceMetrics
} = logSlice.actions;

// Selectors
export const selectLogs = (state) => state.logs.logs;
export const selectFilteredLogs = (state) => state.logs.filteredLogs;
export const selectFilters = (state) => state.logs.filters;
export const selectDisplaySettings = (state) => state.logs.display;
export const selectStatistics = (state) => state.logs.statistics;
export const selectPerformanceMetrics = (state) => state.logs.performance;
export const selectIsLoading = (state) => state.logs.isLoading;
export const selectError = (state) => state.logs.error;

// Memoized selectors
export const selectLogsByLevel = createSelector(
  [selectLogs],
  (logs) => {
    const logsByLevel = {};
    logs.forEach(log => {
      logsByLevel[log.level] = (logsByLevel[log.level] || 0) + 1;
    });
    return logsByLevel;
  }
);

export const selectLogsByCategory = createSelector(
  [selectLogs],
  (logs) => {
    const logsByCategory = {};
    logs.forEach(log => {
      logsByCategory[log.category] = (logsByCategory[log.category] || 0) + 1;
    });
    return logsByCategory;
  }
);

export const selectUniqueComponents = createSelector(
  [selectLogs],
  (logs) => {
    const components = new Set();
    logs.forEach(log => components.add(log.component));
    return Array.from(components).sort();
  }
);

export const selectRecentLogs = createSelector(
  [selectFilteredLogs, selectDisplaySettings],
  (filteredLogs, displaySettings) => {
    return filteredLogs.slice(-displaySettings.maxDisplayLogs);
  }
);

export const selectLogStatistics = createSelector(
  [selectLogs, selectStatistics],
  (logs, statistics) => {
    const now = Date.now();
    const sessionDuration = now - statistics.sessionStartTime;
    
    return {
      ...statistics,
      sessionDuration,
      logsPerMinute: logs.length > 0 ? (logs.length / (sessionDuration / 60000)) : 0,
      averageLogSize: logs.length > 0 ? 
        logs.reduce((sum, log) => sum + JSON.stringify(log).length, 0) / logs.length : 0
    };
  }
);

export default logSlice.reducer;
