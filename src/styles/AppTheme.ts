/**
 * AppTheme - Comprehensive design system and theme configuration
 * Provides consistent styling across the entire application
 */

import { Platform, Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Color palette
const colors = {
  // Primary colors
  primary: '#007AFF',
  primaryDark: '#0056CC',
  primaryLight: '#4DA6FF',
  
  // Secondary colors
  secondary: '#5856D6',
  secondaryDark: '#3634A3',
  secondaryLight: '#8B8AE9',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray100: '#F8F9FA',
  gray200: '#E9ECEF',
  gray300: '#DEE2E6',
  gray400: '#CED4DA',
  gray500: '#ADB5BD',
  gray600: '#6C757D',
  gray700: '#495057',
  gray800: '#343A40',
  gray900: '#212529',
  
  // Semantic colors
  success: '#28A745',
  successDark: '#1E7E34',
  successLight: '#5CBF2A',
  
  warning: '#FFC107',
  warningDark: '#E0A800',
  warningLight: '#FFD93D',
  
  error: '#DC3545',
  errorDark: '#C82333',
  errorLight: '#E66771',
  
  info: '#17A2B8',
  infoDark: '#138496',
  infoLight: '#5DADE2',
  
  // Background colors
  background: '#FFFFFF',
  backgroundSecondary: '#F8F9FA',
  backgroundTertiary: '#E9ECEF',
  
  // Text colors
  text: '#212529',
  textSecondary: '#6C757D',
  textTertiary: '#ADB5BD',
  textInverse: '#FFFFFF',
  
  // Border colors
  border: '#DEE2E6',
  borderLight: '#E9ECEF',
  borderDark: '#CED4DA',
  
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  overlayDark: 'rgba(0, 0, 0, 0.7)',
  
  // Shadow colors
  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.1)',
  shadowMedium: 'rgba(0, 0, 0, 0.2)',
  shadowDark: 'rgba(0, 0, 0, 0.3)',
  
  // Disabled state
  disabled: '#E9ECEF',
  disabledText: '#ADB5BD',
  
  // Camera specific colors
  cameraBackground: '#000000',
  cameraOverlay: 'rgba(0, 0, 0, 0.4)',
  captureButton: '#FF3B30',
  captureButtonActive: '#FF6B60',
  
  // Status colors
  online: '#28A745',
  offline: '#DC3545',
  pending: '#FFC107',
};

// Typography
const fonts = {
  regular: Platform.select({
    ios: 'System',
    android: 'Roboto',
  }),
  medium: Platform.select({
    ios: 'System',
    android: 'Roboto-Medium',
  }),
  bold: Platform.select({
    ios: 'System',
    android: 'Roboto-Bold',
  }),
  light: Platform.select({
    ios: 'System',
    android: 'Roboto-Light',
  }),
  thin: Platform.select({
    ios: 'System',
    android: 'Roboto-Thin',
  }),
};

const fontSizes = {
  xsmall: 12,
  small: 14,
  medium: 16,
  large: 18,
  xlarge: 20,
  xxlarge: 24,
  xxxlarge: 28,
  huge: 32,
  massive: 36,
};

const lineHeights = {
  xsmall: 16,
  small: 20,
  medium: 24,
  large: 26,
  xlarge: 28,
  xxlarge: 32,
  xxxlarge: 36,
  huge: 40,
  massive: 44,
};

const fontWeights = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
  extrabold: '800' as const,
};

// Spacing
const spacing = {
  xsmall: 4,
  small: 8,
  medium: 16,
  large: 24,
  xlarge: 32,
  xxlarge: 48,
  xxxlarge: 64,
  huge: 80,
  massive: 96,
};

// Border radius
const borderRadius = {
  none: 0,
  small: 4,
  medium: 8,
  large: 12,
  xlarge: 16,
  round: 50,
  circle: 9999,
};

// Shadows
const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  small: Platform.select({
    ios: {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    android: {
      elevation: 2,
    },
  }),
  medium: Platform.select({
    ios: {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
    },
    android: {
      elevation: 4,
    },
  }),
  large: Platform.select({
    ios: {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
    },
    android: {
      elevation: 8,
    },
  }),
  xlarge: Platform.select({
    ios: {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.25,
      shadowRadius: 16,
    },
    android: {
      elevation: 16,
    },
  }),
};

// Layout
const layout = {
  screenWidth,
  screenHeight,
  isSmallScreen: screenWidth < 375,
  isMediumScreen: screenWidth >= 375 && screenWidth < 414,
  isLargeScreen: screenWidth >= 414,
  headerHeight: Platform.select({
    ios: 44,
    android: 56,
  }),
  tabBarHeight: Platform.select({
    ios: 49,
    android: 56,
  }),
  statusBarHeight: Platform.select({
    ios: 20,
    android: 24,
  }),
};

// Animation
const animation = {
  timing: {
    fast: 150,
    normal: 250,
    slow: 350,
    verySlow: 500,
  },
  easing: {
    linear: 'linear' as const,
    ease: 'ease' as const,
    easeIn: 'ease-in' as const,
    easeOut: 'ease-out' as const,
    easeInOut: 'ease-in-out' as const,
  },
};

// Z-index layers
const zIndex = {
  background: -1,
  default: 0,
  content: 1,
  header: 10,
  overlay: 100,
  modal: 1000,
  popover: 1100,
  tooltip: 1200,
  notification: 1300,
  maximum: 9999,
};

// Breakpoints
const breakpoints = {
  small: 0,
  medium: 768,
  large: 1024,
  xlarge: 1200,
};

// Component specific styles
const components = {
  button: {
    minHeight: 44, // iOS accessibility minimum
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.small,
  },
  input: {
    minHeight: 44,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.small,
    borderWidth: 1,
    borderColor: colors.border,
  },
  card: {
    borderRadius: borderRadius.large,
    backgroundColor: colors.white,
    padding: spacing.medium,
    ...shadows.medium,
  },
  modal: {
    borderRadius: borderRadius.large,
    backgroundColor: colors.white,
    padding: spacing.large,
    margin: spacing.medium,
    ...shadows.xlarge,
  },
};

// Accessibility
const accessibility = {
  minimumTouchTarget: 44,
  focusColor: colors.primary,
  focusWidth: 2,
  highContrastRatio: 4.5, // WCAG AA standard
  animationDuration: {
    respectsReduceMotion: true,
    fast: 150,
    normal: 250,
  },
};

// Theme object
export const AppTheme = {
  colors,
  fonts,
  fontSizes,
  lineHeights,
  fontWeights,
  spacing,
  borderRadius,
  shadows,
  layout,
  animation,
  zIndex,
  breakpoints,
  components,
  accessibility,
};

// Dark theme variant
export const DarkTheme = {
  ...AppTheme,
  colors: {
    ...colors,
    // Override colors for dark theme
    background: colors.gray900,
    backgroundSecondary: colors.gray800,
    backgroundTertiary: colors.gray700,
    text: colors.white,
    textSecondary: colors.gray300,
    textTertiary: colors.gray400,
    border: colors.gray600,
    borderLight: colors.gray700,
    borderDark: colors.gray500,
    disabled: colors.gray700,
    disabledText: colors.gray500,
  },
};

// High contrast theme for accessibility
export const HighContrastTheme = {
  ...AppTheme,
  colors: {
    ...colors,
    // High contrast colors
    primary: '#0000FF',
    secondary: '#800080',
    success: '#008000',
    warning: '#FF8C00',
    error: '#FF0000',
    text: '#000000',
    background: '#FFFFFF',
    border: '#000000',
  },
};

export type Theme = typeof AppTheme;
export type ThemeColors = typeof colors;
export type ThemeFonts = typeof fonts;
export type ThemeSpacing = typeof spacing;
