/**
 * Camera Types and Interfaces
 * Defines types and constants for camera functionality
 */

// Camera device types
export const CameraDeviceType = {
  BACK: 'back',
  FRONT: 'front',
  EXTERNAL: 'external'
};

// Camera modes
export const CameraMode = {
  PHOTO: 'photo',
  VIDEO: 'video',
  PORTRAIT: 'portrait',
  NIGHT: 'night',
  PANORAMA: 'panorama'
};

// Flash modes
export const FlashMode = {
  OFF: 'off',
  ON: 'on',
  AUTO: 'auto',
  TORCH: 'torch'
};

// Focus modes
export const FocusMode = {
  AUTO: 'auto',
  MANUAL: 'manual',
  CONTINUOUS: 'continuous'
};

// Video quality presets
export const VideoQuality = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  ULTRA_HIGH: 'ultra-high'
};

// Photo quality presets
export const PhotoQuality = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  ULTRA_HIGH: 'ultra-high'
};

// Camera permissions
export const CameraPermission = {
  GRANTED: 'granted',
  DENIED: 'denied',
  RESTRICTED: 'restricted',
  NOT_DETERMINED: 'not-determined'
};

// Camera errors
export const CameraError = {
  PERMISSION_DENIED: 'permission-denied',
  DEVICE_NOT_AVAILABLE: 'device-not-available',
  CAPTURE_FAILED: 'capture-failed',
  RECORDING_FAILED: 'recording-failed',
  INITIALIZATION_FAILED: 'initialization-failed',
  UNKNOWN: 'unknown'
};

// Camera settings interface
export class CameraSettings {
  constructor({
    deviceType = CameraDeviceType.BACK,
    mode = CameraMode.PHOTO,
    flashMode = FlashMode.OFF,
    focusMode = FocusMode.AUTO,
    videoQuality = VideoQuality.HIGH,
    photoQuality = PhotoQuality.HIGH,
    enableHDR = false,
    enableStabilization = true,
    enableAudio = true,
    zoom = 1.0,
    exposure = 0,
    iso = 'auto',
    whiteBalance = 'auto',
    frameRate = 30
  } = {}) {
    this.deviceType = deviceType;
    this.mode = mode;
    this.flashMode = flashMode;
    this.focusMode = focusMode;
    this.videoQuality = videoQuality;
    this.photoQuality = photoQuality;
    this.enableHDR = enableHDR;
    this.enableStabilization = enableStabilization;
    this.enableAudio = enableAudio;
    this.zoom = zoom;
    this.exposure = exposure;
    this.iso = iso;
    this.whiteBalance = whiteBalance;
    this.frameRate = frameRate;
  }

  toJSON() {
    return {
      deviceType: this.deviceType,
      mode: this.mode,
      flashMode: this.flashMode,
      focusMode: this.focusMode,
      videoQuality: this.videoQuality,
      photoQuality: this.photoQuality,
      enableHDR: this.enableHDR,
      enableStabilization: this.enableStabilization,
      enableAudio: this.enableAudio,
      zoom: this.zoom,
      exposure: this.exposure,
      iso: this.iso,
      whiteBalance: this.whiteBalance,
      frameRate: this.frameRate
    };
  }

  static fromJSON(json) {
    return new CameraSettings(json);
  }
}

// Camera capture result
export class CameraCaptureResult {
  constructor({
    uri = null,
    path = null,
    width = 0,
    height = 0,
    size = 0,
    duration = 0,
    format = null,
    timestamp = new Date().toISOString(),
    metadata = null,
    thumbnail = null
  } = {}) {
    this.uri = uri;
    this.path = path;
    this.width = width;
    this.height = height;
    this.size = size;
    this.duration = duration;
    this.format = format;
    this.timestamp = timestamp;
    this.metadata = metadata;
    this.thumbnail = thumbnail;
  }

  toJSON() {
    return {
      uri: this.uri,
      path: this.path,
      width: this.width,
      height: this.height,
      size: this.size,
      duration: this.duration,
      format: this.format,
      timestamp: this.timestamp,
      metadata: this.metadata,
      thumbnail: this.thumbnail
    };
  }

  static fromJSON(json) {
    return new CameraCaptureResult(json);
  }
}

// Camera device info
export class CameraDeviceInfo {
  constructor({
    id = null,
    type = CameraDeviceType.BACK,
    name = null,
    hasFlash = false,
    hasTorch = false,
    supportedFormats = [],
    supportedFrameRates = [],
    minZoom = 1.0,
    maxZoom = 1.0,
    supportsHDR = false,
    supportsStabilization = false,
    supportsFocus = true,
    supportsExposure = true,
    isAvailable = true
  } = {}) {
    this.id = id;
    this.type = type;
    this.name = name;
    this.hasFlash = hasFlash;
    this.hasTorch = hasTorch;
    this.supportedFormats = supportedFormats;
    this.supportedFrameRates = supportedFrameRates;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
    this.supportsHDR = supportsHDR;
    this.supportsStabilization = supportsStabilization;
    this.supportsFocus = supportsFocus;
    this.supportsExposure = supportsExposure;
    this.isAvailable = isAvailable;
  }

  toJSON() {
    return {
      id: this.id,
      type: this.type,
      name: this.name,
      hasFlash: this.hasFlash,
      hasTorch: this.hasTorch,
      supportedFormats: this.supportedFormats,
      supportedFrameRates: this.supportedFrameRates,
      minZoom: this.minZoom,
      maxZoom: this.maxZoom,
      supportsHDR: this.supportsHDR,
      supportsStabilization: this.supportsStabilization,
      supportsFocus: this.supportsFocus,
      supportsExposure: this.supportsExposure,
      isAvailable: this.isAvailable
    };
  }

  static fromJSON(json) {
    return new CameraDeviceInfo(json);
  }
}

// Camera statistics
export class CameraStatistics {
  constructor({
    totalPhotos = 0,
    totalVideos = 0,
    totalCaptureTime = 0,
    averageCaptureTime = 0,
    successfulCaptures = 0,
    failedCaptures = 0,
    totalStorageUsed = 0,
    sessionStartTime = Date.now(),
    lastCaptureTime = null
  } = {}) {
    this.totalPhotos = totalPhotos;
    this.totalVideos = totalVideos;
    this.totalCaptureTime = totalCaptureTime;
    this.averageCaptureTime = averageCaptureTime;
    this.successfulCaptures = successfulCaptures;
    this.failedCaptures = failedCaptures;
    this.totalStorageUsed = totalStorageUsed;
    this.sessionStartTime = sessionStartTime;
    this.lastCaptureTime = lastCaptureTime;
  }

  updateStats(captureResult, captureTime, success = true) {
    if (success) {
      this.successfulCaptures++;
      if (captureResult.duration > 0) {
        this.totalVideos++;
      } else {
        this.totalPhotos++;
      }
      this.totalStorageUsed += captureResult.size || 0;
    } else {
      this.failedCaptures++;
    }

    this.totalCaptureTime += captureTime;
    this.averageCaptureTime = this.totalCaptureTime / (this.successfulCaptures + this.failedCaptures);
    this.lastCaptureTime = Date.now();
  }

  getSuccessRate() {
    const total = this.successfulCaptures + this.failedCaptures;
    return total > 0 ? (this.successfulCaptures / total) * 100 : 0;
  }

  getSessionDuration() {
    return Date.now() - this.sessionStartTime;
  }

  toJSON() {
    return {
      totalPhotos: this.totalPhotos,
      totalVideos: this.totalVideos,
      totalCaptureTime: this.totalCaptureTime,
      averageCaptureTime: this.averageCaptureTime,
      successfulCaptures: this.successfulCaptures,
      failedCaptures: this.failedCaptures,
      totalStorageUsed: this.totalStorageUsed,
      sessionStartTime: this.sessionStartTime,
      lastCaptureTime: this.lastCaptureTime,
      successRate: this.getSuccessRate(),
      sessionDuration: this.getSessionDuration()
    };
  }

  static fromJSON(json) {
    return new CameraStatistics(json);
  }
}

export default {
  CameraDeviceType,
  CameraMode,
  FlashMode,
  FocusMode,
  VideoQuality,
  PhotoQuality,
  CameraPermission,
  CameraError,
  CameraSettings,
  CameraCaptureResult,
  CameraDeviceInfo,
  CameraStatistics
};
