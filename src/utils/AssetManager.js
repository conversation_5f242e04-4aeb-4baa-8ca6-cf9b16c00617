/**
 * AssetManager - Utility for managing large assets
 * Handles loading and caching of images, animations, videos, and audio files
 */

import RNFS from 'react-native-fs';
import AsyncStorage from '@react-native-async-storage/async-storage';

class AssetManager {
  constructor() {
    this.imageCache = new Map();
    this.animationCache = new Map();
    this.videoCache = new Map();
    this.audioCache = new Map();
    this.loadingPromises = new Map();
  }

  /**
   * Generate large dummy assets for testing
   */
  async generateDummyAssets() {
    try {
      // Create large dummy image data (base64 encoded)
      const dummyImages = [];
      for (let i = 0; i < 25; i++) {
        const imageData = this.generateLargeImageData(i);
        dummyImages.push({
          id: `dummy_image_${i}`,
          name: `Large Image ${i + 1}`,
          data: imageData,
          size: imageData.length,
          type: 'image/jpeg'
        });
      }

      // Create dummy animation data
      const dummyAnimations = [];
      for (let i = 0; i < 8; i++) {
        const animationData = this.generateLottieAnimationData(i);
        dummyAnimations.push({
          id: `dummy_animation_${i}`,
          name: `Animation ${i + 1}`,
          data: animationData,
          size: JSON.stringify(animationData).length,
          type: 'application/json'
        });
      }

      // Create dummy video metadata (we'll simulate large video files)
      const dummyVideos = [];
      for (let i = 0; i < 3; i++) {
        const videoData = this.generateLargeVideoData(i);
        dummyVideos.push({
          id: `dummy_video_${i}`,
          name: `Sample Video ${i + 1}`,
          data: videoData,
          size: videoData.length,
          type: 'video/mp4',
          duration: 30 + (i * 15) // 30, 45, 60 seconds
        });
      }

      // Create dummy audio data
      const dummyAudio = [];
      for (let i = 0; i < 5; i++) {
        const audioData = this.generateLargeAudioData(i);
        dummyAudio.push({
          id: `dummy_audio_${i}`,
          name: `Audio Track ${i + 1}`,
          data: audioData,
          size: audioData.length,
          type: 'audio/mp3',
          duration: 60 + (i * 30) // 60, 90, 120, 150, 180 seconds
        });
      }

      // Store in AsyncStorage to persist
      await AsyncStorage.setItem('dummy_images', JSON.stringify(dummyImages));
      await AsyncStorage.setItem('dummy_animations', JSON.stringify(dummyAnimations));
      await AsyncStorage.setItem('dummy_videos', JSON.stringify(dummyVideos));
      await AsyncStorage.setItem('dummy_audio', JSON.stringify(dummyAudio));

      return {
        images: dummyImages,
        animations: dummyAnimations,
        videos: dummyVideos,
        audio: dummyAudio,
        totalSize: this.calculateTotalSize([...dummyImages, ...dummyAnimations, ...dummyVideos, ...dummyAudio])
      };
    } catch (error) {
      console.error('Error generating dummy assets:', error);
      throw error;
    }
  }

  /**
   * Generate large image data (simulated)
   */
  generateLargeImageData(index) {
    // Generate a large base64 string to simulate a 5-10MB image
    const baseString = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
    let largeData = baseString;
    
    // Repeat the base string to create a large file (approximately 5-10MB)
    const targetSize = 5000000 + (index * 1000000); // 5MB + index MB
    while (largeData.length < targetSize) {
      largeData += baseString + Math.random().toString(36).substring(2, 15);
    }
    
    return largeData;
  }

  /**
   * Generate Lottie animation data
   */
  generateLottieAnimationData(index) {
    const baseAnimation = {
      v: "5.7.4",
      fr: 30,
      ip: 0,
      op: 90,
      w: 1920,
      h: 1080,
      nm: `Large Animation ${index + 1}`,
      ddd: 0,
      assets: [],
      layers: []
    };

    // Add many layers to make it large
    for (let i = 0; i < 100 + (index * 50); i++) {
      baseAnimation.layers.push({
        ddd: 0,
        ind: i,
        ty: 4,
        nm: `Layer ${i}`,
        sr: 1,
        ks: {
          o: { a: 0, k: 100 },
          r: { a: 0, k: 0 },
          p: { a: 0, k: [960, 540, 0] },
          a: { a: 0, k: [0, 0, 0] },
          s: { a: 0, k: [100, 100, 100] }
        },
        ao: 0,
        shapes: [
          {
            ty: "gr",
            it: [
              {
                ty: "rc",
                d: 1,
                s: { a: 0, k: [100, 100] },
                p: { a: 0, k: [0, 0] },
                r: { a: 0, k: 0 }
              },
              {
                ty: "fl",
                c: { a: 0, k: [Math.random(), Math.random(), Math.random(), 1] },
                o: { a: 0, k: 100 }
              }
            ]
          }
        ],
        ip: 0,
        op: 90,
        st: 0,
        bm: 0
      });
    }

    return baseAnimation;
  }

  /**
   * Generate large video data (simulated)
   */
  generateLargeVideoData(index) {
    // Generate a large binary-like string to simulate video data
    let videoData = '';
    const targetSize = 20000000 + (index * 15000000); // 20MB + index * 15MB
    
    while (videoData.length < targetSize) {
      videoData += Math.random().toString(36).substring(2, 15) + 
                   Math.random().toString(16).substring(2, 10) +
                   'VIDEODATA' + index + 'FRAME' + Math.floor(Math.random() * 1000);
    }
    
    return videoData;
  }

  /**
   * Generate large audio data (simulated)
   */
  generateLargeAudioData(index) {
    // Generate a large binary-like string to simulate audio data
    let audioData = '';
    const targetSize = 10000000 + (index * 5000000); // 10MB + index * 5MB
    
    while (audioData.length < targetSize) {
      audioData += Math.random().toString(36).substring(2, 15) + 
                   Math.random().toString(16).substring(2, 10) +
                   'AUDIODATA' + index + 'SAMPLE' + Math.floor(Math.random() * 1000);
    }
    
    return audioData;
  }

  /**
   * Calculate total size of assets
   */
  calculateTotalSize(assets) {
    return assets.reduce((total, asset) => total + asset.size, 0);
  }

  /**
   * Load asset with caching
   */
  async loadAsset(assetId, assetType) {
    const cacheKey = `${assetType}_${assetId}`;
    
    // Check if already loading
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // Check cache first
    const cache = this.getCacheForType(assetType);
    if (cache.has(assetId)) {
      return cache.get(assetId);
    }

    // Load asset
    const loadPromise = this.loadAssetFromStorage(assetId, assetType);
    this.loadingPromises.set(cacheKey, loadPromise);

    try {
      const asset = await loadPromise;
      cache.set(assetId, asset);
      this.loadingPromises.delete(cacheKey);
      return asset;
    } catch (error) {
      this.loadingPromises.delete(cacheKey);
      throw error;
    }
  }

  /**
   * Load asset from storage
   */
  async loadAssetFromStorage(assetId, assetType) {
    try {
      const storageKey = `dummy_${assetType}s`;
      const assetsJson = await AsyncStorage.getItem(storageKey);
      
      if (!assetsJson) {
        throw new Error(`No assets found for type: ${assetType}`);
      }

      const assets = JSON.parse(assetsJson);
      const asset = assets.find(a => a.id === assetId);
      
      if (!asset) {
        throw new Error(`Asset not found: ${assetId}`);
      }

      return asset;
    } catch (error) {
      console.error(`Error loading asset ${assetId}:`, error);
      throw error;
    }
  }

  /**
   * Get cache for asset type
   */
  getCacheForType(assetType) {
    switch (assetType) {
      case 'image': return this.imageCache;
      case 'animation': return this.animationCache;
      case 'video': return this.videoCache;
      case 'audio': return this.audioCache;
      default: throw new Error(`Unknown asset type: ${assetType}`);
    }
  }

  /**
   * Clear all caches
   */
  clearCaches() {
    this.imageCache.clear();
    this.animationCache.clear();
    this.videoCache.clear();
    this.audioCache.clear();
    this.loadingPromises.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      images: this.imageCache.size,
      animations: this.animationCache.size,
      videos: this.videoCache.size,
      audio: this.audioCache.size,
      totalCached: this.imageCache.size + this.animationCache.size + 
                   this.videoCache.size + this.audioCache.size
    };
  }
}

export default new AssetManager();
