/**
 * CameraUtils - Utility functions for camera operations
 * Helper functions for camera calculations, validations, and transformations
 */

import { Platform, Dimensions } from 'react-native';
import RNFS from 'react-native-fs';
import {
  CameraDeviceType,
  CameraMode,
  FlashMode,
  VideoQuality,
  PhotoQuality
} from '../types/CameraTypes';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export class CameraUtils {
  /**
   * Calculate aspect ratio from dimensions
   */
  static calculateAspectRatio(width, height) {
    const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
    const divisor = gcd(width, height);
    return {
      width: width / divisor,
      height: height / divisor,
      ratio: width / height,
      formatted: `${width / divisor}:${height / divisor}`
    };
  }

  /**
   * Get optimal resolution for given quality setting
   */
  static getOptimalResolution(quality, deviceType = CameraDeviceType.BACK) {
    const resolutions = {
      [PhotoQuality.LOW]: { width: 640, height: 480 },
      [PhotoQuality.MEDIUM]: { width: 1280, height: 720 },
      [PhotoQuality.HIGH]: { width: 1920, height: 1080 },
      [PhotoQuality.ULTRA_HIGH]: deviceType === CameraDeviceType.BACK 
        ? { width: 3840, height: 2160 } 
        : { width: 1920, height: 1080 }
    };

    return resolutions[quality] || resolutions[PhotoQuality.HIGH];
  }

  /**
   * Calculate file size estimate based on resolution and quality
   */
  static estimateFileSize(width, height, quality, isVideo = false, duration = 0) {
    const pixels = width * height;
    
    if (isVideo) {
      // Video size estimation (bytes per second)
      const qualityMultipliers = {
        [VideoQuality.LOW]: 0.5,
        [VideoQuality.MEDIUM]: 1.0,
        [VideoQuality.HIGH]: 2.0,
        [VideoQuality.ULTRA_HIGH]: 4.0
      };
      
      const bytesPerPixelPerSecond = 0.1;
      const multiplier = qualityMultipliers[quality] || 1.0;
      return Math.round(pixels * bytesPerPixelPerSecond * multiplier * duration);
    } else {
      // Photo size estimation
      const qualityMultipliers = {
        [PhotoQuality.LOW]: 0.3,
        [PhotoQuality.MEDIUM]: 0.6,
        [PhotoQuality.HIGH]: 1.0,
        [PhotoQuality.ULTRA_HIGH]: 1.5
      };
      
      const bytesPerPixel = 3; // Assuming RGB
      const compressionRatio = 0.1; // JPEG compression
      const multiplier = qualityMultipliers[quality] || 1.0;
      return Math.round(pixels * bytesPerPixel * compressionRatio * multiplier);
    }
  }

  /**
   * Validate zoom level for device capabilities
   */
  static validateZoomLevel(zoomLevel, minZoom = 1.0, maxZoom = 10.0) {
    return Math.max(minZoom, Math.min(maxZoom, zoomLevel));
  }

  /**
   * Calculate zoom step for smooth zooming
   */
  static calculateZoomStep(currentZoom, targetZoom, steps = 10) {
    const difference = targetZoom - currentZoom;
    return difference / steps;
  }

  /**
   * Convert screen coordinates to camera coordinates
   */
  static screenToCameraCoordinates(screenX, screenY, cameraWidth, cameraHeight) {
    // Normalize screen coordinates (0-1)
    const normalizedX = screenX / screenWidth;
    const normalizedY = screenY / screenHeight;
    
    // Convert to camera coordinates
    const cameraX = normalizedX * cameraWidth;
    const cameraY = normalizedY * cameraHeight;
    
    return { x: cameraX, y: cameraY };
  }

  /**
   * Calculate focus area from touch point
   */
  static calculateFocusArea(touchX, touchY, areaSize = 100) {
    const halfSize = areaSize / 2;
    
    return {
      x: Math.max(0, Math.min(screenWidth - areaSize, touchX - halfSize)),
      y: Math.max(0, Math.min(screenHeight - areaSize, touchY - halfSize)),
      width: areaSize,
      height: areaSize
    };
  }

  /**
   * Generate unique filename for captures
   */
  static generateFilename(mode, deviceType, timestamp = new Date()) {
    const dateStr = timestamp.toISOString().replace(/[:.]/g, '-');
    const devicePrefix = deviceType === CameraDeviceType.FRONT ? 'front' : 'back';
    const extension = mode === CameraMode.VIDEO ? 'mp4' : 'jpg';
    
    return `${devicePrefix}_${mode}_${dateStr}.${extension}`;
  }

  /**
   * Get camera directory path
   */
  static getCameraDirectoryPath() {
    return `${RNFS.DocumentDirectoryPath}/Camera`;
  }

  /**
   * Get full file path for capture
   */
  static getFilePath(filename) {
    return `${this.getCameraDirectoryPath()}/${filename}`;
  }

  /**
   * Check available storage space
   */
  static async checkStorageSpace() {
    try {
      const fsInfo = await RNFS.getFSInfo();
      return {
        totalSpace: fsInfo.totalSpace,
        freeSpace: fsInfo.freeSpace,
        usedSpace: fsInfo.totalSpace - fsInfo.freeSpace,
        freeSpacePercentage: (fsInfo.freeSpace / fsInfo.totalSpace) * 100
      };
    } catch (error) {
      throw new Error(`Failed to check storage space: ${error.message}`);
    }
  }

  /**
   * Check if there's enough space for capture
   */
  static async hasEnoughSpace(estimatedSize, bufferPercentage = 10) {
    try {
      const storageInfo = await this.checkStorageSpace();
      const requiredSpace = estimatedSize * (1 + bufferPercentage / 100);
      return storageInfo.freeSpace > requiredSpace;
    } catch (error) {
      return false; // Assume no space if check fails
    }
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }

  /**
   * Format duration for display
   */
  static formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    } else if (minutes > 0) {
      return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
    } else {
      return `0:${seconds.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Get camera mode icon
   */
  static getCameraModeIcon(mode) {
    const icons = {
      [CameraMode.PHOTO]: '📷',
      [CameraMode.VIDEO]: '🎥',
      [CameraMode.PORTRAIT]: '👤',
      [CameraMode.NIGHT]: '🌙',
      [CameraMode.PANORAMA]: '🌄'
    };
    
    return icons[mode] || '📷';
  }

  /**
   * Get flash mode icon
   */
  static getFlashModeIcon(flashMode) {
    const icons = {
      [FlashMode.OFF]: '⚡️',
      [FlashMode.ON]: '💡',
      [FlashMode.AUTO]: '🔆',
      [FlashMode.TORCH]: '🔦'
    };
    
    return icons[flashMode] || '⚡️';
  }

  /**
   * Calculate grid lines for camera overlay
   */
  static calculateGridLines(width, height, gridType = 'thirds') {
    const lines = [];
    
    if (gridType === 'thirds') {
      // Rule of thirds grid
      const verticalStep = width / 3;
      const horizontalStep = height / 3;
      
      // Vertical lines
      for (let i = 1; i < 3; i++) {
        lines.push({
          type: 'vertical',
          x: verticalStep * i,
          y1: 0,
          y2: height
        });
      }
      
      // Horizontal lines
      for (let i = 1; i < 3; i++) {
        lines.push({
          type: 'horizontal',
          x1: 0,
          x2: width,
          y: horizontalStep * i
        });
      }
    }
    
    return lines;
  }

  /**
   * Calculate histogram data from image (simulated)
   */
  static calculateHistogram(imageData) {
    // Simulated histogram calculation
    // In real implementation, this would analyze actual image data
    const histogram = {
      red: new Array(256).fill(0),
      green: new Array(256).fill(0),
      blue: new Array(256).fill(0),
      luminance: new Array(256).fill(0)
    };
    
    // Generate simulated histogram data
    for (let i = 0; i < 256; i++) {
      histogram.red[i] = Math.random() * 1000;
      histogram.green[i] = Math.random() * 1000;
      histogram.blue[i] = Math.random() * 1000;
      histogram.luminance[i] = (histogram.red[i] + histogram.green[i] + histogram.blue[i]) / 3;
    }
    
    return histogram;
  }

  /**
   * Validate camera settings
   */
  static validateSettings(settings, deviceCapabilities) {
    const errors = [];
    
    // Validate zoom
    if (settings.zoom < deviceCapabilities.minZoom || settings.zoom > deviceCapabilities.maxZoom) {
      errors.push(`Zoom level ${settings.zoom} is outside device range (${deviceCapabilities.minZoom}-${deviceCapabilities.maxZoom})`);
    }
    
    // Validate flash mode
    if (settings.flashMode !== FlashMode.OFF && !deviceCapabilities.hasFlash) {
      errors.push('Flash mode not supported on this device');
    }
    
    // Validate HDR
    if (settings.enableHDR && !deviceCapabilities.supportsHDR) {
      errors.push('HDR not supported on this device');
    }
    
    // Validate stabilization
    if (settings.enableStabilization && !deviceCapabilities.supportsStabilization) {
      errors.push('Stabilization not supported on this device');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get optimal settings for device
   */
  static getOptimalSettings(deviceCapabilities, mode = CameraMode.PHOTO) {
    const settings = {
      mode,
      flashMode: deviceCapabilities.hasFlash ? FlashMode.AUTO : FlashMode.OFF,
      enableHDR: deviceCapabilities.supportsHDR,
      enableStabilization: deviceCapabilities.supportsStabilization,
      zoom: deviceCapabilities.minZoom,
      photoQuality: PhotoQuality.HIGH,
      videoQuality: VideoQuality.HIGH,
      frameRate: 30
    };
    
    // Adjust for device type
    if (deviceCapabilities.type === CameraDeviceType.FRONT) {
      settings.photoQuality = PhotoQuality.MEDIUM;
      settings.videoQuality = VideoQuality.MEDIUM;
    }
    
    return settings;
  }

  /**
   * Calculate performance score based on metrics
   */
  static calculatePerformanceScore(metrics) {
    let score = 100;
    
    // Penalize slow capture times
    if (metrics.averageCaptureTime > 3000) {
      score -= 30;
    } else if (metrics.averageCaptureTime > 2000) {
      score -= 15;
    }
    
    // Penalize failed captures
    const failureRate = metrics.failedCaptures / (metrics.totalCaptures || 1);
    score -= failureRate * 50;
    
    // Penalize initialization time
    if (metrics.initializationTime > 5000) {
      score -= 20;
    } else if (metrics.initializationTime > 3000) {
      score -= 10;
    }
    
    return Math.max(0, Math.round(score));
  }
}

export default CameraUtils;
