/**
 * PerformanceMonitor - Utility for tracking app performance metrics
 * Monitors FPS, memory usage, JavaScript thread performance, and UI thread performance
 */

import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import LoggerService from '../services/LoggerService';
import { LogLevel, LogCategory } from '../models/LogEntry';

class PerformanceMonitor {
  constructor() {
    this.isMonitoring = false;
    this.metrics = {
      fps: [],
      memoryUsage: [],
      jsThreadUsage: [],
      uiThreadUsage: [],
      renderTimes: [],
      navigationTimes: []
    };
    
    this.intervals = {
      fps: null,
      memory: null,
      threads: null
    };
    
    this.frameCount = 0;
    this.lastFrameTime = performance.now();
    this.renderStartTimes = new Map();
    
    // Performance thresholds
    this.thresholds = {
      lowFPS: 30,
      criticalFPS: 15,
      highMemoryUsage: 80, // percentage
      criticalMemoryUsage: 90, // percentage
      slowRenderTime: 16.67, // 60fps = 16.67ms per frame
      verySlowRenderTime: 33.33 // 30fps = 33.33ms per frame
    };
  }

  /**
   * Start performance monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    LoggerService.info('PerformanceMonitor', 'Performance monitoring started');
    
    // Start FPS monitoring
    this.startFPSMonitoring();
    
    // Start memory monitoring
    this.startMemoryMonitoring();
    
    // Start thread monitoring
    this.startThreadMonitoring();
    
    // Setup global performance tracking
    this.setupGlobalPerformanceTracking();
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    // Clear all intervals
    Object.values(this.intervals).forEach(interval => {
      if (interval) clearInterval(interval);
    });
    
    this.intervals = {
      fps: null,
      memory: null,
      threads: null
    };
    
    LoggerService.info('PerformanceMonitor', 'Performance monitoring stopped');
  }

  /**
   * Start FPS monitoring
   */
  startFPSMonitoring() {
    let frameCount = 0;
    let lastTime = performance.now();
    
    const measureFPS = () => {
      const currentTime = performance.now();
      frameCount++;
      
      // Calculate FPS every second
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        this.metrics.fps.push({
          value: fps,
          timestamp: Date.now()
        });
        
        // Keep only last 60 readings (1 minute at 1 reading per second)
        if (this.metrics.fps.length > 60) {
          this.metrics.fps = this.metrics.fps.slice(-60);
        }
        
        // Log performance issues
        if (fps < this.thresholds.criticalFPS) {
          LoggerService.log(
            LogLevel.ERROR,
            LogCategory.PERFORMANCE_METRICS,
            'PerformanceMonitor',
            `Critical FPS: ${fps}`,
            { fps, threshold: this.thresholds.criticalFPS }
          );
        } else if (fps < this.thresholds.lowFPS) {
          LoggerService.log(
            LogLevel.WARNING,
            LogCategory.PERFORMANCE_METRICS,
            'PerformanceMonitor',
            `Low FPS: ${fps}`,
            { fps, threshold: this.thresholds.lowFPS }
          );
        }
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      if (this.isMonitoring) {
        requestAnimationFrame(measureFPS);
      }
    };
    
    requestAnimationFrame(measureFPS);
  }

  /**
   * Start memory monitoring
   */
  startMemoryMonitoring() {
    const monitorMemory = async () => {
      try {
        const [totalMemory, usedMemory] = await Promise.all([
          DeviceInfo.getTotalMemory(),
          DeviceInfo.getUsedMemory()
        ]);
        
        const memoryUsagePercentage = (usedMemory / totalMemory) * 100;
        
        this.metrics.memoryUsage.push({
          total: totalMemory,
          used: usedMemory,
          free: totalMemory - usedMemory,
          percentage: memoryUsagePercentage,
          timestamp: Date.now()
        });
        
        // Keep only last 120 readings (10 minutes at 1 reading per 5 seconds)
        if (this.metrics.memoryUsage.length > 120) {
          this.metrics.memoryUsage = this.metrics.memoryUsage.slice(-120);
        }
        
        // Log memory issues
        if (memoryUsagePercentage > this.thresholds.criticalMemoryUsage) {
          LoggerService.log(
            LogLevel.ERROR,
            LogCategory.MEMORY_USAGE,
            'PerformanceMonitor',
            `Critical memory usage: ${memoryUsagePercentage.toFixed(1)}%`,
            {
              memoryUsagePercentage,
              usedMemory,
              totalMemory,
              threshold: this.thresholds.criticalMemoryUsage
            }
          );
        } else if (memoryUsagePercentage > this.thresholds.highMemoryUsage) {
          LoggerService.log(
            LogLevel.WARNING,
            LogCategory.MEMORY_USAGE,
            'PerformanceMonitor',
            `High memory usage: ${memoryUsagePercentage.toFixed(1)}%`,
            {
              memoryUsagePercentage,
              usedMemory,
              totalMemory,
              threshold: this.thresholds.highMemoryUsage
            }
          );
        }
        
      } catch (error) {
        LoggerService.error('PerformanceMonitor', 'Error monitoring memory', error);
      }
    };
    
    // Monitor memory every 5 seconds
    this.intervals.memory = setInterval(monitorMemory, 5000);
    monitorMemory(); // Initial reading
  }

  /**
   * Start thread monitoring (simulated for React Native)
   */
  startThreadMonitoring() {
    const monitorThreads = () => {
      // Simulate thread usage monitoring
      // In a real implementation, this would use native modules
      const jsThreadUsage = Math.random() * 100;
      const uiThreadUsage = Math.random() * 100;
      
      this.metrics.jsThreadUsage.push({
        value: jsThreadUsage,
        timestamp: Date.now()
      });
      
      this.metrics.uiThreadUsage.push({
        value: uiThreadUsage,
        timestamp: Date.now()
      });
      
      // Keep only last 60 readings
      if (this.metrics.jsThreadUsage.length > 60) {
        this.metrics.jsThreadUsage = this.metrics.jsThreadUsage.slice(-60);
      }
      
      if (this.metrics.uiThreadUsage.length > 60) {
        this.metrics.uiThreadUsage = this.metrics.uiThreadUsage.slice(-60);
      }
      
      // Log high thread usage
      if (jsThreadUsage > 90) {
        LoggerService.log(
          LogLevel.WARNING,
          LogCategory.PERFORMANCE_METRICS,
          'PerformanceMonitor',
          `High JS thread usage: ${jsThreadUsage.toFixed(1)}%`,
          { jsThreadUsage }
        );
      }
      
      if (uiThreadUsage > 90) {
        LoggerService.log(
          LogLevel.WARNING,
          LogCategory.PERFORMANCE_METRICS,
          'PerformanceMonitor',
          `High UI thread usage: ${uiThreadUsage.toFixed(1)}%`,
          { uiThreadUsage }
        );
      }
    };
    
    // Monitor threads every 2 seconds
    this.intervals.threads = setInterval(monitorThreads, 2000);
    monitorThreads(); // Initial reading
  }

  /**
   * Setup global performance tracking
   */
  setupGlobalPerformanceTracking() {
    // Track navigation performance
    if (global.performance && global.performance.mark) {
      const originalMark = global.performance.mark.bind(global.performance);
      global.performance.mark = (name) => {
        const result = originalMark(name);
        
        if (name.includes('navigation')) {
          LoggerService.log(
            LogLevel.PERFORMANCE,
            LogCategory.PERFORMANCE_METRICS,
            'PerformanceMonitor',
            `Navigation mark: ${name}`,
            { mark: name, timestamp: Date.now() }
          );
        }
        
        return result;
      };
    }
  }

  /**
   * Start render time tracking for a component
   */
  startRenderTracking(componentName) {
    const startTime = performance.now();
    this.renderStartTimes.set(componentName, startTime);
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      this.metrics.renderTimes.push({
        component: componentName,
        renderTime,
        timestamp: Date.now()
      });
      
      // Keep only last 100 render times
      if (this.metrics.renderTimes.length > 100) {
        this.metrics.renderTimes = this.metrics.renderTimes.slice(-100);
      }
      
      // Log slow renders
      if (renderTime > this.thresholds.verySlowRenderTime) {
        LoggerService.log(
          LogLevel.ERROR,
          LogCategory.PERFORMANCE_METRICS,
          'PerformanceMonitor',
          `Very slow render: ${componentName} took ${renderTime.toFixed(2)}ms`,
          { component: componentName, renderTime, threshold: this.thresholds.verySlowRenderTime }
        );
      } else if (renderTime > this.thresholds.slowRenderTime) {
        LoggerService.log(
          LogLevel.WARNING,
          LogCategory.PERFORMANCE_METRICS,
          'PerformanceMonitor',
          `Slow render: ${componentName} took ${renderTime.toFixed(2)}ms`,
          { component: componentName, renderTime, threshold: this.thresholds.slowRenderTime }
        );
      }
      
      this.renderStartTimes.delete(componentName);
    };
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    return {
      fps: {
        current: this.metrics.fps.length > 0 ? this.metrics.fps[this.metrics.fps.length - 1].value : 0,
        average: this.calculateAverage(this.metrics.fps.map(f => f.value)),
        min: Math.min(...this.metrics.fps.map(f => f.value)),
        max: Math.max(...this.metrics.fps.map(f => f.value))
      },
      memory: {
        current: this.metrics.memoryUsage.length > 0 ? this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1] : null,
        averageUsage: this.calculateAverage(this.metrics.memoryUsage.map(m => m.percentage))
      },
      threads: {
        jsThread: {
          current: this.metrics.jsThreadUsage.length > 0 ? this.metrics.jsThreadUsage[this.metrics.jsThreadUsage.length - 1].value : 0,
          average: this.calculateAverage(this.metrics.jsThreadUsage.map(j => j.value))
        },
        uiThread: {
          current: this.metrics.uiThreadUsage.length > 0 ? this.metrics.uiThreadUsage[this.metrics.uiThreadUsage.length - 1].value : 0,
          average: this.calculateAverage(this.metrics.uiThreadUsage.map(u => u.value))
        }
      },
      renders: {
        recentRenders: this.metrics.renderTimes.filter(r => r.timestamp > oneMinuteAgo),
        averageRenderTime: this.calculateAverage(this.metrics.renderTimes.map(r => r.renderTime)),
        slowRenders: this.metrics.renderTimes.filter(r => r.renderTime > this.thresholds.slowRenderTime).length
      },
      timestamp: now
    };
  }

  /**
   * Calculate average of an array of numbers
   */
  calculateAverage(numbers) {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const metrics = this.getCurrentMetrics();
    
    return {
      overall: this.calculateOverallPerformanceScore(metrics),
      fps: metrics.fps,
      memory: metrics.memory,
      threads: metrics.threads,
      renders: metrics.renders,
      issues: this.identifyPerformanceIssues(metrics),
      recommendations: this.generateRecommendations(metrics)
    };
  }

  /**
   * Calculate overall performance score (0-100)
   */
  calculateOverallPerformanceScore(metrics) {
    let score = 100;
    
    // FPS score (30% weight)
    if (metrics.fps.current < this.thresholds.criticalFPS) {
      score -= 30;
    } else if (metrics.fps.current < this.thresholds.lowFPS) {
      score -= 15;
    }
    
    // Memory score (25% weight)
    if (metrics.memory.current && metrics.memory.current.percentage > this.thresholds.criticalMemoryUsage) {
      score -= 25;
    } else if (metrics.memory.current && metrics.memory.current.percentage > this.thresholds.highMemoryUsage) {
      score -= 12;
    }
    
    // Thread usage score (25% weight)
    if (metrics.threads.jsThread.current > 90 || metrics.threads.uiThread.current > 90) {
      score -= 25;
    } else if (metrics.threads.jsThread.current > 70 || metrics.threads.uiThread.current > 70) {
      score -= 12;
    }
    
    // Render performance score (20% weight)
    if (metrics.renders.slowRenders > 10) {
      score -= 20;
    } else if (metrics.renders.slowRenders > 5) {
      score -= 10;
    }
    
    return Math.max(0, Math.round(score));
  }

  /**
   * Identify performance issues
   */
  identifyPerformanceIssues(metrics) {
    const issues = [];
    
    if (metrics.fps.current < this.thresholds.criticalFPS) {
      issues.push({ type: 'critical', category: 'fps', message: `Critical FPS: ${metrics.fps.current}` });
    } else if (metrics.fps.current < this.thresholds.lowFPS) {
      issues.push({ type: 'warning', category: 'fps', message: `Low FPS: ${metrics.fps.current}` });
    }
    
    if (metrics.memory.current && metrics.memory.current.percentage > this.thresholds.criticalMemoryUsage) {
      issues.push({ type: 'critical', category: 'memory', message: `Critical memory usage: ${metrics.memory.current.percentage.toFixed(1)}%` });
    } else if (metrics.memory.current && metrics.memory.current.percentage > this.thresholds.highMemoryUsage) {
      issues.push({ type: 'warning', category: 'memory', message: `High memory usage: ${metrics.memory.current.percentage.toFixed(1)}%` });
    }
    
    if (metrics.renders.slowRenders > 10) {
      issues.push({ type: 'warning', category: 'renders', message: `Many slow renders: ${metrics.renders.slowRenders}` });
    }
    
    return issues;
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(metrics) {
    const recommendations = [];
    
    if (metrics.fps.current < this.thresholds.lowFPS) {
      recommendations.push('Consider optimizing animations and reducing complex UI operations');
    }
    
    if (metrics.memory.current && metrics.memory.current.percentage > this.thresholds.highMemoryUsage) {
      recommendations.push('Review memory usage and implement proper cleanup in components');
    }
    
    if (metrics.renders.slowRenders > 5) {
      recommendations.push('Optimize component renders using React.memo and useMemo');
    }
    
    return recommendations;
  }
}

// Export singleton instance
export default new PerformanceMonitor();
