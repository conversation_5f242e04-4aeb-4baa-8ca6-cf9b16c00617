# 4.10.13-ACB - What's new?
- Update authorities of provider in AndroidManifest.
- Show troubleshootingId on custom dialog.
- Fix issue that shows white screen before exit app.
# 4.10.12-ACB - What's new?
- Increase quit app time to 30 seconds.
- Show error code on custom dialog.
# 4.10.11-ACB - What's new?
- Fix iOS hang app.
- Remove duplicate setup TLA, Ti logic.
# 4.10.10-ACB - What's new?
- Fix VGDialog.
# 4.10.9-ACB - What's new?
- Support compile SDK version 33.
- Modify VGDialog.
# 4.10.8-ACB - What's new?
- Change Processor version to compatible with new release.
# 4.10.7-ACB - What's new?
- Integrate with latest SDKs to fix security issues.
- Add new detections: developer options detection, airdroid detection, virtual tap detection, network detection
- Use new API to enable overlay detection.
- Introduce new react native API to enable virtual tap detection: setVirtualTapDetectionEnabled(boolean virtualTapDetectionEnabled)
- Refactor code.
- Remove all logs for release version.

# 4.10.6-ACB - What's new?
- Modify encrypt and decrypt functions.
- Handle Vguard errors from 20000 to 30000 for both platform.

# 4.10.5-ACB - What's new?
- Handle data of screen sharing detect for SDK's new version.
- Modify data in VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED action.

# 4.10.4-ACB - What's new?
- Update handling error codes.
- Handle screen sharing, overlay, virtual space actions in plugin.

# 4.10.3-ACB - What's new?
-  Remove QUERY_ALL_PACKAGES permission.

# 4.10.2-ACB - What's new?
- Fix issue relate to singleTask launchMode in Android.
- Add logic handle error code in plugin.
- Add show alert and quit app logic for both Android & iOS.

# 4.10.1-ACB - What's new?
-  Add Plugin.signMsg to support App Identity.
-  Add Plugin.signMsgChecksum to support App Identity.
-  Configure Sync for iOS and Android.
-  Sync setDebugable API name for both platform.
-  Sync setAllowsArbitraryNetworking API name for both platform.
-  Sync destroy API name for both platform.
-  Sync setMaximumNetworkRetryTime API name for both platform.
-  Sync resetVOSTrustedStorage API name for both platform.
-  Sync requestScan API name for both platform.
-  Handle exclude files from backup for iOS & restructure code.

# 4.10.1 - What's new?
- Trigger api forceSyncLogs if v-os/vguard initialize throw the exception/error
- Add APIs: 
	+ VguardPlugin.destroy()
	+ VguardPlugin.resetVOSTrustedStorage()

# 4.10.0 - What's new? 
- - Update to be compatible with sdk 4.10.0 and later


# 4.9.2 - What's new?
- Remove buildToolsVersion '33' because it is deprecated
- Use the new way to invoke the VGuard.
- Replace LifecycleEventListener with Application.ActivityLifecycleCallbacks so that we can fix the lifecycle issue.
- Add arrow functions because we use this keyword to fix the undefined issue when focusing on the Secure Keyboard.
- Updated the README.md file following the new way.
- Move the AAR file to the folder to fix the issue that can't build the APK release.
- Fix the issue that can't run on iOS.

# 4.9.1 - What's new?
- Add APIs:
  	+ sdkVersion()
  	+ getFirmwareVersion()
    + getProcessorVersion()
- Add events with action name:
	+ VGuardPlugin.VGUARD_OVERLAY_DETECTED_DISABLE
	+ VGuardPlugin.VGUARD_SCREEN_SHARING_DETECTED 
	## Usage: read more in README.md

# 4.9.0 - What's New?
- Update to be compatible with sdk 4.9.1 and later
- Add APIs: 
	+ setLoggerBaseUrl(String url)	//  This API sets the base URL of the V-OS Troubleshooting Logs Service.
	+ forceSyncLogs() 				//  This API  lets you manually synchornize the troubleshooting logs at the client-side to the V-OS Troubleshooting Logs Service.
	+ setMemoryConfiguration(int config) // 0: DEFAULT, 1: HIGH
	+ setThreatIntelligenceServerURL(String threadIntelUrl)
	+ setMaximumNetworkRetryTime(int retry)
	+ clearVOSTrustedStorage()		//  This API can be used while there is an issue reading from V-OS trusted storage (20035: ERROR_VOS_TRUST_STORAGE_FAILED)
	+ resetVOSTrustedStorage()		//  This API can be used when the SDK encounters 20035: ERROR_VOS_TRUST_STORAGE_FAILED result code.
	
- Add events with action name: 
	+ VGuardPlugin.RESET_VOS_STORAGE
	+ VGuardPlugin.VGUARD_VIRTUAL_SPACE_DETECTED
	+ VGuardPlugin.VGUARD_SSL_ERROR_DETECTED
	+ VGuardPlugin.VGUARD_HANDLE_THREAT_POLICY
  	## Usage: read more in README.md

# Resolved issue(s)
- Fix issue The keyboard not being closed if this is showed by user touch on input text
- Fix issue The keyboard not being closed if autofocus = true text

# Resolved issue(s)
- Update to be compatible with vguard component (vos-app-protection-android-4.8.7.0.aar - sdk 4.8 patch 14 hf3)

# Resolved issues in 4.8.0
- Update to be compatible with sdk 4.8.x
- [AP-263] [Android][React][UOB] SecureKeyboard


