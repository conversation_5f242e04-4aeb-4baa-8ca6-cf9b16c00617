apply plugin: 'com.android.library'
apply plugin:'base'
android {
    compileSdkVersion 33
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 4
        versionName "4.10.3"
    }
    lintOptions {
        abortOnError false
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding true
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'com.facebook.react:react-native:+'

    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // VKey SDK
    implementation (name: 'vos-processor-android-4.10.3.2', ext: 'aar')
    implementation (name: 'vos-app-protection-android-4.10.2.8', ext: 'aar')
    implementation (name: 'cryptota-android-4.9.0.2', ext: 'aar')
    // VKey SDK dependecies
    implementation 'com.getkeepsafe.relinker:relinker:1.4.4'
    implementation 'com.google.guava:guava:32.1.2-android'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.lifecycle:lifecycle-process:2.6.2'

    implementation 'com.google.code.gson:gson:2.8.6'
    api 'io.jsonwebtoken:jjwt-api:0.10.7'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.10.7'
    runtimeOnly('io.jsonwebtoken:jjwt-orgjson:0.10.7') {
        exclude group: 'org.json', module: 'json' //provided by Android natively
    }
}
  