package com.vkey.vguard;
import android.content.Context;
import android.util.Base64;
import com.vkey.android.vguard.VGuard;
import com.vkey.android.vguard.VGuardFactory;
import com.vkey.vos.signer.taInterface;

import org.json.JSONException;
import org.json.JSONObject;
import java.io.File;
import vkey.android.vos.VosWrapper;

public class CryptoTaImpl {
    private static final String TAG = CryptoTaImpl.class.getName();
    private static final int VK_TA_INIT = -1;
    private static int cryptoTaStatus = VK_TA_INIT;
    public static String TA_ALIAS = "";
    public static void unloadCryptoTA() {
        taInterface.getInstance().unloadTA();
        cryptoTaStatus =VK_TA_INIT;
    }
    public static int loadCryptoTA(Context ctx) {
        int vosStatus = VosWrapper.getInstance(ctx).execute(() -> {
        });
        if (vosStatus < 1) {
            return (int) vosStatus;
        } else {
            if (cryptoTaStatus > 0) {
                return cryptoTaStatus;
            }

            taInterface _tainterface = taInterface.getInstance();

            cryptoTaStatus = _tainterface.loadTA(); // success = 2
            if (cryptoTaStatus >= 0) {
                cryptoTaStatus = _tainterface.initialize();  // success = 1

                if (cryptoTaStatus >= 0) {
                    int processManifest = _tainterface.processManifest(ctx); // success = 1
                    cryptoTaStatus = processManifest;

                    if (processManifest < 0) {
                        int errorPrimary = _tainterface.getPrimaryError(processManifest);
                        int error2nd = _tainterface.getSecondaryError(processManifest);
                        if (error2nd == -3 || error2nd == -4 || error2nd == -5) {
                            int ret = _tainterface.clearCryptoTa();

                            cryptoTaStatus = _tainterface.processManifest(ctx);
                        }
                        else if (error2nd == -6) {
                            _tainterface.clearCryptoTa();
                            _tainterface.unloadTA();
                            return loadCryptoTA(ctx);
                        }
                        else if (error2nd == -180) {
                            cleanVKAssets(ctx);
                            // TODO: restart vguard
                            if(cryptotaCallback!= null) {
                                cryptotaCallback.triggerRestartVguard();
                            }
                            return cryptoTaStatus;
                        }
                    }
                }
            }

            if(cryptoTaStatus == -5007 || cryptoTaStatus == -5008 || cryptoTaStatus == -5009) {
                cleanVKAssets(ctx);
                // TODO: restart vguard
                if(cryptotaCallback!= null) {
                    cryptotaCallback.triggerRestartVguard();
                }
            }
            else if(cryptoTaStatus == -5107){
                _tainterface.unloadTA();
                cryptoTaStatus = loadCryptoTA(ctx);
            }
            return cryptoTaStatus;
        }
    }

    public static String signMsg(Context ctx, String data) {
        VosWrapper vosWrapper = VosWrapper.getInstance(ctx);
        int executeRet = vosWrapper.execute(() -> {
        });
        if (executeRet < 1) {
            return Integer.toString(executeRet);
        }

        if (cryptoTaStatus == VK_TA_INIT) {
            cryptoTaStatus = loadCryptoTA(ctx);
        }
        if (cryptoTaStatus < 0)  {
            return Integer.toString(executeRet);
        }

        int[] iArr = new int[1];
        byte[] bytes = data.getBytes();
        taInterface tainterface = taInterface.getInstance();
        byte[] signMsg = tainterface.signMsg(bytes, TA_ALIAS, 2, iArr);
        if(signMsg == null || iArr[0]<=0)  {
            return String.valueOf(iArr[0]);
        }

        String str = new String(Base64.encode(signMsg, 0));
        return str.replace("\n","").replace("\r","");
    }

    public static JSONObject signMsgChecksum(Context ctx, String data){
        try{
            JSONObject dataObject = new JSONObject();
            //Check is vos running
            VosWrapper vosWrapper = VosWrapper.getInstance(ctx);
            int executeRet = vosWrapper.execute(() -> {});
            if(executeRet < 1){
                dataObject.put("error", Integer.toString(executeRet));
                return dataObject;
            }
            if(cryptoTaStatus == VK_TA_INIT){
                cryptoTaStatus =  loadCryptoTA(ctx);
            }
            if(cryptoTaStatus < 0){
                dataObject.put("error", Integer.toString(executeRet));
                return dataObject;
            }

            int[] error = new int[1];
            byte[] bytes = data.getBytes();
            byte[] checksum = { 0, 0, 0, 0 };
            byte[] epocTime = { 0, 0, 0, 0 };
            taInterface tainterface = taInterface.getInstance();
            byte[] signMsg = tainterface.signMsgChecksum(bytes, TA_ALIAS, epocTime, checksum, taInterface.SHA256, error);
            if(signMsg == null || error[0] <= 0){
                int primaryError = tainterface.getPrimaryError(error[0]);
                int secondaryError = tainterface.getSecondaryError(error[0]);
                dataObject.put("error", error[0]);
                return dataObject;
            }

            String signature = new String(Base64.encode(signMsg,0));
            dataObject.put("signature", signature.replace("\n","").replace("\r",""));
            dataObject.put("checksum", new String(Base64.encode(checksum,0)).replace("\n","").replace("\r",""));
            dataObject.put("epoctime", new String(Base64.encode(epocTime,0)).replace("\n","").replace("\r",""));
            return dataObject;
        } catch(JSONException ex){
            return new JSONObject();
        }
    }

    public static void cleanVKAssets(Context ctx) {
        // unloadTA
        taInterface.getInstance().unloadTA();

        try {
            VGuard vguard = VGuardFactory.getInstance();
            if (vguard != null) {
                vguard.resetVOSTrustedStorage();
            } else {
                VosWrapper.getInstance(ctx).clearVOS();
            }
        } catch (Exception ignored) {}
        try {
            String[] listAssets = new String[]{
                    "crypto_ta.bin",
                    "firmware",
                    "profile",
                    "sig_cfs",
                    "signature",
                    "vkeylicensepack",
                    "manifest.json"
            };
            String dataFilesSep = ctx.getFilesDir().getAbsolutePath()  + File.separator;
            for (String fileName: listAssets) {
                File file = new File(dataFilesSep + fileName);
                try {
                    boolean rs = file.delete();
                } catch (Exception e) {e.fillInStackTrace();}
            }
        } catch (Exception ignored) {}
        try {
            VGuard vguard = VGuardFactory.getInstance();
            if (vguard != null) {
                vguard.destroy();
            } else {
                VosWrapper.getInstance(ctx).stopVOS();
            }
        } catch (Exception ignored) {}
    }

    public static CryptoTaCallback cryptotaCallback;
    public interface CryptoTaCallback {
        void triggerRestartVguard();
    }
}
