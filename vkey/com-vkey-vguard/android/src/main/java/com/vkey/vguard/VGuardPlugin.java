package com.vkey.vguard;

import static com.vkey.android.vguard.VGuardBroadcastReceiver.ACTION_FINISH;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.ACTION_SCAN_COMPLETE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.PROFILE_LOADED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.SCAN_COMPLETE_RESULT;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_AIRDROID_PORT_IS_OPEN;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_ALERT_MESSAGE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_ALERT_TITLE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_DEVELOPER_OPTIONS_ENABLED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_DISABLED_APP_EXPIRED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_HANDLE_THREAT_POLICY;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_HIGHEST_THREAT_POLICY;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_MESSAGE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_NETWORK_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_OVERLAY_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_OVERLAY_DETECTED_DISABLE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SCREEN_SHARING_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SSL_ERROR_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_STATUS;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_VIRTUAL_SPACE_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_VIRTUAL_TAP_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VOS_READY;
import static com.vkey.android.vguard.model.VGThreatPolicy.BY_PASS;
import static com.vkey.android.vguard.model.VGThreatPolicy.DISABLE_APP;
import static com.vkey.android.vguard.model.VGThreatPolicy.QUIT_APP;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Base64;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.vkey.android.internal.vguard.engine.BasicThreatInfo;
import com.vkey.android.vguard.ActivityLifecycleHook;
import com.vkey.android.vguard.FeatureToggleManager;
import com.vkey.android.vguard.LocalBroadcastManager;
import com.vkey.android.vguard.MemoryConfiguration;
import com.vkey.android.vguard.VGExceptionHandler;
import com.vkey.android.vguard.VGFullScreenDialogActivity;
import com.vkey.android.vguard.VGuard;
import com.vkey.android.vguard.VGuardBroadcastReceiver;
import com.vkey.android.vguard.VGuardFactory;
import com.vkey.android.vguard.VGuardLifecycleHook;
import com.vkey.android.vguard.model.VGThreatPolicy;
import com.vkey.android.vguard.model.VGThreatResponse;
import com.vkey.android.vguard.model.VGVirtualTapType;
import com.vkey.android.vguard.model.VGuardNetworkType;
import com.vkey.vguard.common.Utility;
import com.vkey.vguard.dialog.VGDialogActivity;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import vkey.android.vos.VosWrapper;

public class VGuardPlugin extends ReactContextBaseJavaModule implements CryptoTaImpl.CryptoTaCallback {

  @NonNull
  @Override
  public String getName() {
    return "VGuardPlugin";
  }

  private static final String TAG = VGuardPlugin.class.getName();
  private static ReactApplicationContext reactContext;
  private static WeakReference<Activity> currentActivity;
  private static final int ERROR = -1;
  private static final String VGUARD_EVENTS = "vkey.android.vguard.events";
  private static final String VGUARD_ERROR = "VGUARD_ERROR";
  private static final String FIRMWARE_RETURN_CODE = "vkey.android.vguard.FIRMWARE_RETURN_CODE";
  private static final String RESET_VOS_STORAGE = "vkey.android.vguard.resetVOSTrustedStorageRvcr";
  private static final List<String> VOS_STORAGE_ERROR_CODE = Arrays.asList("-3", "-5", "20035");
  private static final List<String> CRITICAL_ERROR_CODE = Arrays.asList("20030","20031","20032","20033");
  private static final List<String> EMULATOR_ERROR_CODE = Arrays.asList("-1039", "20050");
  private static final String HARD_CODE_TITLE_DIALOG = "Thiết bị của bạn không đảm bảo an toàn";
  private static final String HARD_CODE_MESSAGE_DIALOG = "Ứng dụng ACB ONE phát hiện thiết bị hiện tại không an toàn. Để bảo vệ cho tài khoản và thông tin của Quý khách, vui lòng kiểm tra lại thiết bị.\n" +
          "Quý khách có thể liên hệ Phòng Chăm sóc và Dịch vụ Khách hàng của ACB theo số 1900545486 - (028) 38247247 hoặc ACB gần nhất để được hướng dẫn";
  private static final String HARD_CODE_MESSAGE_DIALOG_2 = "\nỨng dụng sẽ tự đóng sau 30 giây";
  private static final String HARD_CODE_BUTTON_DIALOG = "Đóng";
  private static Boolean sendErrorToHostApp = false;
  private static Boolean showDialogAndQuit = false;
  private static Boolean sdkShowedPopup = false;
  private static Boolean showPopupByAction = false;
  private static Exception currentError = null;
  // For VGuard to notify host app of events
  private static VGuardBroadcastReceiver mVGuardRcvr;

  // VGuard object that is used for scanning
  private static VGuard vGuardMgr;
  private final static long VOS_INIT = 1;
  private static long vosFirmwareCode = VOS_INIT;
  // LifecycleHook to notify VGuard of activity's lifecycle
  public static VGuardLifecycleHook hook;

  private MemoryConfiguration mMemoryConfiguration = MemoryConfiguration.DEFAULT;
  private boolean isAllowsArbitraryNetworking = false;
  private String tlaUrl = null;
  private String tiUrl = null;
  private boolean isDebug = false;

  private static int activityCount = 0;
  private boolean isOverlayDetectionEnabled = false;
  private VGThreatPolicy highestThreatPolicy = null;
  private String dialogTitle = "";
  private String dialogMessage = "";
  private static final ArrayList<VGThreatResponse> dialogThreats = new ArrayList<>();

  private boolean isVirtualTapDetectionEnabled = false;

  public VGuardPlugin(ReactApplicationContext reactContext) {
    super(reactContext);
    VGuardPlugin.reactContext = reactContext;
  }

  public static void setCurrentActivity(Activity activity) {
    if (activity != null) {
      currentActivity = new WeakReference<>(activity);
    }
  }

  public static void setVGActivityLifecycleCallbacks(Application application) {
    if (application != null) {
      application.registerActivityLifecycleCallbacks(vgActivityLifecycleCallbacks);
    }
  }

  private static final VGActivityLifecycleCallbacks vgActivityLifecycleCallbacks = new VGActivityLifecycleCallbacks() {
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
      super.onActivityCreated(activity, savedInstanceState);
      setCurrentActivity(activity);
      activityCount++;
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
      setCurrentActivity(activity);
      if (vGuardMgr != null && hook != null ) {
        try {
          if(currentError == null){
            if(sdkShowedPopup || showPopupByAction){
              if(!(activity instanceof VGFullScreenDialogActivity)){
                showNewPopup(currentActivity.get(), dialogThreats, QUIT_APP);
              }
            }
            vGuardMgr.onResume(hook, reactContext);
            vGuardMgr.requestScan();
          } else{
            if(!(activity instanceof VGDialogActivity)){
              checkQuitAppOrSendError(currentError);
            }
          }
        } catch (Exception e) {
          e.printStackTrace();
        }
      }
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
      if (vGuardMgr != null && hook != null) {
        try {
          vGuardMgr.onPause(hook);
        } catch (Exception e) {
          e.printStackTrace();
        }
      }
    }

    /**
     * Because onActivityDestroyed is called continuously,
     * it will destroy the VGuard if we invoke the onDestroy method here,
     * so we need to check the host activity is true to destroy VGuard.
     */
    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
      activityCount--;
      if(activityCount <= 0) {
        _vguardDestroy();
        activityCount = 0;
      }
    }
  };

  private static void checkQuitAppOrSendError(Exception e) {
    if(sendErrorToHostApp){
      logAndSendError(e);
    }
    if(showDialogAndQuit){
      showPopup(currentActivity.get(),HARD_CODE_TITLE_DIALOG, HARD_CODE_MESSAGE_DIALOG + "\nMã lỗi: " + e.getMessage() + "\nTID: " + getTroubleshootingId() + HARD_CODE_MESSAGE_DIALOG_2, HARD_CODE_BUTTON_DIALOG, QUIT_APP);
    }
  }

  static void _vguardDestroy() {

      if (reactContext != null) {
        CryptoTaImpl.unloadCryptoTA();
      }
      if (mVGuardRcvr != null) {
        LocalBroadcastManager.getInstance(reactContext).unregisterReceiver(mVGuardRcvr);
        mVGuardRcvr = null;
      }

      vosFirmwareCode = VOS_INIT;
      if (vGuardMgr != null) {
        try {
          vGuardMgr.destroy();
        } catch (Exception e) {
          e.printStackTrace();
        }
        vGuardMgr = null;
      }

  }


  static boolean isVosStarted() {
    if (vGuardMgr != null) {
      return vGuardMgr.getIsVosStarted();
    }
    return false;
  }

  @javax.annotation.Nullable
  @Override
  public Map<String, Object> getConstants() {
    final Map<String, Object> constants = new HashMap<>();
    constants.put("VGUARD_EVENTS", VGUARD_EVENTS);
    constants.put("VGUARD_ERROR", VGUARD_ERROR);
    constants.put("VOS_READY", VOS_READY);
    constants.put("VGUARD_STATUS", VGUARD_STATUS);
    constants.put("VGUARD_OVERLAY_DETECTED", VGUARD_OVERLAY_DETECTED);
    constants.put("VGUARD_OVERLAY_DETECTED_DISABLE", VGUARD_OVERLAY_DETECTED_DISABLE);
    constants.put("VGUARD_PROFILE_LOADED", PROFILE_LOADED);

    // scan threats
    constants.put("ACTION_FINISH", ACTION_FINISH);
    constants.put("ACTION_SCAN_COMPLETE", ACTION_SCAN_COMPLETE);

    // 4.9
    constants.put("RESET_VOS_STORAGE", RESET_VOS_STORAGE);
    constants.put("VGUARD_VIRTUAL_SPACE_DETECTED", VGUARD_VIRTUAL_SPACE_DETECTED);
    constants.put("VGUARD_SCREEN_SHARING_DETECTED", VGUARD_SCREEN_SHARING_DETECTED);
    constants.put("VGUARD_SSL_ERROR_DETECTED", VGUARD_SSL_ERROR_DETECTED);
    constants.put("VGUARD_HANDLE_THREAT_POLICY", VGUARD_HANDLE_THREAT_POLICY);
    constants.put("VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED", VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED);
    //4.10
    constants.put("VGUARD_DEVELOPER_OPTIONS_ENABLED", VGUARD_DEVELOPER_OPTIONS_ENABLED);
    constants.put("VGUARD_AIRDROID_PORT_IS_OPEN", VGUARD_AIRDROID_PORT_IS_OPEN);
    constants.put("VGUARD_VIRTUAL_TAP_DETECTED", VGUARD_VIRTUAL_TAP_DETECTED);
    constants.put("VGUARD_NETWORK_DETECTED", VGUARD_NETWORK_DETECTED);
    return constants;
  }

  @ReactMethod
  public void forceSyncLogs() {
    if (!TextUtils.isEmpty(tlaUrl)) {
      VosWrapper.getInstance(reactContext).forceSyncLogs();
    }
  }

  @ReactMethod
  public void setLoggerBaseUrl(String url) {
    tlaUrl = url;
    VosWrapper.getInstance(reactContext).setLoggerBaseUrl(url);
  }

  @ReactMethod
  public void setThreatIntelligenceServerURL(String threadIntelUrl) {
    tiUrl = threadIntelUrl;
  }

  @ReactMethod
  public void setDebugable(boolean debugable) {
    this.isDebug = debugable;
    VGuardFactory.debug = debugable;
  }

  @ReactMethod
  public void setAllowsArbitraryNetworking(boolean enable) {
    isAllowsArbitraryNetworking = enable;
    if (vGuardMgr != null) {
      vGuardMgr.allowsArbitraryNetworking(enable);
    }
  }

  @ReactMethod
  public void setMemoryConfiguration(int config) {
    mMemoryConfiguration = config == 0 ? MemoryConfiguration.DEFAULT : MemoryConfiguration.HIGH;
    if (vGuardMgr != null) {
      vGuardMgr.setMemoryConfiguration(mMemoryConfiguration);
    }
  }

  @ReactMethod
  public void setMaximumNetworkRetryTime(int retry) {
    if (vGuardMgr != null) {
      vGuardMgr.setMaximumNetworkRetryTime(retry);
    }
  }

  @ReactMethod
  public boolean clearVOSTrustedStorage() {
    if (vGuardMgr != null) {
      return vGuardMgr.clearVOSTrustedStorage();
    }
    return false;
  }

  @ReactMethod
  public boolean resetVOSTrustedStorage() {
    if (vGuardMgr != null) {
      vGuardMgr.resetVOSTrustedStorage();
    }
    return false;
  }

  /**
   * This method is only used on Android platform.
   */
  @ReactMethod
  public void setOverlayDetectionEnabled(boolean isEnable) {
    this.isOverlayDetectionEnabled = isEnable;
  }

  @ReactMethod
  public void destroy() {
    _vguardDestroy();
  }

  @ReactMethod
  public void setupVGuard() {
    // register using LocalBroadcastManager only for keeping data within your app
    if (mVGuardRcvr == null) {
      vosFirmwareCode = VOS_INIT;
      mVGuardRcvr = initialVGuardReceiver();

      VGuardBroadcastReceiver resetVOSTrustedStorageRvcr = new VGuardBroadcastReceiver(null) {
        @Override
        public void onReceive(Context context, Intent intent) {
          super.onReceive(context, intent);
          boolean isResetVOSTrustedStorageSuccess = vGuardMgr.resetVOSTrustedStorage();
          sendEventEmitter(reactContext, RESET_VOS_STORAGE, isResetVOSTrustedStorageSuccess);
        }
      };
      LocalBroadcastManager localBroadcastMgr = LocalBroadcastManager.getInstance(reactContext);
      // necessary for vguard to finish activity safely
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(ACTION_FINISH));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(ACTION_SCAN_COMPLETE));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VOS_READY));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(PROFILE_LOADED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_OVERLAY_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_OVERLAY_DETECTED_DISABLE));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_STATUS));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_VIRTUAL_SPACE_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_SCREEN_SHARING_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_DEVELOPER_OPTIONS_ENABLED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_AIRDROID_PORT_IS_OPEN));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_VIRTUAL_TAP_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_NETWORK_DETECTED));

      localBroadcastMgr.registerReceiver(resetVOSTrustedStorageRvcr, new IntentFilter(RESET_VOS_STORAGE));
    }
    invokeVGuard();
  }

  private final VGExceptionHandler vgExceptionHandler = e -> {
    getVGuardInstance();
    String errorCode = e.getMessage();
    if(errorCode != null){
      handleErrorCode(errorCode);
    }
    currentError = e;
    checkQuitAppOrSendError(currentError);
    VosWrapper.getInstance(reactContext).forceSyncLogs();
  };

  private void handleErrorCode(String errorCode) {
    //Check emulator
    if(EMULATOR_ERROR_CODE.contains(errorCode)){
      sendErrorToHostApp = true;
      showDialogAndQuit = true;
    }
    else {
      //Check critical error
      if(CRITICAL_ERROR_CODE.contains(errorCode)){
        sendErrorToHostApp = true;
        showDialogAndQuit = true;
      }
      else {
        //Check trusted storage issue
        if(VOS_STORAGE_ERROR_CODE.contains(errorCode)){
          sendErrorToHostApp = false;
          showDialogAndQuit = false;
          vGuardMgr.resetVOSTrustedStorage();
          vGuardMgr.destroy();
          invokeVGuard();
        }
        else{
          //Quit app if 20000 <= error < 30000 (V-guard errors)
          sendErrorToHostApp = true;
          try {
            int error = Integer.parseInt(errorCode);
            if(error >= 20000 && error < 30000){
              showDialogAndQuit = true;
            }
            //Quit app if error < -999 (V-os errors)
            else{
              showDialogAndQuit = error < -999;
            }
          } catch(Exception ignored) {
          }
        }
      }
    }
  }

  private static void logAndSendError(Exception e){
    sendEventEmitter(reactContext, VGUARD_ERROR, e.getMessage());
  }

  public void invokeVGuard() {
    resetValueOfDialog();
    try {
      VGuardFactory.Builder builder = new VGuardFactory.Builder();
      builder.setDebugable(isDebug)
              .setMemoryConfiguration(mMemoryConfiguration)
              .setVGExceptionHandler(vgExceptionHandler)
              .setAllowsArbitraryNetworking(isAllowsArbitraryNetworking)
              .setVirtualTapDetectionEnabled(isVirtualTapDetectionEnabled);
      //Overlay Detection
      FeatureToggleManager featureToggleManager = FeatureToggleManager.getInstance();
      featureToggleManager.enableGenericFeature(FeatureToggleManager.FeatureName.OVERLAY_DETECTION, isOverlayDetectionEnabled);
      new VGuardFactory().getVGuard(reactContext.getCurrentActivity(), builder);
    } catch (Exception e) {
      vgExceptionHandler.handleException(e);
    }

  }

  private static void getVGuardInstance() {
    if (vGuardMgr == null) {
      try {
        vGuardMgr = VGuardFactory.getInstance();
        // necessary for VGuard to be informed of the activity's lifecycle
        hook = new ActivityLifecycleHook(vGuardMgr);
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
  }

  private static void sendEventEmitter(ReactApplicationContext reactAppCtx, String eventName, @Nullable Object params) {
    WritableMap emitData = Arguments.createMap();
    emitData.putString("action", eventName);
    String key = "data";
    if (params != null) {
      if (params instanceof Integer) {
        emitData.putInt(key, (Integer) params);
      } else if (params instanceof Long) {
        emitData.putString(key, Long.toString((long) params));
      } else if (params instanceof String) {
        emitData.putString(key, (String) params);
      } else if (params instanceof WritableArray) {
        emitData.putArray(key, (WritableArray) params);
      } else if (params instanceof WritableMap) {
        emitData.putMap(key, (WritableMap) params);
      }
    }

    reactAppCtx
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
            .emit(VGUARD_EVENTS, emitData);
  }

  private VGuardBroadcastReceiver initialVGuardReceiver() {
    return new VGuardBroadcastReceiver(null) {
      @Override
      public void onReceive(Context context, Intent intent) {
        super.onReceive(context, intent);
        if(intent.getAction() != null) {
          switch(intent.getAction()) {
            case ACTION_FINISH: {
              sendEventEmitter(reactContext, ACTION_FINISH, null);
              quitApp();
              break;
            }
            case PROFILE_LOADED: {
              sendEventEmitter(reactContext, PROFILE_LOADED, null);
              break;
            }
            case VOS_READY: {
              vosFirmwareCode = intent.getLongExtra(FIRMWARE_RETURN_CODE, 0);

              getVGuardInstance();

              if (vGuardMgr != null && !TextUtils.isEmpty(tiUrl)) {
                vGuardMgr.setThreatIntelligenceServerURL(tiUrl);
              }

              if (vosFirmwareCode > 0) {
                currentError = null;
                sendErrorToHostApp = false;
                showDialogAndQuit = false;
                initializeCryptoTA(context);
              }
              sendEventEmitter(reactContext, VOS_READY, vosFirmwareCode);
              break;
            }
            case ACTION_SCAN_COMPLETE: {
              WritableArray arrayData = getArrayThreats(intent);
              sendEventEmitter(reactContext, ACTION_SCAN_COMPLETE, arrayData);
              break;
            }
            case VGUARD_OVERLAY_DETECTED: {
              sendEventEmitter(reactContext, VGUARD_OVERLAY_DETECTED, null);
              showPopupByAction = true;
              showPopup(currentActivity.get(),HARD_CODE_TITLE_DIALOG, HARD_CODE_MESSAGE_DIALOG + "\nMã lỗi: 10000" + "\nTID: " + getTroubleshootingId() + HARD_CODE_MESSAGE_DIALOG_2, HARD_CODE_BUTTON_DIALOG, QUIT_APP);
              break;
            }
            case VGUARD_OVERLAY_DETECTED_DISABLE: {
              sendEventEmitter(reactContext, VGUARD_OVERLAY_DETECTED_DISABLE, null);
              break;
            }
            case VGUARD_VIRTUAL_SPACE_DETECTED: {
              sendEventEmitter(reactContext, VGUARD_VIRTUAL_SPACE_DETECTED, null);
              showPopupByAction = true;
              showPopup(currentActivity.get(),HARD_CODE_TITLE_DIALOG, HARD_CODE_MESSAGE_DIALOG + "\nMã lỗi: 9000" + "\nTID: " + getTroubleshootingId() + HARD_CODE_MESSAGE_DIALOG_2, HARD_CODE_BUTTON_DIALOG, QUIT_APP);
              break;
            }
            case VGUARD_STATUS: {
              if (intent.hasExtra(VGUARD_HANDLE_THREAT_POLICY)) {
                handleThreatPolicy(intent, VGUARD_HANDLE_THREAT_POLICY);
              }
              else if (intent.hasExtra(VGUARD_SSL_ERROR_DETECTED)) {
                 handleSslErrorDetection(intent, VGUARD_SSL_ERROR_DETECTED);
              } else {
                String message = intent.getStringExtra(VGUARD_MESSAGE);
                sendEventEmitter(reactContext, VGUARD_STATUS, message);
              }
              break;
            }
            case VGUARD_SCREEN_SHARING_DETECTED: {
              StringBuilder builder = new StringBuilder();
              try {
                String sharingDisplays = intent.getStringExtra(VGUARD_SCREEN_SHARING_DISPLAY_NAMES);
                JSONArray jsonArray = new JSONArray(sharingDisplays);
                builder.append(jsonArray);
              } catch (Exception e) {
                e.fillInStackTrace();
              }
              sendEventEmitter(reactContext, VGUARD_SCREEN_SHARING_DETECTED, builder.toString());
              showPopupByAction = true;
              showPopup(currentActivity.get(),HARD_CODE_TITLE_DIALOG, HARD_CODE_MESSAGE_DIALOG + "\nMã lỗi: 8000" + "\nTID: " + getTroubleshootingId()+ HARD_CODE_MESSAGE_DIALOG_2, HARD_CODE_BUTTON_DIALOG, QUIT_APP);
              break;
            }
            case VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED: {
              StringBuilder builder = new StringBuilder();
              try {
                String sideloadlist = intent.getStringExtra(VGUARD_SIDELOADED_RESULT);
                if (!TextUtils.isEmpty(sideloadlist)) {
                  JSONArray jsonArray = new JSONArray(sideloadlist);
                  builder.append(jsonArray);
                } else {
                  String packageID = intent.getStringExtra("vkey.android.vguard.VGUARD_SIDELOADED_PACKAGE_ID");
                  String source = intent.getStringExtra("vkey.android.vguard.VGUARD_SIDELOADED_SOURCE");
                  builder.append("PackageID: ").append(packageID);
                  builder.append("Source Install: ").append(source);
                }

              } catch (Exception e) {
                e.fillInStackTrace();
              }
              sendEventEmitter(reactContext, VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED, builder.toString());
              break;
            }
            case VGUARD_DEVELOPER_OPTIONS_ENABLED: {
              sendEventEmitter(reactContext, VGUARD_DEVELOPER_OPTIONS_ENABLED, null);
              break;
            }
            case VGUARD_AIRDROID_PORT_IS_OPEN: {
              sendEventEmitter(reactContext, VGUARD_AIRDROID_PORT_IS_OPEN, null);
              break;
            }
            case VGUARD_VIRTUAL_TAP_DETECTED: {
              VGVirtualTapType type = (VGVirtualTapType) intent.getSerializableExtra(VGuardBroadcastReceiver.VGUARD_VIRTUAL_TAP_TYPE);
              String virtualTapTypeString = convertVirtualTapTypeToString(type);
              sendEventEmitter(reactContext, VGUARD_VIRTUAL_TAP_DETECTED, virtualTapTypeString);
              break;
            }
            case VGUARD_NETWORK_DETECTED: {
              VGuardNetworkType[] networkTypes = (VGuardNetworkType[]) intent.getSerializableExtra(VGuardBroadcastReceiver.VGUARD_NETWORK_TYPES);
              if(networkTypes != null) {
                WritableArray arrayData = getArrayNetworkTypes(networkTypes);
                sendEventEmitter(reactContext, VGUARD_NETWORK_DETECTED, arrayData);
              }
              break;
            }
          }
        }
      }
    };
  }

  private String convertVirtualTapTypeToString(VGVirtualTapType type) {
    switch (type) {
      case VIRTUAL_TAP: return "VIRTUAL_TAP";
      case CORELLIUM_VD: return "CORELLIUM_VD";
      case ANDROID_STUDIO_EMULATOR: return "ANDROID_STUDIO_EMULATOR";
      case AIRDROID_USB_DEBUG: return "AIRDROID_USB_DEBUG";
      case P_CLOUDY_NON_VIRTUAL: return "P_CLOUDY_NON_VIRTUAL";
      default: return "UNKNOWN";
    }
  }

  private WritableArray getArrayNetworkTypes(VGuardNetworkType[] networkTypes) {
    WritableArray arrayData = Arguments.createArray();
    for(VGuardNetworkType networkType: networkTypes) {
      String networkTypeString = convertNetworkTypeToString(networkType);
      arrayData.pushString(networkTypeString);
    }
    return arrayData;
  }

  private String convertNetworkTypeToString(VGuardNetworkType networkType) {
    switch (networkType) {
      case WIFI: return "WIFI";
      case CELLULAR: return "CELLULAR";
      case VPN: return "VPN";
      case ETHERNET: return "ETHERNET";
      case BLUETOOTH: return "BLUETOOTH";
      case WIFI_AWARE: return "WIFI_AWARE";
      case LOWPAN: return "LOWPAN";
      case USB: return "USB";
      case THREAD: return "THREAD";
      case SATELLITE: return "SATELLITE";
      default: return "UNKNOWN";
    }
  }

  private WritableArray getArrayThreats(Intent intent) {
    ArrayList<Parcelable> detectedThreats = (ArrayList<Parcelable>) intent.getParcelableArrayListExtra(SCAN_COMPLETE_RESULT);
    StringBuilder builder = new StringBuilder();
    WritableArray arrayData = Arguments.createArray();
    if(detectedThreats != null) {
      for (Parcelable info : detectedThreats) {
        BasicThreatInfo threatInfo = (BasicThreatInfo) info;
        WritableMap infoMap = Arguments.createMap();
        infoMap.putString("ThreatClass", threatInfo.getThreatClass());
        infoMap.putString("ThreatInfo", threatInfo.getThreatInfo());
        infoMap.putString("ThreatName", threatInfo.getThreatName());
        infoMap.putString("ThreatPackageID", threatInfo.getThreatPackage());

        arrayData.pushMap(infoMap);

        // print log
        String infoStr = ((BasicThreatInfo) info).toString();
        builder.append(infoStr).append("\n");
      }
    }
    return arrayData;
  }

  private void handleSslErrorDetection(Intent intent, String eventName) {
    boolean sslErr = intent.getBooleanExtra(VGUARD_SSL_ERROR_DETECTED, false);

    WritableMap mapData = Arguments.createMap();
    mapData.putBoolean(VGUARD_SSL_ERROR_DETECTED, sslErr);
    if (sslErr) {
      try {
        String message = intent.getStringExtra(VGUARD_MESSAGE);
        if (message != null) {
          JSONObject jsonObject = new JSONObject(message);
          mapData.putString(VGUARD_ALERT_TITLE, jsonObject.optString(VGUARD_ALERT_TITLE));
          mapData.putString(VGUARD_ALERT_MESSAGE, jsonObject.optString(VGUARD_ALERT_MESSAGE));
        }
      } catch (Exception ignored) {
      }
    }
    sendEventEmitter(reactContext, eventName, mapData);
  }

  private void handleThreatPolicy(Intent intent, String eventName) {
    WritableMap mapData = Arguments.createMap();
    WritableArray arrayResponses = getArrayResponses(intent);

    mapData.putArray("responses", arrayResponses);

    VGThreatPolicy highestResponse;
    if(intent.hasExtra(VGUARD_HIGHEST_THREAT_POLICY)){
      highestResponse = (VGThreatPolicy) intent.getSerializableExtra(VGUARD_HIGHEST_THREAT_POLICY);
    } else {
      highestResponse = highestThreatPolicy;
      highestThreatPolicy = null;
    }
    String highestResponseString = convertThreatPolicyToString(highestResponse);
    mapData.putString(VGUARD_HIGHEST_THREAT_POLICY, highestResponseString);

    String alertTitle = "";
    if(intent.hasExtra(VGUARD_ALERT_TITLE)) {
      alertTitle = intent.getStringExtra(VGUARD_ALERT_TITLE);
      mapData.putString(VGUARD_ALERT_TITLE, alertTitle);
    }
    String alertMsg = "";
    if(intent.hasExtra(VGUARD_ALERT_MESSAGE)) {
      alertMsg = intent.getStringExtra(VGUARD_ALERT_MESSAGE);
      mapData.putString(VGUARD_ALERT_MESSAGE, alertMsg);
    }
    long disabledAppExpired = intent.getLongExtra(VGUARD_DISABLED_APP_EXPIRED, 0);
    mapData.putInt(VGUARD_DISABLED_APP_EXPIRED, (int) disabledAppExpired);

    StringBuilder builder = new StringBuilder();
    if (highestResponse != BY_PASS) {
      builder.append("highest policy: ").append(highestResponseString).append("\n");
    }
    if (!TextUtils.isEmpty(alertTitle)) {
      builder.append("alertTitle: ").append(alertTitle).append("\n");
    }
    if (!TextUtils.isEmpty(alertMsg)) {
      builder.append("alertMsg: ").append(alertMsg).append("\n");
    }
    if (disabledAppExpired > 0) {
      SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      String activeDate = format.format(new Date(disabledAppExpired));
      builder.append("App can use again after: ").append(activeDate).append("\n");
    }

    sendEventEmitter(reactContext, eventName, mapData);

    if(highestResponse == QUIT_APP || highestResponse == DISABLE_APP) {
      sdkShowedPopup = true;
    }
  }

  private WritableArray getArrayResponses(Intent intent) {
    ArrayList<VGThreatResponse> threatResponses = intent.getParcelableArrayListExtra(SCAN_COMPLETE_RESULT);
    StringBuilder builder = new StringBuilder();
    WritableArray arrayData = Arguments.createArray();
    if(threatResponses != null) {
      for (VGThreatResponse response : threatResponses) {
        WritableMap infoMap = Arguments.createMap();
        infoMap.putString("title", response.getTitle());
        infoMap.putString("message", response.getMessage());
        infoMap.putString("categoryName", response.getCategoryName());
        infoMap.putString("categoryValue", response.getCategoryValue());
        infoMap.putString("threatPolicy", convertThreatPolicyToString(response.getThreatPolicy()));
        if(highestThreatPolicy  == null) {
          highestThreatPolicy = response.getThreatPolicy();
        }
        if(dialogTitle.isEmpty()){
          dialogTitle = response.getTitle();
        }
        if(dialogMessage.isEmpty()){
          dialogMessage = response.getFormattedMessage();
        }
        if(!dialogThreats.contains(response)){
          dialogThreats.add(response);
        }
        arrayData.pushMap(infoMap);

        // print log
        String infoStr = ((VGThreatResponse) response).toString();
        builder.append(infoStr).append("\n");
      }
    }
    return arrayData;
  }

  private String convertThreatPolicyToString(VGThreatPolicy highestResponse) {
    if(highestResponse != null) {
      switch(highestResponse) {
        case BY_PASS: {
          return "BY_PASS";
        }
        case ALERT_USER: {
          return "ALERT_USER";
        }
        case QUIT_APP: {
          return "QUIT_APP";
        }
        case DISABLE_APP: {
          return "DISABLE_APP";
        }
        case BLOCK_NETWORK:{
          return "BLOCK_NETWORK";
        }
      }
    }
    return "";
  }

  private static void showPopup(Activity activity, String alertTitle, String alertMsg,String alertButtonText, VGThreatPolicy highestResponse){
    Intent intent1 = new Intent(reactContext, VGDialogActivity.class);
    intent1.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
    Bundle bundle = new Bundle();
    bundle.putString("title", alertTitle);
    bundle.putString("message", alertMsg);
    bundle.putString("mButtonText", alertButtonText);
    int mode = convertModeToInt(highestResponse);
    bundle.putInt("mode", mode);
    bundle.putInt("actualMode", mode);
    intent1.putExtras(bundle);
    activity.startActivity(intent1);
  }

  private static int convertModeToInt(VGThreatPolicy highestResponse) {
    switch (highestResponse) {
      case ALERT_USER: return 1;
      case QUIT_APP: return 2;
      case DISABLE_APP: return 3;
    }
    return 0;
  }

  private static void showNewPopup(Activity activity, ArrayList<VGThreatResponse> threatList, VGThreatPolicy highestResponse){
    Intent intent1 = new Intent(reactContext, VGFullScreenDialogActivity.class );
    intent1.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
    Bundle bundle = new Bundle();
    bundle.putParcelableArrayList(VGuardBroadcastReceiver.VGUARD_THREAT_RESPONSE_LIST, threatList);
    bundle.putSerializable(VGUARD_HIGHEST_THREAT_POLICY, highestResponse);

    intent1.putExtras(bundle);
    activity.startActivity(intent1);
  }

  /**
   * React Methods
   ***/

  @ReactMethod
  public void requestScan() {
    if (vGuardMgr != null) {
      resetValueOfDialog();
      vGuardMgr.requestScan();
    }
  }

  private void resetValueOfDialog() {
    dialogThreats.clear();
    highestThreatPolicy = null;
    dialogTitle = "";
    dialogMessage = "";
  }

  // public void setVGExceptionHandler(VGExceptionHandler exHandler)

  // vtap requirements
  // I do not even know why we need to expose these methods for vtap
  // if it's public to vtap it's public for everyone
  @ReactMethod
  // public int getCustomerId() {
  public void getCustomerID(Promise promise) {
    int customerId = ERROR;
    if (vGuardMgr != null) {
      customerId = vGuardMgr.getCustomerId();
    }
    if (promise != null) {
      promise.resolve(Integer.toString(customerId));
    }
  }

  @ReactMethod
  public void getPassword(Promise promise) {
    String password = null;
    if (vGuardMgr != null) {
      byte[] data = vGuardMgr.getPassword();
      password = Utility.encodeHex(data);
    }
    if (promise != null) {
      promise.resolve(password);
    }
  }

  @ReactMethod
  public void lockVos(Promise promise) {
    int result = ERROR;
    if (vGuardMgr != null) {
      result = vGuardMgr.lockVos();
    }
    if (promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void encryptUsingCustomerKey(String plaintext, Promise promise) {
    String result = null;
    if (vGuardMgr != null) {
      byte[] bytes = plaintext.getBytes();
      byte[] encrypt = vGuardMgr.encryptUsingCustomerKey(bytes);
      result = Base64.encodeToString(encrypt,0);
    }
    if (promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void decryptUsingCustomerKey(String ciphertext, Promise promise) {
    String result = null;
    if (vGuardMgr != null) {
      byte[] bytes = Base64.decode(ciphertext, 0);
      byte[] decrypt = vGuardMgr.decryptUsingCustomerKey(bytes);
      result = new String(decrypt);
    }
    if (promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void getTroubleshootingId(Promise promise) {
    String troubleshootingId = null;
    if (vGuardMgr != null) {
      troubleshootingId = vGuardMgr.getTroubleshootingId();
    }
    if (promise != null) {
      promise.resolve(troubleshootingId);
    }
  }

  @ReactMethod
  public void getIsVosStarted(Promise promise) {
    boolean result = false;
    if (vGuardMgr != null) {
      result = vGuardMgr.getIsVosStarted();
    }
    if (promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void sdkVersion(Promise promise) {
    String result = "";
    if (vGuardMgr != null) {
      result = vGuardMgr.sdkVersion();
    }
    if (promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void getFirmwareVersion(Promise promise) {
    String result = VosWrapper.getInstance(reactContext).getFirmwareVersion();
    if (promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void getProcessorVersion(Promise promise) {
    String result = VosWrapper.getInstance(reactContext).getProcessorVersion();
    if (promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void isVosRunning(Promise promise) {
    int res = VosWrapper.getInstance(reactContext).execute(() -> {

    });
    if (promise != null) {
      promise.resolve(res > 0);
    }
  }

  public static String bytesToHex(byte[] data) {
    if (data == null) {
      return null;
    }
    StringBuilder str = new StringBuilder();
      for (byte datum : data) {
          if ((datum & 0xFF) < 16)
              str.append("0").append(Integer.toHexString(datum & 0xFF));
          else
              str.append(Integer.toHexString(datum & 0xFF));
      }
    return str.toString();
  }

  @Override
  public void triggerRestartVguard() {
    // restart vguard again
    invokeVGuard();
  }

  private void initializeCryptoTA(Context ctx) {
    CryptoTaImpl.cryptotaCallback = VGuardPlugin.this;
    CryptoTaImpl.loadCryptoTA(ctx);
  }

  @ReactMethod
  public void signMsg(String data, Promise promise) {
    if (vosFirmwareCode < VOS_INIT) {
      promise.resolve(Long.toString(vosFirmwareCode));
    } else {
      String signData = CryptoTaImpl.signMsg(reactContext, data);
      if (promise != null) {
        promise.resolve(signData);
      }
    }
  }

  @ReactMethod
  public void signMsgChecksum(String data, Promise promise) {
    if (vosFirmwareCode < VOS_INIT) {
      try {
        JSONObject errorObject = new JSONObject();
        errorObject.put("error", (Long.toString(vosFirmwareCode)));
        promise.resolve(errorObject.toString());
      } catch (JSONException e) {
        JSONObject errorObject = new JSONObject();
        promise.resolve(errorObject.toString());
      }
    } else {
      JSONObject signData = CryptoTaImpl.signMsgChecksum(reactContext, data);
      if (promise != null) {
        promise.resolve(signData.toString());
      }
    }
  }

  @ReactMethod
  public void setTaAlias(String alias) {
    CryptoTaImpl.TA_ALIAS = alias;
  }

  @ReactMethod
  public void setVirtualTapDetectionEnabled(boolean virtualTapDetectionEnabled) {
    isVirtualTapDetectionEnabled = virtualTapDetectionEnabled;
  }

  private void quitApp() {
      ActivityManager am = (ActivityManager) reactContext.getSystemService(Context.ACTIVITY_SERVICE);
      List<ActivityManager.AppTask> appTaskList = am.getAppTasks();
      if (appTaskList != null && !appTaskList.isEmpty()) {
        ActivityManager.AppTask appTask = appTaskList.get(0);
        appTask.finishAndRemoveTask();
      }
    try {
      android.os.Process.killProcess(android.os.Process.myPid());
      System.exit(0);
    } catch (Exception e) {
      android.os.Process.killProcess(android.os.Process.myPid());
      System.exit(0);
    }
  }

  private static String getTroubleshootingId() {
    if(vGuardMgr != null) {
      return vGuardMgr.getTroubleshootingId();
    }
    return "";
  }
}