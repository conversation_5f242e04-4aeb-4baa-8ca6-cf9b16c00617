package com.vkey.vguard;

import androidx.annotation.NonNull;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

public class VKCryptoTaPlugin extends ReactContextBaseJavaModule {
    private static final String TAG = VKCryptoTaPlugin.class.getName();
    private static ReactApplicationContext reactContext;

    public VKCryptoTaPlugin(ReactApplicationContext reactContext) {
        super(reactContext);
        VKCryptoTaPlugin.reactContext = reactContext;
    }

    @NonNull
    @Override
    public String getName() {
        return "VKCryptoTaPlugin";
    }

    @ReactMethod
    public void unloadCryptoTA() {
        CryptoTaImpl.unloadCryptoTA();
    }

    @ReactMethod
    public int loadCryptoTA() {
        return CryptoTaImpl.loadCryptoTA(reactContext);
    }

    @ReactMethod
    public String signMsg(String data) {
        return CryptoTaImpl.signMsg(reactContext, data);
    }
}
