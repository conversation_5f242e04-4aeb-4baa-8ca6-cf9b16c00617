//
//  VkeyCryptoTA.h
//  TestApp_Strong_78
//
//  Created by <PERSON><PERSON><PERSON> (Le) on 28/11/2023.
//  Copyright © 2023 V-Key Ptd Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN
@protocol CryptoTaCallback <NSObject>
- (void)triggerRestartVguard;
@end

@interface VkeyCryptoTA : NSObject
@property (unsafe_unretained, nonatomic) id<CryptoTaCallback> delegate;
@property (strong, nonatomic) NSString* kTA_ALIAS;
+ (instancetype) sharedManager;
- (void)loadCryptoTA;
- (NSString*) signMsg:(NSString*) message;
- (NSString*) signMsgChecksum:(NSString*) message;

@end

NS_ASSUME_NONNULL_END
