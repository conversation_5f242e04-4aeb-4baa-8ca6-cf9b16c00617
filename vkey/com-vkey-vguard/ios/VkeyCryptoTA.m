//
//  VkeyCryptoTA.m
//  TestApp_Strong_78
//
//  Created by <PERSON><PERSON><PERSON> (Le) on 28/11/2023.
//  Copyright © 2023 V-Key Ptd Ltd. All rights reserved.
//

#import "VkeyCryptoTA.h"
#import <cryptota/taInterface.h>
#import <VosWrapper/VosWrapper.h>
#import <VGuard/VGuard.h>

@implementation VkeyCryptoTA  {
    int sharedProcessManifest;
}

+ (instancetype)sharedManager {
    static VkeyCryptoTA *sharedMyManager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedMyManager = [[self alloc] init];
        sharedMyManager.kTA_ALIAS = @"";
    });
    return sharedMyManager;
}

- (NSString*) signMsg:(NSString*) message {
    int execRet = [VosWrapper execute:nil];
    NSLog(@"signMsg.Execute %d", execRet);
    if (execRet < 1) {
        return @(execRet).stringValue;
    }
    
    if(self->sharedProcessManifest < 0) {
        [self loadCryptoTA];
    }
    
    NSLog(@"signMsg.processManifest %d", self->sharedProcessManifest);
    if(self->sharedProcessManifest < 0) {
        return @(self->sharedProcessManifest).stringValue;
    }

    NSData *bytes = [message dataUsingEncoding:NSUTF8StringEncoding];
    int signError;
    NSData *signedMsg = [[taInterface getInstance] signMsg:bytes withAlias:_kTA_ALIAS hash:2 error:&signError];
    
    if ((signError < 0) || (signedMsg == nil)){
        /* Error handling code */
        NSLog(@"###loadCryptoTA.signMsg ## error: %d", signError);
    }
    
    NSLog(@"###loadCryptoTA.signMsg ## signedMsg: %@", signedMsg);
    
    NSString *base64String = [signedMsg base64EncodedStringWithOptions:0];
    NSLog(@"###loadCryptoTA.signMsg ## signedMsg: %@", base64String);
    return base64String;
}

- (NSString *) signMsgChecksum:(NSString*) message {

    int execRet = [VosWrapper execute:nil];
    NSLog(@"###signMsgChecksum.Execute %d", execRet);
    if (execRet < 1) {
        NSDictionary *data = @{
            @"error": @(execRet).stringValue
        };
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:data options:0 error:&error];
        if (!jsonData) {
            NSLog(@"Error serializing JSON: %@", error);
            return [NSString stringWithFormat:@"{}"];
        }
        return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    }

    if (self->sharedProcessManifest < 0) {
        [self loadCryptoTA];
    }

    NSLog(@"###signMsgChecksum.processManifest %d", self->sharedProcessManifest);
    if (self->sharedProcessManifest < 0) {
        NSDictionary *data = @{
            @"error": @(self->sharedProcessManifest).stringValue
        };
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:data options:0 error:&error];
        if (!jsonData) {
            NSLog(@"Error serializing JSON: %@", error);
            return [NSString stringWithFormat:@"{}"];
        }
        return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    }

    NSData *messageData = [message dataUsingEncoding:NSUTF8StringEncoding];
    uint8_t epoctime[4] = {0, 0, 0, 0};
    uint8_t checksum[4] = {0, 0, 0, 0};
    int32_t signError = 0;

    NSData *signedMsg = [[taInterface getInstance] signMsgChecksum:messageData withAlias:_kTA_ALIAS epoctime:epoctime checksum:checksum hash:2 error:&signError];

    if ((signError < 0) || (signedMsg == nil)) {// case fail
        int pri_err = [[taInterface getInstance]  getPrimaryError:signError];
        int sec_err = [[taInterface getInstance]  getSecondaryError:signError];
        NSLog(@"%@",[NSString stringWithFormat:@"^Pri error: %d | Sec error: %d", pri_err, sec_err]);
        
        NSLog(@"###signMsgChecksum ## error: %d", signError);
        NSDictionary *data = @{
            @"error": @(signError).stringValue
        };
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:data options:0 error:&error];
        if (!jsonData) {
            NSLog(@"Error serializing JSON: %@", error);
            return [NSString stringWithFormat:@"{}"];
        }
        return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    } else {// case success
        NSString *signatureBase64String = [signedMsg base64EncodedStringWithOptions:0];
        NSString *epoctimeBase64String = [[NSData dataWithBytes:epoctime length:sizeof(epoctime)] base64EncodedStringWithOptions:0];
        NSString *checksumBase6String = [[NSData dataWithBytes:checksum length:sizeof(checksum)] base64EncodedStringWithOptions:0];
        // NSLog(@"###signMsgChecksum \n signatureBase64String: %@\n epoctimeDataString: %@\n checksumDataString: %@", signatureBase64String, epoctimeBase64String, checksumBase6String);
        
        NSDictionary *data = @{
            @"signature": signatureBase64String,
            @"epoctime": epoctimeBase64String,
            @"checksum": checksumBase6String
        };
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:data options:0 error:&error];
        
        if (!jsonData) {
            NSLog(@"Error serializing JSON: %@", error);
            return [NSString stringWithFormat:@"{}"];
        }
        return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    }
}

- (void)loadCryptoTA {
    int execRet = [VosWrapper execute:nil];
    NSLog(@"###loadCryptoTA.Execute %d", execRet);
    if (execRet < 1) {
        return;
    }
    
    if(self->sharedProcessManifest > 0) {
        NSLog(@"###loadCryptoTA is loaded: %d", self->sharedProcessManifest);
        return;
    }
    
    taInterface *sharedTaInterface = [taInterface getInstance];
    int loadTA = [sharedTaInterface loadTA];
    NSLog(@"###loadCryptoTA.loadTA: %d", loadTA);
    self->sharedProcessManifest = loadTA;
    if(loadTA >= 0) {
        int initialize = [sharedTaInterface initialize];
        NSLog(@"###loadCryptoTA.initialize: %d",  initialize);
        self->sharedProcessManifest = initialize;
        if(initialize >= 0) {
            int processManifest = [sharedTaInterface processManifest];
            NSLog(@"###loadCryptoTA.processManifest: %@",  @(processManifest).stringValue);
            self->sharedProcessManifest = processManifest;
            
            // Handle secondary exception
            int error2nd = [sharedTaInterface getSecondaryError:processManifest];
            if (error2nd == -5 || error2nd == -4 || error2nd == -3) {
                // clear ta & re-processManifest
                [sharedTaInterface clearCryptoTa];
                self->sharedProcessManifest = [sharedTaInterface processManifest];
            }
            else if (error2nd == -6) {
                // clear ta & re-processManifest
                [sharedTaInterface clearCryptoTa];
                [sharedTaInterface unloadTA];
                [self loadCryptoTA];
                return;
            }
            else if (error2nd == -180) {
                // clear vos storage & then restart vguard/vos/ta
                [self clearStorageAndRestartVguard];
                return;
            }
        }
        
    }
    if (self->sharedProcessManifest == -5008 || self->sharedProcessManifest == -5009 || self->sharedProcessManifest == -5007) {
        // clear vos storage & then restart vguard/vos/ta
        [self clearStorageAndRestartVguard];
    }
    else if (self->sharedProcessManifest == -5107) {
        // Unload TA & Reset setupSigning
        [sharedTaInterface unloadTA];
        [self loadCryptoTA];
    }
}

- (void) cleanVKAssets {
    [[taInterface getInstance] unloadTA];
    VGuardManager *vgManager = [VGuardManager sharedVGuardManager];
    if(vgManager != nil) {
        [vgManager resetVOSTrustedStorage];
    
//        if ([vgManager respondsToSelector:@selector(destroy)]) {
//            [vgManager destroy];
//        } else {
//            NSLog(@"destroy: API not supported");
//        }
    }
}

- (void) clearStorageAndRestartVguard {
    // clear vos storage
    [self cleanVKAssets];
    // TODO: restart vguard
    if(self.delegate != nil) {
        [self.delegate triggerRestartVguard];
    }
}
@end
