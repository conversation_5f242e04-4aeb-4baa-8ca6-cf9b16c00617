<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64_armv7/VGuard.framework/Headers/NSUserDefaults.h</key>
		<data>
		pdS8MqGbpNRaANrUCsfwsglnAjo=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGConstant.h</key>
		<data>
		U/dYFqOs/VHXlZGJFDvjw/ETzgM=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGuard.h</key>
		<data>
		fJKMYoAVCj/4PVATtYlhMve/TjY=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGuardExceptionHandler.h</key>
		<data>
		STogGBI2fnUrsMXzOgvI0Cnfiq8=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGuardManager.h</key>
		<data>
		1xJJagZe8RRUiCGeR/z/SfX0SBA=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGuardThreats.h</key>
		<data>
		L9x7ULR/MF/LKBfiM2IfrdVGhUg=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VKeySecureKeypad.h</key>
		<data>
		DbgX6q+h4AgpO3o56nLei6KcqRY=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/IPS/VGuardDetectionManager.h</key>
		<data>
		IVAaS7xAqYAMfDkB082Jqs9ltqo=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/IPS/VGuardDetectionManager.m</key>
		<data>
		KcFh9EgkmyE0DoP75froSnerT34=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/IPS/VGuardIPSModule.h</key>
		<data>
		A4bzgsShEYEq7Q/nK8xJjg7/nCk=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/IPS/VGuardIPSModule.m</key>
		<data>
		iwIgbM1R2r0RWmYzlNBbl10qHQc=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/Info.plist</key>
		<data>
		yMeEjwWFtOrImx8D0mN9OHcSaOo=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/Modules/module.modulemap</key>
		<data>
		J0n7SrSf6NrJYzc+UFl3PjrEvyA=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/PrivacyInfo.xcprivacy</key>
		<data>
		doi9LJCWvrold7arjSlypDp7efw=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/VGuard</key>
		<data>
		NgdSWxVmo3wSmnfO4ecp+8hcHVc=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/VGuard_Info.plist</key>
		<data>
		3ubnUkUfBjHoIBxtVYNtaCiR1Yk=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeDirectory</key>
		<data>
		LbAEwd2GVO3MDoCFo1+moh7WnKY=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeRequirements</key>
		<data>
		SWUMFGibgWcSsLKuf0ecY/l8nNE=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeRequirements-1</key>
		<data>
		r0/0MZfnCCVPDjtUZRSs8851dHQ=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeResources</key>
		<data>
		nT/qdPVJCzjltF1CXdxiulhCth8=
		</data>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeSignature</key>
		<data>
		0twFwYtfKePVnrVYGOEGJRinJ2w=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/NSUserDefaults.h</key>
		<data>
		pdS8MqGbpNRaANrUCsfwsglnAjo=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGConstant.h</key>
		<data>
		U/dYFqOs/VHXlZGJFDvjw/ETzgM=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGuard.h</key>
		<data>
		fJKMYoAVCj/4PVATtYlhMve/TjY=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGuardExceptionHandler.h</key>
		<data>
		STogGBI2fnUrsMXzOgvI0Cnfiq8=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGuardManager.h</key>
		<data>
		1xJJagZe8RRUiCGeR/z/SfX0SBA=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGuardThreats.h</key>
		<data>
		L9x7ULR/MF/LKBfiM2IfrdVGhUg=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VKeySecureKeypad.h</key>
		<data>
		DbgX6q+h4AgpO3o56nLei6KcqRY=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/IPS/VGuardDetectionManager.h</key>
		<data>
		IVAaS7xAqYAMfDkB082Jqs9ltqo=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/IPS/VGuardDetectionManager.m</key>
		<data>
		KcFh9EgkmyE0DoP75froSnerT34=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/IPS/VGuardIPSModule.h</key>
		<data>
		A4bzgsShEYEq7Q/nK8xJjg7/nCk=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/IPS/VGuardIPSModule.m</key>
		<data>
		iwIgbM1R2r0RWmYzlNBbl10qHQc=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Info.plist</key>
		<data>
		U7aDs4xt+mtLXf0UixdIyPsUvr8=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Modules/module.modulemap</key>
		<data>
		J0n7SrSf6NrJYzc+UFl3PjrEvyA=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/PrivacyInfo.xcprivacy</key>
		<data>
		doi9LJCWvrold7arjSlypDp7efw=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/VGuard</key>
		<data>
		NDfKQXSAmT/sNGaNo8eF3LKTAc0=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/VGuard_Info.plist</key>
		<data>
		3ubnUkUfBjHoIBxtVYNtaCiR1Yk=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeDirectory</key>
		<data>
		+evHhPzp4bVsbKMr9+zU98UTf20=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeRequirements-1</key>
		<data>
		WXMrTayWzUIhO80yXD5n3RGy8WA=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeResources</key>
		<data>
		Ix68r234xhNInHk8GYznFWrBlzM=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64_armv7/VGuard.framework/Headers/NSUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			pdS8MqGbpNRaANrUCsfwsglnAjo=
			</data>
			<key>hash2</key>
			<data>
			Tc5LZbctIp3Y56SCDHmsbc1UwN6Nu04qyPzKAzhUkmU=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGConstant.h</key>
		<dict>
			<key>hash</key>
			<data>
			U/dYFqOs/VHXlZGJFDvjw/ETzgM=
			</data>
			<key>hash2</key>
			<data>
			w7rOuqpKuxO9t2spl7TKgo8kvnX1/70vwolHd1fWyrA=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGuard.h</key>
		<dict>
			<key>hash</key>
			<data>
			fJKMYoAVCj/4PVATtYlhMve/TjY=
			</data>
			<key>hash2</key>
			<data>
			kNXtsDiFrkfbUDAQVhJ7dBUovbmU7Pszt1iS9485MWA=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGuardExceptionHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			STogGBI2fnUrsMXzOgvI0Cnfiq8=
			</data>
			<key>hash2</key>
			<data>
			uk29EmgH2VYrMZLxlaem6mmS8WqYvxB75cL+o0A0MVM=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGuardManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			1xJJagZe8RRUiCGeR/z/SfX0SBA=
			</data>
			<key>hash2</key>
			<data>
			KMC7WehcokACdz1DcCdgMzkiBmP8KLIXW6jtqUIIRDA=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VGuardThreats.h</key>
		<dict>
			<key>hash</key>
			<data>
			L9x7ULR/MF/LKBfiM2IfrdVGhUg=
			</data>
			<key>hash2</key>
			<data>
			T4iKktjco1aCW9RpBP83EASrX7eJ72ZYc78XujWH2Qs=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/Headers/VKeySecureKeypad.h</key>
		<dict>
			<key>hash</key>
			<data>
			DbgX6q+h4AgpO3o56nLei6KcqRY=
			</data>
			<key>hash2</key>
			<data>
			tBdvLT4LL0GwARbUefgcVAh8ByIEtYwOEjWMZtE6fkM=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/IPS/VGuardDetectionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			IVAaS7xAqYAMfDkB082Jqs9ltqo=
			</data>
			<key>hash2</key>
			<data>
			thY8IZX0DTDX7FaMwujbAEYAplC/DfG27yPeVtMCG/A=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/IPS/VGuardDetectionManager.m</key>
		<dict>
			<key>hash</key>
			<data>
			KcFh9EgkmyE0DoP75froSnerT34=
			</data>
			<key>hash2</key>
			<data>
			bRdF9fUc4W5qFeEB02UvGGXSll8gVEvVbFletaHryLk=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/IPS/VGuardIPSModule.h</key>
		<dict>
			<key>hash</key>
			<data>
			A4bzgsShEYEq7Q/nK8xJjg7/nCk=
			</data>
			<key>hash2</key>
			<data>
			7KoTSTNooVBgG2ByOFu9NtotwDxRhLOFCg+JyuEmaCI=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/IPS/VGuardIPSModule.m</key>
		<dict>
			<key>hash</key>
			<data>
			iwIgbM1R2r0RWmYzlNBbl10qHQc=
			</data>
			<key>hash2</key>
			<data>
			CfY2pxJH+8ZI+zWDq4e5449R9hKeZjrpsEO6Jyzdbl4=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			yMeEjwWFtOrImx8D0mN9OHcSaOo=
			</data>
			<key>hash2</key>
			<data>
			KfD7VvwL1qO2daovFD0qFLESgcibe8XZYxze3fwIFPM=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			J0n7SrSf6NrJYzc+UFl3PjrEvyA=
			</data>
			<key>hash2</key>
			<data>
			FXa4Mfk01FM+RKSsD7cwdtv1bBu6f1EWmElqQBiJ79Q=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			doi9LJCWvrold7arjSlypDp7efw=
			</data>
			<key>hash2</key>
			<data>
			edvKYjWggMzYW0aTphpyuZlpVPEcq8+HdIUdCwMTIw8=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/VGuard</key>
		<dict>
			<key>hash</key>
			<data>
			NgdSWxVmo3wSmnfO4ecp+8hcHVc=
			</data>
			<key>hash2</key>
			<data>
			vbSMBhP1GWnu2f9hPw8osISuhG1PSf2/jhbGGMtoqmo=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/VGuard_Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			3ubnUkUfBjHoIBxtVYNtaCiR1Yk=
			</data>
			<key>hash2</key>
			<data>
			4pB4j/83AoKKDKaxMVFY5xWabnqiQNGsEioIU58Tspk=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			LbAEwd2GVO3MDoCFo1+moh7WnKY=
			</data>
			<key>hash2</key>
			<data>
			EyW2UnznqEmFX/3K+e5UsPA3gatN5V3zoz+Y5RC6Gu0=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			SWUMFGibgWcSsLKuf0ecY/l8nNE=
			</data>
			<key>hash2</key>
			<data>
			5mObI9kENadAOF5EqgGcI1ROiVeXTmkom5WoHiLolAo=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			r0/0MZfnCCVPDjtUZRSs8851dHQ=
			</data>
			<key>hash2</key>
			<data>
			OWlsEoZH6ftQrsW7klcOc+KiYBDNnH/k4NQMu7rsivE=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			nT/qdPVJCzjltF1CXdxiulhCth8=
			</data>
			<key>hash2</key>
			<data>
			nJcOFY4nlxlZN5HhHZWAoBo1HTqM2uW05obmvOhmFjs=
			</data>
		</dict>
		<key>ios-arm64_armv7/VGuard.framework/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			0twFwYtfKePVnrVYGOEGJRinJ2w=
			</data>
			<key>hash2</key>
			<data>
			d2s1Ot1coNdhq5iWLqmdF9tXHDsxblMUpUQa1lHdFS8=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/NSUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			pdS8MqGbpNRaANrUCsfwsglnAjo=
			</data>
			<key>hash2</key>
			<data>
			Tc5LZbctIp3Y56SCDHmsbc1UwN6Nu04qyPzKAzhUkmU=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGConstant.h</key>
		<dict>
			<key>hash</key>
			<data>
			U/dYFqOs/VHXlZGJFDvjw/ETzgM=
			</data>
			<key>hash2</key>
			<data>
			w7rOuqpKuxO9t2spl7TKgo8kvnX1/70vwolHd1fWyrA=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGuard.h</key>
		<dict>
			<key>hash</key>
			<data>
			fJKMYoAVCj/4PVATtYlhMve/TjY=
			</data>
			<key>hash2</key>
			<data>
			kNXtsDiFrkfbUDAQVhJ7dBUovbmU7Pszt1iS9485MWA=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGuardExceptionHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			STogGBI2fnUrsMXzOgvI0Cnfiq8=
			</data>
			<key>hash2</key>
			<data>
			uk29EmgH2VYrMZLxlaem6mmS8WqYvxB75cL+o0A0MVM=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGuardManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			1xJJagZe8RRUiCGeR/z/SfX0SBA=
			</data>
			<key>hash2</key>
			<data>
			KMC7WehcokACdz1DcCdgMzkiBmP8KLIXW6jtqUIIRDA=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VGuardThreats.h</key>
		<dict>
			<key>hash</key>
			<data>
			L9x7ULR/MF/LKBfiM2IfrdVGhUg=
			</data>
			<key>hash2</key>
			<data>
			T4iKktjco1aCW9RpBP83EASrX7eJ72ZYc78XujWH2Qs=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Headers/VKeySecureKeypad.h</key>
		<dict>
			<key>hash</key>
			<data>
			DbgX6q+h4AgpO3o56nLei6KcqRY=
			</data>
			<key>hash2</key>
			<data>
			tBdvLT4LL0GwARbUefgcVAh8ByIEtYwOEjWMZtE6fkM=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/IPS/VGuardDetectionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			IVAaS7xAqYAMfDkB082Jqs9ltqo=
			</data>
			<key>hash2</key>
			<data>
			thY8IZX0DTDX7FaMwujbAEYAplC/DfG27yPeVtMCG/A=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/IPS/VGuardDetectionManager.m</key>
		<dict>
			<key>hash</key>
			<data>
			KcFh9EgkmyE0DoP75froSnerT34=
			</data>
			<key>hash2</key>
			<data>
			bRdF9fUc4W5qFeEB02UvGGXSll8gVEvVbFletaHryLk=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/IPS/VGuardIPSModule.h</key>
		<dict>
			<key>hash</key>
			<data>
			A4bzgsShEYEq7Q/nK8xJjg7/nCk=
			</data>
			<key>hash2</key>
			<data>
			7KoTSTNooVBgG2ByOFu9NtotwDxRhLOFCg+JyuEmaCI=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/IPS/VGuardIPSModule.m</key>
		<dict>
			<key>hash</key>
			<data>
			iwIgbM1R2r0RWmYzlNBbl10qHQc=
			</data>
			<key>hash2</key>
			<data>
			CfY2pxJH+8ZI+zWDq4e5449R9hKeZjrpsEO6Jyzdbl4=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			U7aDs4xt+mtLXf0UixdIyPsUvr8=
			</data>
			<key>hash2</key>
			<data>
			DARy1mFOREPRqreWB9KvI7JsUJDS8VBH9uB2mvI/DHU=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			J0n7SrSf6NrJYzc+UFl3PjrEvyA=
			</data>
			<key>hash2</key>
			<data>
			FXa4Mfk01FM+RKSsD7cwdtv1bBu6f1EWmElqQBiJ79Q=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			doi9LJCWvrold7arjSlypDp7efw=
			</data>
			<key>hash2</key>
			<data>
			edvKYjWggMzYW0aTphpyuZlpVPEcq8+HdIUdCwMTIw8=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/VGuard</key>
		<dict>
			<key>hash</key>
			<data>
			NDfKQXSAmT/sNGaNo8eF3LKTAc0=
			</data>
			<key>hash2</key>
			<data>
			SKYdwdtyaBMMGEAz2V3e8IE+ZJ6pl5SllqzSnx1onJI=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/VGuard_Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			3ubnUkUfBjHoIBxtVYNtaCiR1Yk=
			</data>
			<key>hash2</key>
			<data>
			4pB4j/83AoKKDKaxMVFY5xWabnqiQNGsEioIU58Tspk=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			+evHhPzp4bVsbKMr9+zU98UTf20=
			</data>
			<key>hash2</key>
			<data>
			rYddtP6QMPm23EcQUeQ2FOe80lGfhVFJ+LTU3+mmacI=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			OnX22wWFKRSOFN1+obRynMCeyXM=
			</data>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			WXMrTayWzUIhO80yXD5n3RGy8WA=
			</data>
			<key>hash2</key>
			<data>
			qu2hacMYO32binhsk5fnZJ5SMv7bijF9xTQmuyZfNDQ=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			Ix68r234xhNInHk8GYznFWrBlzM=
			</data>
			<key>hash2</key>
			<data>
			59HN56zA96nGR6UfQbKuBkGtwT01F+p/SCF94B5VPz8=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VGuard.framework/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
