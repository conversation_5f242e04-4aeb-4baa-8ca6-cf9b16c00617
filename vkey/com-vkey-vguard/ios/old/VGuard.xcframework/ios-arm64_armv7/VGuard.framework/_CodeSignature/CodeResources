<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/NSUserDefaults.h</key>
		<data>
		pdS8MqGbpNRaANrUCsfwsglnAjo=
		</data>
		<key>Headers/VGConstant.h</key>
		<data>
		U/dYFqOs/VHXlZGJFDvjw/ETzgM=
		</data>
		<key>Headers/VGuard.h</key>
		<data>
		fJKMYoAVCj/4PVATtYlhMve/TjY=
		</data>
		<key>Headers/VGuardExceptionHandler.h</key>
		<data>
		STogGBI2fnUrsMXzOgvI0Cnfiq8=
		</data>
		<key>Headers/VGuardManager.h</key>
		<data>
		1xJJagZe8RRUiCGeR/z/SfX0SBA=
		</data>
		<key>Headers/VGuardThreats.h</key>
		<data>
		L9x7ULR/MF/LKBfiM2IfrdVGhUg=
		</data>
		<key>Headers/VKeySecureKeypad.h</key>
		<data>
		DbgX6q+h4AgpO3o56nLei6KcqRY=
		</data>
		<key>IPS/VGuardDetectionManager.h</key>
		<data>
		IVAaS7xAqYAMfDkB082Jqs9ltqo=
		</data>
		<key>IPS/VGuardDetectionManager.m</key>
		<data>
		KcFh9EgkmyE0DoP75froSnerT34=
		</data>
		<key>IPS/VGuardIPSModule.h</key>
		<data>
		A4bzgsShEYEq7Q/nK8xJjg7/nCk=
		</data>
		<key>IPS/VGuardIPSModule.m</key>
		<data>
		iwIgbM1R2r0RWmYzlNBbl10qHQc=
		</data>
		<key>Info.plist</key>
		<data>
		yMeEjwWFtOrImx8D0mN9OHcSaOo=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		J0n7SrSf6NrJYzc+UFl3PjrEvyA=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		doi9LJCWvrold7arjSlypDp7efw=
		</data>
		<key>VGuard_Info.plist</key>
		<data>
		3ubnUkUfBjHoIBxtVYNtaCiR1Yk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/NSUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			pdS8MqGbpNRaANrUCsfwsglnAjo=
			</data>
			<key>hash2</key>
			<data>
			Tc5LZbctIp3Y56SCDHmsbc1UwN6Nu04qyPzKAzhUkmU=
			</data>
		</dict>
		<key>Headers/VGConstant.h</key>
		<dict>
			<key>hash</key>
			<data>
			U/dYFqOs/VHXlZGJFDvjw/ETzgM=
			</data>
			<key>hash2</key>
			<data>
			w7rOuqpKuxO9t2spl7TKgo8kvnX1/70vwolHd1fWyrA=
			</data>
		</dict>
		<key>Headers/VGuard.h</key>
		<dict>
			<key>hash</key>
			<data>
			fJKMYoAVCj/4PVATtYlhMve/TjY=
			</data>
			<key>hash2</key>
			<data>
			kNXtsDiFrkfbUDAQVhJ7dBUovbmU7Pszt1iS9485MWA=
			</data>
		</dict>
		<key>Headers/VGuardExceptionHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			STogGBI2fnUrsMXzOgvI0Cnfiq8=
			</data>
			<key>hash2</key>
			<data>
			uk29EmgH2VYrMZLxlaem6mmS8WqYvxB75cL+o0A0MVM=
			</data>
		</dict>
		<key>Headers/VGuardManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			1xJJagZe8RRUiCGeR/z/SfX0SBA=
			</data>
			<key>hash2</key>
			<data>
			KMC7WehcokACdz1DcCdgMzkiBmP8KLIXW6jtqUIIRDA=
			</data>
		</dict>
		<key>Headers/VGuardThreats.h</key>
		<dict>
			<key>hash</key>
			<data>
			L9x7ULR/MF/LKBfiM2IfrdVGhUg=
			</data>
			<key>hash2</key>
			<data>
			T4iKktjco1aCW9RpBP83EASrX7eJ72ZYc78XujWH2Qs=
			</data>
		</dict>
		<key>Headers/VKeySecureKeypad.h</key>
		<dict>
			<key>hash</key>
			<data>
			DbgX6q+h4AgpO3o56nLei6KcqRY=
			</data>
			<key>hash2</key>
			<data>
			tBdvLT4LL0GwARbUefgcVAh8ByIEtYwOEjWMZtE6fkM=
			</data>
		</dict>
		<key>IPS/VGuardDetectionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			IVAaS7xAqYAMfDkB082Jqs9ltqo=
			</data>
			<key>hash2</key>
			<data>
			thY8IZX0DTDX7FaMwujbAEYAplC/DfG27yPeVtMCG/A=
			</data>
		</dict>
		<key>IPS/VGuardDetectionManager.m</key>
		<dict>
			<key>hash</key>
			<data>
			KcFh9EgkmyE0DoP75froSnerT34=
			</data>
			<key>hash2</key>
			<data>
			bRdF9fUc4W5qFeEB02UvGGXSll8gVEvVbFletaHryLk=
			</data>
		</dict>
		<key>IPS/VGuardIPSModule.h</key>
		<dict>
			<key>hash</key>
			<data>
			A4bzgsShEYEq7Q/nK8xJjg7/nCk=
			</data>
			<key>hash2</key>
			<data>
			7KoTSTNooVBgG2ByOFu9NtotwDxRhLOFCg+JyuEmaCI=
			</data>
		</dict>
		<key>IPS/VGuardIPSModule.m</key>
		<dict>
			<key>hash</key>
			<data>
			iwIgbM1R2r0RWmYzlNBbl10qHQc=
			</data>
			<key>hash2</key>
			<data>
			CfY2pxJH+8ZI+zWDq4e5449R9hKeZjrpsEO6Jyzdbl4=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			J0n7SrSf6NrJYzc+UFl3PjrEvyA=
			</data>
			<key>hash2</key>
			<data>
			FXa4Mfk01FM+RKSsD7cwdtv1bBu6f1EWmElqQBiJ79Q=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			doi9LJCWvrold7arjSlypDp7efw=
			</data>
			<key>hash2</key>
			<data>
			edvKYjWggMzYW0aTphpyuZlpVPEcq8+HdIUdCwMTIw8=
			</data>
		</dict>
		<key>VGuard_Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			3ubnUkUfBjHoIBxtVYNtaCiR1Yk=
			</data>
			<key>hash2</key>
			<data>
			4pB4j/83AoKKDKaxMVFY5xWabnqiQNGsEioIU58Tspk=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
