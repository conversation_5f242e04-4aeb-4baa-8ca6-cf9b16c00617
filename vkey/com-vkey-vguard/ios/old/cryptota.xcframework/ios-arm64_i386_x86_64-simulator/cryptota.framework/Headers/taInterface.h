//
//  taInterface.h
//  taInterface
//
//  Created by <PERSON><PERSON><PERSON> on 27/5/18.
//  Copyright © 2018 V-Key. All rights reserved.
//
#ifndef TAINTERFACE_H
#define TAINTERFACE_H

#import <Foundation/Foundation.h>

typedef unsigned char byte;
enum {
    CRYPTOTA_NO_DIGEST          =   0,
    CRYPTOTA_SHA1               =   1,
    CRYPTOTA_SHA256             =   2,
    CRYPTOTA_SYMKEYSRC_MANIFEST =   0,
};

#define ISO_TAG 4
#define VA_TAG  4

@interface taInterface : NSObject
-(instancetype)init;

/**
 * Get an instance of the taInterface.
 */
+ (id)getInstance;

/**
 * Load this Trusted Application
 */
- (int)loadTA;

/**
 * Unload this Trusted Application
 */
- (int)unloadTA;

/**
 * Initialize this Trusted Application
 */
- (int)initialize;

- (int)getTrustedTime;

- (NSString *)getVersion;

// - (NSData *)getSignerCert;

// - (NSString *)getSignerPwd;

-(int)selfTest;

/**
 *  Process manifest file.
 *
 *  This method will only process 'manifest_<FUID>.json' file. This file must be included in
 *  the application's assets folder in order to be processed.
 *
 *  return  TA_FUNC_OK if success
 *           VK_TA_FILE_NOT_FOUND if file not found
 *           VK_TA_INVALID_FILE if unable to read file
 *           VK_TA_MEMORY_ERROR if unable to allocate memory to read file content
 */
-(int)processManifest;

/**
 * Sign 'msg' and return signature using 'alias' key stored in datastore.
 * 
 * Algorithm: RSASSA-PKCS1-v1_5
 * 
 * Example:
 *      int TRUSTED_TIME_SERVER_URL_ALREADY_SET	= -1109;
 *      int ret = [VosWrapper setTrustedTimeServerUrl: @"<URL>"];
 *      if ( (ret < 0) && (ret != TRUSTED_TIME_SERVER_URL_ALREADY_SET) )
 *          ...
 *      unsigned char epoctime[] = {0, 0, 0, 0};
 *      unsigned char checksum[] = {0, 0, 0, 0};
 *      NSData* signedMsg = [[taInterface getInstance] signMsgChecksum:<message> withAlias:@"eKYC"
 *                              epoctime:epoctime checksum:checksum hash: CRYPTOTA_SHA1 error:&ret];
 *      if ((ret < 0) ||(signedMsg == nil))
 *          ...
 
 * 
 * @param   msg     Message to sign
 * @param   alias   Alias of key to use
 * @param   epoctime    epoc time returned
 * @param   checksum    checksum returned
 * @param   hash    Hash type to use. SHA1, SHA256
 * @param   e       Error code returned. Success if positive. Failure if negative
 *                  VK_INT_BAD_ARGUMENTS, VK_TA_DATASTORE_OPEN_ERROR, VK_TA_DATASTORE_FIND_ERROR,
 *                  VK_TA_RSA_DECODE_ERROR, VK_TA_DATASTORE_CLOSE_ERROR,
 *                  VK_TA_DATASTORE_OPEN_ERROR,
 *                  VK_TA_DATASTORE_FIND_ERROR, VK_TA_DATASTORE_CLOSE_ERROR,
 *                  RSA_BAD_ENCODE_SIGNATURE, RSA_SIG_SIGN_E
 */
-(NSData*)signMsgChecksum: (NSData*)msg withAlias:(NSString*)alias epoctime:(unsigned char*)epoctime
                                checksum:(unsigned char*)checksum hash:(int)hash error:(int*)e;

/**
 * Sign 'msg' and return signature using 'alias' key stored in datastore.
 * 
 * Algorithm: RSASSA-PKCS1-v1_5
 * 
 * Example:
 *      int TRUSTED_TIME_SERVER_URL_ALREADY_SET	= -1109;
 *      int ret = [VosWrapper setTrustedTimeServerUrl: @"<URL>"];
 *      if ( (ret < 0) && (ret != TRUSTED_TIME_SERVER_URL_ALREADY_SET) )
 *          ...
 *      NSData* signedMsg = [[taInterface getInstance] signMsg:<message> withAlias:@"eKYC" hash: SHA1 error:&ret];
 *      if ((ret < 0) ||(signedMsg == nil))
 *          ...
 * 
 * param   msg     Message to sign
 * param   alias   Alias of key to use
 * param   hash    Hash type to use. SHA1, SHA256
 * param   e       Error code returned. Success if positive. Failure if negative
 *                  VK_INT_BAD_ARGUMENTS, VK_TA_DATASTORE_OPEN_ERROR, VK_TA_DATASTORE_FIND_ERROR,
 *                  VK_TA_RSA_DECODE_ERROR, VK_TA_DATASTORE_CLOSE_ERROR,
 *                  VK_TA_DATASTORE_OPEN_ERROR,
 *                  VK_TA_DATASTORE_FIND_ERROR, VK_TA_DATASTORE_CLOSE_ERROR,
 *                  RSA_BAD_ENCODE_SIGNATURE, RSA_SIG_SIGN_E
 */
-(NSData*)signMsg: (NSData*)msg withAlias:(NSString*)alias hash:(int)hash error:(int*)e;

/**
 * Decrypt 'ciphertext' with 'alias' private key stored in datastore and return decrypted plaintext.
 * 
 * Algorithm: RSAES-PKCS1-v1_5
 * 
 *  Example: 
 *      int TRUSTED_TIME_SERVER_URL_ALREADY_SET	= -1109;
 *      int ret = [VosWrapper setTrustedTimeServerUrl: @"<URL>"];
 *      if ( (ret < 0) && (ret != TRUSTED_TIME_SERVER_URL_ALREADY_SET) )
 *          ...
 *      NSData* plaintext = [[taInterface getInstance] privateDecrypt:<ciphertext> withAlias: <alias> error: &ret];
 *      if (ret < 0)
 *          ...
 *
 * param   ciphertext  Ciphertext to decrypt
 * param   alias       Alias of key to use
 * param   e           Error code returned. Success if positive. Failure if negative
 *                      VK_INT_BAD_ARGUMENTS, VK_TA_DATASTORE_OPEN_ERROR, VK_TA_DATASTORE_FIND_ERROR,
 *                      VK_TA_RSA_DECODE_ERROR, VK_TA_RSA_DECRYPTION_ERROR, VK_TA_DATASTORE_CLOSE_ERROR,
 *                      VK_TA_DATASTORE_OPEN_ERROR,
 *                      VK_TA_DATASTORE_FIND_ERROR, VK_TA_DATASTORE_CLOSE_ERROR,
 *                      BAD_FUNC_ARG, MEMORY_E, RSA_DECRYPT_E
 */
-(NSData*)privateDecrypt: (NSData*)ciphertext withAlias:(NSString*)alias error:(int*)e;

/**
 * Decrypt 'ciphertext' with 'alias' private key stored in datastore and return decrypted plaintext
 * with OAEP.
 *
 * Algorithm: RSAES-OAEP
 * 
 *  Example: 
 *      int TRUSTED_TIME_SERVER_URL_ALREADY_SET	= -1109;
 *      int ret = [VosWrapper setTrustedTimeServerUrl: @"<URL>"];
 *      if ( (ret < 0) && (ret != TRUSTED_TIME_SERVER_URL_ALREADY_SET) )
 *          ...
 *      NSData* plaintext = [[taInterface getInstance] privateDecrypt:<ciphertext> withAlias:<alias> encodeParam:<param> algo:SHA1 error:&ret];
 *      if (ret < 0)
 *          ...
 *
 * param   ciphertext  Ciphertext to decrypt
 * param   alias       Alias of key to use
 * param   param       Encoding parameters that may be empty.
 * param   algo        SHA1 or SHA256
 *                      SHA1. When this is used, the SDK uses SHA1 with MGF1(SHA1) as the hashing function.
 *                      SHA256. When this is used, the SDK uses SHA256 with MGF1(SHA256) as the hashing function.
 * param   e           Error code returned. Success if positive. Failure if negative
 *                      VK_INT_BAD_ARGUMENTS, VK_TA_DATASTORE_OPEN_ERROR, VK_TA_DATASTORE_FIND_ERROR,
 *                      VK_TA_RSA_DECODE_ERROR, VK_TA_RSA_DECRYPTION_ERROR, VK_TA_DATASTORE_CLOSE_ERROR,
 *                      VK_TA_DATASTORE_OPEN_ERROR,
 *                      VK_TA_DATASTORE_FIND_ERROR, VK_TA_DATASTORE_CLOSE_ERROR,
 *                      BAD_FUNC_ARG, MEMORY_E, RSA_DECRYPT_E
 */
-(NSData*)rsaOaepDecrypt: (NSData*)ciphertext withAlias:(NSString*)alias encodeParam:(NSData*)param algo:(int)algo error:(int*)e;

-(NSData*)aesGcmSivEncrypt: (NSData*)plaintext withAlias:(NSString*)alias keySrc:(int)keySrc iv:(NSData*)iv
    authIn:(NSData*)authIn error:(int*)e;
-(NSData*)aesGcmSivDecrypt: (NSData*)cipherAuthTagText withAlias:(NSString*)alias keySrc:(int)keySrc iv:(NSData*)iv authIn:(NSData*)authIn error:(int*)e;

- (int)clearCryptoTa;
- (int)getPrimaryError: (int) error;
- (int)getSecondaryError: (int) error;

@end

#endif
