<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/cryptotaex.h</key>
		<data>
		DCrGJBUZ6VMuE223gCQf9w+kKlM=
		</data>
		<key>Headers/taInterface.h</key>
		<data>
		HSdgdpbbGUx1RJJFS86mtqzeIGY=
		</data>
		<key>Info.plist</key>
		<data>
		+KaHe73uFWjWVFr8cfGwekVLBM8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		JUbtdSC/HgIHGFo0R2bYJcgW/wY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/cryptotaex.h</key>
		<dict>
			<key>hash</key>
			<data>
			DCrGJBUZ6VMuE223gCQf9w+kKlM=
			</data>
			<key>hash2</key>
			<data>
			+l4weNhTdkwHA+2qSL8HH2oUkHN9gCAXrMux7TG5in8=
			</data>
		</dict>
		<key>Headers/taInterface.h</key>
		<dict>
			<key>hash</key>
			<data>
			HSdgdpbbGUx1RJJFS86mtqzeIGY=
			</data>
			<key>hash2</key>
			<data>
			7USbtm08TvzRfC1FK9JB96IdtSbS0UxjldDAtfJMbuk=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			JUbtdSC/HgIHGFo0R2bYJcgW/wY=
			</data>
			<key>hash2</key>
			<data>
			gfjFxzvy6Z6Mw4Ms3LR3gDYqQf4SbPBWpixwr7MIaW4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
